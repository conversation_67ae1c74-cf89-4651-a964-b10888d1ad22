// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import App from "./App";
import router from "./router";
import store from "./store";
import VCalendar from "v-calendar"; // 引入 v-calendar
import {
  sDataGrid
} from "sd-data-grid";
import "element-ui/lib/theme-chalk/index.css";
import selectPrisonVue from '@/view/health-care/dqxd/selectPrison.vue'
// Font Awesome 图标库
import "@fortawesome/fontawesome-free/css/all.min.css";
import directives from "@/util/directive.js";
import infiniteScroll from "vue-infinite-scroll";
import fileUpload from "sd-minio-upfile";
import {
  EventBus
} from "@/event-bus";
import API from "@/api/index.js";
import BspLayout from "@/components/bsp-layout";
import {
  DynamicFormPlugin
} from "@/components/dynamic-form";
import {
  PersonnelSelectorPlugin
} from "@/components/personnel-selector";
import BspFileUploadPlugin from '@/components/bsp-upload';
import {
  verifyGlobalComponents
} from "@/components/bsp-layout/verify-global-components.js";
import {
  $Post,
  $Get,
  $Post_RP,
  $Post_Binary,
  $Delete
} from "./api/fetch";
import myComponents from "@/components/useComponent"; // 自定义基础组件
import myDialog from "@/components/myDialog"; // 自定义alert提示组件
//图片预览插件
import Viewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import {
  getUserCache,
  hasFuncPerm
} from "@/libs/util"
//谈话教育
import sip from "@/util/sip";
import videojs from "video.js";
import "video.js/dist/video-js.css"; // 引入默认样式
import zhCN from "video.js/dist/lang/zh-CN.json"; // 导入中文语言包
import "@/assets/js/rem.js";
import $ from "jquery";
import TextToSpeechPlugin from '@/assets/js/textToSpeech.js'
import {
  DatePicker,
  TimePicker,
  Button,
  Select,
  Option,
  Checkbox,
  Tree,
  Skeleton,
  SkeletonItem,
  Switch,
  Cascader,
  Progress,
  Radio,
  Tag,
  OptionGroup,
  Image,
  Steps,
  Step,
  Descriptions,
  DescriptionsItem,
  Input,
  Popover,
  Row,
  InputNumber,
  Tabs,
  TabPane,
  Menu,
  MenuItem,
  InfiniteScroll,
  Autocomplete,
  Loading,
  Collapse,
  CollapseItem,
  Carousel,
  CarouselItem
} from "element-ui";
import {
  infoModal,
  successModal,
  errorModal,
  warningModal,
  confirmModal,
  deleteModal,
  guiderModal
} from "@/libs/modal";
import ViewUI from "view-design";
import path from "@/path/path";
import dic from "sd-dic-grid";
import dicTree from "sd-dic-tree-grid";
import config from "@/config";
import "./index.less";
import vueComs from "./commonComs"; //全局引入需要在门户使用的组件
import VueCron from "vue-cron";
import RSDataGrid from "@/components/rs-datagrid/install.js"; //全局引入 RS-DataGrid 组件
import Bus from "@/libs/eventBus.js";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
// dam引入
import "@/assets/js/jquery.min.js";
// import "@ztree/ztree_v3/js/jquery.ztree.core.min.js";
// import "@ztree/ztree_v3/js/jquery.ztree.excheck.min.js";
// import "@ztree/ztree_v3/js/jquery.ztree.exhide.min.js";
// import '@ztree/ztree_v3/css/zTreeStyle/zTreeStyle.css'
// import '@ztree/ztree_v3/css/metroStyle/metroStyle.css'
import "@/assets/style/metroStyle/metroStyle.css";
import myBtn from "@/components/myButton";

import dicKit from "_c/dic/dataKit.js";

window.jQuery = $;
window.$ = $;
dayjs.extend(utc);

// 添加中文语言支持
videojs.addLanguage("zh-CN", zhCN);
// 🆕 支持ihc版本的字典组件（如果需要的话可以切换）
// import dic from 'sd-dic-grid'  // ihc版本使用的字典组件

// 实际打包时应该不引入mock
/* eslint-disable */
//if (process.env.NODE_ENV !== 'production') require('@/mock')
//require('@/mock')

Vue.use(ViewUI)
  .use(path) // 🔄 ihc版本的重复调用，确保路径组件正确注册
  .use(dic, {
    appMark: serverConfig.APP_CODE
  })
  .use(dicTree, {
    appMark: serverConfig.APP_CODE
  })
  .use(DatePicker)
  .use(TimePicker)
  .use(Button)
  .use(Checkbox)
  .use(Switch)
  .use(Progress)
  .use(Image)
  .use(Steps)
  .use(Step)
  .use(RSDataGrid)
  .use(BspLayout)
  .use(DynamicFormPlugin)
  .use(PersonnelSelectorPlugin)
  .use(BspFileUploadPlugin)
  .use(fileUpload)
  .use(VCalendar)
  .use(myBtn)
  .use(myComponents)
  .use(myDialog)
  .use(InfiniteScroll)
  .use(Autocomplete);
//全局使用图片预览和无限滚动
Vue.use(Viewer).use(infiniteScroll);
// Vue.use(selectPrisonVue)
Vue.component("selectPrisonVue", selectPrisonVue);
Vue.use(TextToSpeechPlugin);
//设置图片预览初始值
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: "data-source",
  },
});
Vue.prototype.$config = config;
Vue.prototype.serverConfig = window.serverConfig || {};
Vue.prototype.$bus = EventBus;
Vue.prototype.$dicKit = dicKit;
Vue.prototype.dayjs = dayjs;
Vue.prototype.hasFuncPerm = hasFuncPerm;
Vue.prototype.videojs = videojs;
Vue.prototype.sip = sip;
// 全局公用方法
import utils from "@/assets/js/util.js";
Vue.prototype.utils = utils;
// 全局混入，为所有组件提供 serverConfig 访问
Vue.mixin({
  data() {
    return {
      serverConfig: window.serverConfig || {},
    };
  },
});
// this.$set(serverConfig, 'menuMode', 'top')
Vue.prototype.serverConfig.menuMode = 'top'
/**
 * @description 生产环境关掉提示
 */
Vue.prototype.globalAppCode = serverConfig.APP_CODE; // 'acp';//window.serverConfig.APP_CODE//
Vue.prototype.$Modal = ViewUI.Modal;
Vue.prototype.infoModal = infoModal;
Vue.prototype.successModal = successModal;
Vue.prototype.errorModal = errorModal;
Vue.prototype.warningModal = warningModal;
Vue.prototype.confirmModal = confirmModal;
Vue.prototype.deleteModal = deleteModal;
Vue.prototype.guiderModal = guiderModal;
Vue.config.productionTip = false;
Vue.prototype.$Post = $Post;
Vue.prototype.$Get = $Get;
Vue.prototype.$Post_RP = $Post_RP;
Vue.prototype.$Post_Binary = $Post_Binary;
Vue.prototype.$Delete = $Delete;
Vue.prototype.Bus = Bus;

Vue.prototype.API = API;
Vue.component("el-select", Select);
Vue.component("el-option", Option);
Vue.component("el-tree", Tree);
Vue.component("el-skeleton", Skeleton);
Vue.component("el-cascader", Cascader);
Vue.component("el-radio", Radio);
Vue.component("el-skeleton-item", SkeletonItem);
Vue.component("el-menu", Menu);
Vue.component("el-menu-item", MenuItem);
// Vue.component('el-scrollbar', scrollbar)
Vue.component("sDataGrid", sDataGrid);
// Vue.component("el-button", Button);
Vue.component("el-tag", Tag);
Vue.component("el-option-group", OptionGroup);
Vue.component("el-descriptions", Descriptions);
Vue.component("el-descriptions-item", DescriptionsItem);
Vue.component("el-input", Input);
Vue.component("el-popover", Popover);
Vue.component("el-row", Row);
Vue.component("el-input-number", InputNumber);
Vue.component("el-tabs", Tabs);
Vue.component("el-tab-pane", TabPane);
Vue.component("el-collapse", Collapse);
Vue.component("el-collapse-item", CollapseItem);
Vue.component("el-carousel", Carousel);
Vue.component("el-carousel-item", CarouselItem);
Vue.use(directives);
Vue.use(VueCron); //使用方式：<vueCron></vueCron>
Vue.use(Loading.directive);
Vue.use(getUserCache)

vueComs();

// 在开发环境下验证 BSP 组件注册
if (process.env.NODE_ENV === "development") {
  Vue.nextTick(() => {
    verifyGlobalComponents(Vue);
  });
}

/* eslint-disable no-new */
new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
