<template>
  <img
    :class="['bsp-img-viewer', isView && 'is-view']"
    v-if="src"
    :src="src"
    ref="img"
    @click.stop="handleView"
    :alt="alt"
  />
</template>

<script>
import "viewerjs/dist/viewer.css";
import Viewer from "viewerjs";
export default {
  props: {
    // 是否支持预览
    isView: {
      type: Boolean,
      default: false,
    },
    // 属性提示
    alt: {
      type: String,
      default: "",
    },
    src: {
      type: String,
    },
  },
  data() {
    return {
      viewer: null,
    };
  },
  watch: {
    isView: {
      handler(val) {
        this.initViewTool();
      },
      immediate: true,
    },
    src: {
      handler() {
        this.initViewTool();
      },
      immediate: true,
    },
  },
  methods: {
    initViewTool() {
      if (!(this.isView && this.src)) return;
      this.$nextTick(() => {
        const imgRef = this.$refs.img;
        if (!imgRef) return;
        this.viewer = new Viewer(imgRef, {
          title: (image) => image.alt,
        });
      });
    },
    // 预览
    handleView() {
      if (!this.isView) return;
      this.viewer.update();
      this.viewer.view();
    },
  },
};
</script>
<style lang="less" scoped>
.bsp-img-viewer {
  &.is-view {
    cursor: pointer;
  }
}
</style>