<template>
  <div class="com-go-video" :id="windowUniqueId"></div>
</template>
<script>
import GoVideoWebPlayer from "./lib/GoVideoWebPlayer.js";
import { nanoid } from "nanoid";
export default {
  props: {
    initConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    getBase64: {
      type: Function,
    },
    autoPlay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      windowUniqueId: nanoid(),
      player: null,
      playList: [],
      defaultConfiguration: {
        isPlayback: false,
        wndNums: 1,
        maxSplit: 1,
        draggable: false,
        getAroundRecord: true,
        showBackgroundImage: true,
        keepLastFrame: true,
        topMenuKeys: ["Capture", "FullScreen", "Voice"],
        rightMenuKeys: [],
        timeLineMenuKeys: ["fullScreen"],
        defaultVolume: 100,
        timelineTopBlank: 0,
      },
    };
  },
  mounted() {
    this.initWindow();
    this.initService();
  },
  beforeDestroy() {
    this.destroyVideo();
  },
  methods: {
    play(playList) {
      this.playList = playList;
      this.setWndNumByList();
      this.playPlayList();
    },
    setWndNumByList() {
      let list = this.playList.map(
        (item) => item && Object.keys(item).length !== 0
      );
      let len = this.player.wndNums;
      if (list.length !== 0) {
        len = list.length === 1 ? 1 : list.length <= 4 ? 4 : 9;
      }
      if (len !== this.player.wndNums) {
        this.player.setWndNums(len, true);
      }
    },
    playPlayList() {
      if (this.autoPlay && this.initConfig.isPlayback) {
        return this.recordVideoAuto(0);
      }
      this.playList.forEach((element, index) => {
        if (this.initConfig.isPlayback) {
          this.recordVideo({ ...element, wndNo: index });
        } else {
          this.playVideo({ ...element, wndNo: index });
        }
      });
    },
    switchEnvironment() {
      return `${window.origin}/goVideo`;
    },
    onCapture(e) {
      if (this.getBase64) {
        this.getBase64(e);
      } else {
        let link = document.createElement("a");
        link.href = e;
        link.setAttribute(
          "download",
          `${new Date().Format("yyyy-MM-dd hh:mm:ss")}.png`
        );
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    initWindow() {
      let defaultConfiguration = {
        ...this.defaultConfiguration,
        ...this.initConfig,
      };
      this.player = new GoVideoWebPlayer(
        this.windowUniqueId,
        defaultConfiguration
      );
      this.player.onCapture = this.onCapture;
    },
    initService() {
      const defaultRequest = new GoVideoWebPlayer.DefaultRequest({
        ip: this.switchEnvironment(),
        useCMS: true,
        token: "",
        isARStream: 0,
        reconnect: true,
      });
      this.player.use(defaultRequest);
    },
    async recordVideo(item) {
      let {
        channelId,
        deviceName,
        storageType,
        wndNo,
        date,
        config,
        customInfo = {},
      } = item;
      let defaultConfiguration = {
        customInfo, // 自定义信息
        focusFreeWnd: false,
        focusWndByKey: false,
        wndNo,
      };
      await this.player.defaultPlayback(
        channelId,
        deviceName,
        storageType || 0,
        date,
        {
          ...defaultConfiguration,
          ...config,
        }
      );
    },
    async recordVideoAuto(idx) {
      if (idx >= this.playList.length) return;
      let item = this.playList[idx];
      if (item) {
        await this.recordVideo({ ...item, wndNo: idx });
        this.player.seekTime(item.config.seekTime || item.config.startTime);
        this.player.setVolume(100, idx);
        this.player.onVideoSetted = () => {
          let btn = document.getElementsByClassName(
            "gs-timeline-option-button gs-control-play"
          )[0];
          btn.click();
          this.player.selectWnd(idx + 1);
          this.recordVideoAuto(idx + 1);
        };
      }
    },
    playVideo(item) {
      let { channelId, deviceName, wndNo, config, customInfo = {} } = item;
      let defaultConfiguration = {
        customInfo, // 自定义信息
        focusFreeWnd: false,
        focusWndByKey: false,
        wndNo,
      };
      this.player.defaultPlay(channelId, deviceName, 1, {
        ...defaultConfiguration,
        ...config,
      });
    },
    destroyVideo() {
      this.player.destroy();
    },
  },
};
</script>

<style src="./lib/assets/index.css" scoped></style>
<style lang="less" scoped>
.com-go-video {
  width: 100%;
  height: 100%;
}
</style>
