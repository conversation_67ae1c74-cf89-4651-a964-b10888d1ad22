.gs-button-Capture-click {
    background-image: url(../source/button/Capture/click.png) !important;
}

.gs-button-Capture:hover {
    background-image: url(../source/button/Capture/hover.png);
}

.gs-button-Capture-hover {
    background-image: url(../source/button/Capture/hover.png);
}

.gs-button-Capture {
    background-image: url(../source/button/Capture/normal.png);
}

.gs-button-Close-click {
    background-image: url(../source/button/Close/click.png) !important;
}

.gs-button-Close:hover {
    background-image: url(../source/button/Close/hover.png);
}

.gs-button-Close-hover {
    background-image: url(../source/button/Close/hover.png);
}

.gs-button-Close {
    background-image: url(../source/button/Close/normal.png);
}

.gs-button-Download-click {
    background-image: url(../source/button/Download/click.png) !important;
}

.gs-button-Download:hover {
    background-image: url(../source/button/Download/hover.png);
}

.gs-button-Download-hover {
    background-image: url(../source/button/Download/hover.png);
}

.gs-button-Download {
    background-image: url(../source/button/Download/normal.png);
}

.gs-button-EighthStream-click {
    background-image: url(../source/button/EighthStream/click.png) !important;
}

.gs-button-EighthStream:hover {
    background-image: url(../source/button/EighthStream/hover.png);
}

.gs-button-EighthStream-hover {
    background-image: url(../source/button/EighthStream/hover.png);
}

.gs-button-EighthStream {
    background-image: url(../source/button/EighthStream/normal.png);
}

.gs-button-FifthStream-click {
    background-image: url(../source/button/FifthStream/click.png) !important;
}

.gs-button-FifthStream:hover {
    background-image: url(../source/button/FifthStream/hover.png);
}

.gs-button-FifthStream-hover {
    background-image: url(../source/button/FifthStream/hover.png);
}

.gs-button-FifthStream {
    background-image: url(../source/button/FifthStream/normal.png);
}

.gs-button-FourthStream-click {
    background-image: url(../source/button/FourthStream/click.png) !important;
}

.gs-button-FourthStream:hover {
    background-image: url(../source/button/FourthStream/hover.png);
}

.gs-button-FourthStream-hover {
    background-image: url(../source/button/FourthStream/hover.png);
}

.gs-button-FourthStream {
    background-image: url(../source/button/FourthStream/normal.png);
}

.gs-button-MainStream-click {
    background-image: url(../source/button/MainStream/click.png) !important;
}

.gs-button-MainStream:hover {
    background-image: url(../source/button/MainStream/hover.png);
}

.gs-button-MainStream-hover {
    background-image: url(../source/button/MainStream/hover.png);
}

.gs-button-MainStream {
    background-image: url(../source/button/MainStream/normal.png);
}

.gs-button-MultipleCapture-click {
    background-image: url(../source/button/MultipleCapture/click.png) !important;
}

.gs-button-MultipleCapture:hover {
    background-image: url(../source/button/MultipleCapture/hover.png);
}

.gs-button-MultipleCapture-hover {
    background-image: url(../source/button/MultipleCapture/hover.png);
}

.gs-button-MultipleCapture {
    background-image: url(../source/button/MultipleCapture/normal.png);
}

.gs-button-NinthStream-click {
    background-image: url(../source/button/NinthStream/click.png) !important;
}

.gs-button-NinthStream:hover {
    background-image: url(../source/button/NinthStream/hover.png);
}

.gs-button-NinthStream-hover {
    background-image: url(../source/button/NinthStream/hover.png);
}

.gs-button-NinthStream {
    background-image: url(../source/button/NinthStream/normal.png);
}

.gs-button-ptzDown-click {
    background-image: url(../source/button/ptzDown/click.png) !important;
}

.gs-button-ptzDown:hover {
    background-image: url(../source/button/ptzDown/hover.png);
}

.gs-button-ptzDown-hover {
    background-image: url(../source/button/ptzDown/hover.png);
}

.gs-button-ptzDown {
    background-image: url(../source/button/ptzDown/normal.png);
}

.gs-button-ptzLeft-click {
    background-image: url(../source/button/ptzLeft/click.png) !important;
}

.gs-button-ptzLeft:hover {
    background-image: url(../source/button/ptzLeft/hover.png);
}

.gs-button-ptzLeft-hover {
    background-image: url(../source/button/ptzLeft/hover.png);
}

.gs-button-ptzLeft {
    background-image: url(../source/button/ptzLeft/normal.png);
}

.gs-button-ptzLeftDown-click {
    background-image: url(../source/button/ptzLeftDown/click.png) !important;
}

.gs-button-ptzLeftDown:hover {
    background-image: url(../source/button/ptzLeftDown/hover.png);
}

.gs-button-ptzLeftDown-hover {
    background-image: url(../source/button/ptzLeftDown/hover.png);
}

.gs-button-ptzLeftDown {
    background-image: url(../source/button/ptzLeftDown/normal.png);
}

.gs-button-ptzLeftUp-click {
    background-image: url(../source/button/ptzLeftUp/click.png) !important;
}

.gs-button-ptzLeftUp:hover {
    background-image: url(../source/button/ptzLeftUp/hover.png);
}

.gs-button-ptzLeftUp-hover {
    background-image: url(../source/button/ptzLeftUp/hover.png);
}

.gs-button-ptzLeftUp {
    background-image: url(../source/button/ptzLeftUp/normal.png);
}

.gs-button-ptzRight-click {
    background-image: url(../source/button/ptzRight/click.png) !important;
}

.gs-button-ptzRight:hover {
    background-image: url(../source/button/ptzRight/hover.png);
}

.gs-button-ptzRight-hover {
    background-image: url(../source/button/ptzRight/hover.png);
}

.gs-button-ptzRight {
    background-image: url(../source/button/ptzRight/normal.png);
}

.gs-button-ptzRightDown-click {
    background-image: url(../source/button/ptzRightDown/click.png) !important;
}

.gs-button-ptzRightDown:hover {
    background-image: url(../source/button/ptzRightDown/hover.png);
}

.gs-button-ptzRightDown-hover {
    background-image: url(../source/button/ptzRightDown/hover.png);
}

.gs-button-ptzRightDown {
    background-image: url(../source/button/ptzRightDown/normal.png);
}

.gs-button-ptzRightUp-click {
    background-image: url(../source/button/ptzRightUp/click.png) !important;
}

.gs-button-ptzRightUp:hover {
    background-image: url(../source/button/ptzRightUp/hover.png);
}

.gs-button-ptzRightUp-hover {
    background-image: url(../source/button/ptzRightUp/hover.png);
}

.gs-button-ptzRightUp {
    background-image: url(../source/button/ptzRightUp/normal.png);
}

.gs-button-ptzUp-click {
    background-image: url(../source/button/ptzUp/click.png) !important;
}

.gs-button-ptzUp:hover {
    background-image: url(../source/button/ptzUp/hover.png);
}

.gs-button-ptzUp-hover {
    background-image: url(../source/button/ptzUp/hover.png);
}

.gs-button-ptzUp {
    background-image: url(../source/button/ptzUp/normal.png);
}

.gs-button-ptzZoomIn-click {
    background-image: url(../source/button/ptzZoomIn/click.png) !important;
}

.gs-button-ptzZoomIn:hover {
    background-image: url(../source/button/ptzZoomIn/hover.png);
}

.gs-button-ptzZoomIn-hover {
    background-image: url(../source/button/ptzZoomIn/hover.png);
}

.gs-button-ptzZoomIn {
    background-image: url(../source/button/ptzZoomIn/normal.png);
}

.gs-button-ptzZoomOut-click {
    background-image: url(../source/button/ptzZoomOut/click.png) !important;
}

.gs-button-ptzZoomOut:hover {
    background-image: url(../source/button/ptzZoomOut/hover.png);
}

.gs-button-ptzZoomOut-hover {
    background-image: url(../source/button/ptzZoomOut/hover.png);
}

.gs-button-ptzZoomOut {
    background-image: url(../source/button/ptzZoomOut/normal.png);
}

.gs-button-SecondaryStream-click {
    background-image: url(../source/button/SecondaryStream/click.png) !important;
}

.gs-button-SecondaryStream:hover {
    background-image: url(../source/button/SecondaryStream/hover.png);
}

.gs-button-SecondaryStream-hover {
    background-image: url(../source/button/SecondaryStream/hover.png);
}

.gs-button-SecondaryStream {
    background-image: url(../source/button/SecondaryStream/normal.png);
}

.gs-button-SeventhStream-click {
    background-image: url(../source/button/SeventhStream/click.png) !important;
}

.gs-button-SeventhStream:hover {
    background-image: url(../source/button/SeventhStream/hover.png);
}

.gs-button-SeventhStream-hover {
    background-image: url(../source/button/SeventhStream/hover.png);
}

.gs-button-SeventhStream {
    background-image: url(../source/button/SeventhStream/normal.png);
}

.gs-button-SixthStream-click {
    background-image: url(../source/button/SixthStream/click.png) !important;
}

.gs-button-SixthStream:hover {
    background-image: url(../source/button/SixthStream/hover.png);
}

.gs-button-SixthStream-hover {
    background-image: url(../source/button/SixthStream/hover.png);
}

.gs-button-SixthStream {
    background-image: url(../source/button/SixthStream/normal.png);
}

.gs-button-SliceVideo-click {
    background-image: url(../source/button/SliceVideo/click.png) !important;
}

.gs-button-SliceVideo:hover {
    background-image: url(../source/button/SliceVideo/hover.png);
}

.gs-button-SliceVideo-hover {
    background-image: url(../source/button/SliceVideo/hover.png);
}

.gs-button-SliceVideo {
    background-image: url(../source/button/SliceVideo/normal.png);
}

.gs-button-TenthStream-click {
    background-image: url(../source/button/TenthStream/click.png) !important;
}

.gs-button-TenthStream:hover {
    background-image: url(../source/button/TenthStream/hover.png);
}

.gs-button-TenthStream-hover {
    background-image: url(../source/button/TenthStream/hover.png);
}

.gs-button-TenthStream {
    background-image: url(../source/button/TenthStream/normal.png);
}

.gs-button-ThirdStream-click {
    background-image: url(../source/button/ThirdStream/click.png) !important;
}

.gs-button-ThirdStream:hover {
    background-image: url(../source/button/ThirdStream/hover.png);
}

.gs-button-ThirdStream-hover {
    background-image: url(../source/button/ThirdStream/hover.png);
}

.gs-button-ThirdStream {
    background-image: url(../source/button/ThirdStream/normal.png);
}

.gs-common-Exclamation {
    background-image: url(../source/common/Exclamation.png) !important;
}

.gs-common-lock {
    background-image: url(../source/common/lock.png) !important;
}

.gs-common-NoVideo {
    background-image: url(../source/common/NoVideo.png) !important;
}

.gs-common-NoVideoOnly {
    background-image: url(../source/common/NoVideoOnly.png) !important;
}

.gs-mobile-mobileBack {
    background-image: url(../source/mobile/mobileBack.png) !important;
}

.gs-mobile-mobileClose {
    background-image: url(../source/mobile/mobileClose.png) !important;
}

.gs-mobile-mobileExitMagnify {
    background-image: url(../source/mobile/mobileExitMagnify.png) !important;
}

.gs-mobile-mobileMagnify {
    background-image: url(../source/mobile/mobileMagnify.png) !important;
}

.gs-mobile-mobilePortrait {
    background-image: url(../source/mobile/mobilePortrait.png) !important;
}

.gs-switch-close-FullScreen-click {
    background-image: url(../source/switch/close/FullScreen/click.png) !important;
}

.gs-switch-close-FullScreen:hover {
    background-image: url(../source/switch/close/FullScreen/hover.png);
}

.gs-switch-close-FullScreen-hover {
    background-image: url(../source/switch/close/FullScreen/hover.png);
}

.gs-switch-close-FullScreen {
    background-image: url(../source/switch/close/FullScreen/normal.png);
}

.gs-switch-close-KeepRatio-click {
    background-image: url(../source/switch/close/KeepRatio/click.png) !important;
}

.gs-switch-close-KeepRatio:hover {
    background-image: url(../source/switch/close/KeepRatio/hover.png);
}

.gs-switch-close-KeepRatio-hover {
    background-image: url(../source/switch/close/KeepRatio/hover.png);
}

.gs-switch-close-KeepRatio {
    background-image: url(../source/switch/close/KeepRatio/normal.png);
}

.gs-switch-close-Record-click {
    background-image: url(../source/switch/close/Record/click.png) !important;
}

.gs-switch-close-Record:hover {
    background-image: url(../source/switch/close/Record/hover.png);
}

.gs-switch-close-Record-hover {
    background-image: url(../source/switch/close/Record/hover.png);
}

.gs-switch-close-Record {
    background-image: url(../source/switch/close/Record/normal.png);
}

.gs-switch-close-Talk-click {
    background-image: url(../source/switch/close/Talk/click.png) !important;
}

.gs-switch-close-Talk:hover {
    background-image: url(../source/switch/close/Talk/hover.png);
}

.gs-switch-close-Talk-hover {
    background-image: url(../source/switch/close/Talk/hover.png);
}

.gs-switch-close-Talk {
    background-image: url(../source/switch/close/Talk/normal.png);
}

.gs-switch-close-ThreeDimensions-click {
    background-image: url(../source/switch/close/ThreeDimensions/click.png) !important;
}

.gs-switch-close-ThreeDimensions:hover {
    background-image: url(../source/switch/close/ThreeDimensions/hover.png);
}

.gs-switch-close-ThreeDimensions-hover {
    background-image: url(../source/switch/close/ThreeDimensions/hover.png);
}

.gs-switch-close-ThreeDimensions {
    background-image: url(../source/switch/close/ThreeDimensions/normal.png);
}

.gs-switch-close-Video-click {
    background-image: url(../source/switch/close/Video/click.png) !important;
}

.gs-switch-close-Video:hover {
    background-image: url(../source/switch/close/Video/hover.png);
}

.gs-switch-close-Video-hover {
    background-image: url(../source/switch/close/Video/hover.png);
}

.gs-switch-close-Video {
    background-image: url(../source/switch/close/Video/normal.png);
}

.gs-switch-close-Voice-click {
    background-image: url(../source/switch/close/Voice/click.png) !important;
}

.gs-switch-close-Voice:hover {
    background-image: url(../source/switch/close/Voice/hover.png);
}

.gs-switch-close-Voice-hover {
    background-image: url(../source/switch/close/Voice/hover.png);
}

.gs-switch-close-Voice {
    background-image: url(../source/switch/close/Voice/normal.png);
}

.gs-switch-close-Zoom-click {
    background-image: url(../source/switch/close/Zoom/click.png) !important;
}

.gs-switch-close-Zoom:hover {
    background-image: url(../source/switch/close/Zoom/hover.png);
}

.gs-switch-close-Zoom-hover {
    background-image: url(../source/switch/close/Zoom/hover.png);
}

.gs-switch-close-Zoom {
    background-image: url(../source/switch/close/Zoom/normal.png);
}

.gs-switch-open-FullScreen-click {
    background-image: url(../source/switch/open/FullScreen/click.png) !important;
}

.gs-switch-open-FullScreen:hover {
    background-image: url(../source/switch/open/FullScreen/hover.png);
}

.gs-switch-open-FullScreen-hover {
    background-image: url(../source/switch/open/FullScreen/hover.png);
}

.gs-switch-open-FullScreen {
    background-image: url(../source/switch/open/FullScreen/normal.png);
}

.gs-switch-open-KeepRatio-click {
    background-image: url(../source/switch/open/KeepRatio/click.png) !important;
}

.gs-switch-open-KeepRatio:hover {
    background-image: url(../source/switch/open/KeepRatio/hover.png);
}

.gs-switch-open-KeepRatio-hover {
    background-image: url(../source/switch/open/KeepRatio/hover.png);
}

.gs-switch-open-KeepRatio {
    background-image: url(../source/switch/open/KeepRatio/normal.png);
}

.gs-switch-open-Record-click {
    background-image: url(../source/switch/open/Record/click.png) !important;
}

.gs-switch-open-Record:hover {
    background-image: url(../source/switch/open/Record/hover.png);
}

.gs-switch-open-Record-hover {
    background-image: url(../source/switch/open/Record/hover.png);
}

.gs-switch-open-Record {
    background-image: url(../source/switch/open/Record/normal.png);
}

.gs-switch-open-Talk-click {
    background-image: url(../source/switch/open/Talk/click.png) !important;
}

.gs-switch-open-Talk:hover {
    background-image: url(../source/switch/open/Talk/hover.png);
}

.gs-switch-open-Talk-hover {
    background-image: url(../source/switch/open/Talk/hover.png);
}

.gs-switch-open-Talk {
    background-image: url(../source/switch/open/Talk/normal.png);
}

.gs-switch-open-ThreeDimensions-click {
    background-image: url(../source/switch/open/ThreeDimensions/click.png) !important;
}

.gs-switch-open-ThreeDimensions:hover {
    background-image: url(../source/switch/open/ThreeDimensions/hover.png);
}

.gs-switch-open-ThreeDimensions-hover {
    background-image: url(../source/switch/open/ThreeDimensions/hover.png);
}

.gs-switch-open-ThreeDimensions {
    background-image: url(../source/switch/open/ThreeDimensions/normal.png);
}

.gs-switch-open-Video-click {
    background-image: url(../source/switch/open/Video/click.png) !important;
}

.gs-switch-open-Video:hover {
    background-image: url(../source/switch/open/Video/hover.png);
}

.gs-switch-open-Video-hover {
    background-image: url(../source/switch/open/Video/hover.png);
}

.gs-switch-open-Video {
    background-image: url(../source/switch/open/Video/normal.png);
}

.gs-switch-open-Voice-click {
    background-image: url(../source/switch/open/Voice/click.png) !important;
}

.gs-switch-open-Voice:hover {
    background-image: url(../source/switch/open/Voice/hover.png);
}

.gs-switch-open-Voice-hover {
    background-image: url(../source/switch/open/Voice/hover.png);
}

.gs-switch-open-Voice {
    background-image: url(../source/switch/open/Voice/normal.png);
}

.gs-switch-open-Zoom-click {
    background-image: url(../source/switch/open/Zoom/click.png) !important;
}

.gs-switch-open-Zoom:hover {
    background-image: url(../source/switch/open/Zoom/hover.png);
}

.gs-switch-open-Zoom-hover {
    background-image: url(../source/switch/open/Zoom/hover.png);
}

.gs-switch-open-Zoom {
    background-image: url(../source/switch/open/Zoom/normal.png);
}

