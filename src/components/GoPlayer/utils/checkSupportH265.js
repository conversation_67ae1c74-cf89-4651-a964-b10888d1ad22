// 使用HTMLVideoElement.canPlayType()检测浏览器是否声明支持H.265格式
function isH265Supported() {
  // 这个video元素从未被添加到DOM中，它只是存在于JavaScript上下文中，
  // 当函数执行完毕后，如果没有引用指向它，它会被垃圾回收器回收。
  const video = document.createElement('video');
  const mimeTypes = [
    'video/mp4; codecs="hev1.1.6.L93.90"', // HEVC Main Profile
    'video/mp4; codecs="hvc1.1.6.L93.90"', // Apple HEVC
    'video/webm; codecs="hev1.1.6.L93.90"', // WebM格式
    'video/mp4; codecs="hev1.2.4.L153.B0"'  // HEVC Range Extension
  ];

  return mimeTypes.some(type => {
    const playability = video.canPlayType(type);
    return playability === "probably" || playability === "maybe";
  });
}

// 通过MediaCapabilities API检测编解码器支持情况
async function isH265Capable() {
  if (!('mediaCapabilities' in navigator)) return false;

  const config = {
    type: /** @type {'file'} */ ('file'),
    video: {
      contentType: 'video/mp4; codecs="hev1.1.6.L93.90"',
      width: 1920,
      height: 1080,
      bitrate: 5000000,
      framerate: 30
    }
  };

  try {
    const result = await navigator.mediaCapabilities.decodingInfo(config);
    return result.supported && result.smooth && result.powerEfficient;
  } catch {
    return false;
  }
}

// 检测操作系统和浏览器组合对H.265的支持情况
function getSystemSupport() {
  const ua = navigator.userAgent;
  const isWindows = /Windows/.test(ua);
  const isMac = /Macintosh/.test(ua);
  const isIOS = /iPhone|iPad|iPod/.test(ua);
  const isSafari = /Safari/.test(ua) && !/Chrome/.test(ua);
  const isChrome = /Chrome/.test(ua);
  const isEdge = /Edg/.test(ua);
  const isFirefox = /Firefox/.test(ua);

  // Safari支持情况
  if ((isMac || isIOS) && isSafari) {
    return true; // macOS和iOS上的Safari支持
  }

  // Chrome/Edge在Windows上的支持情况
  if (isWindows && (isChrome || isEdge)) {
    const match = ua.match(/Windows NT (\d+\.\d+)/);
    if (!match) return false;
    const osVersion = parseFloat(match[1]);
    return osVersion >= 10.0; // Windows 10+支持
  }

  // Firefox不支持
  if (isFirefox) {
    return false;
  }

  // 其他情况
  return null;
}

export async function isSupportH265() {
  return isH265Supported() && await isH265Capable() && getSystemSupport()
}