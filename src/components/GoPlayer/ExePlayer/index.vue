<!-- 组件须知 -->
<!-- 本组件是依赖于【高新兴智能助手5.0.6.2655】软件开发而成
  -- 本质上是通过 websocket 把约定好的数据格式传递给 视频播放器软件
  -- 视频播放器软件通过获取到的数据,自动拉流播放
  ---------------------------------------------------------------
  -- 原始参考代码在 ./utils 文件夹下
  -- device.js    封装的ws调用 [但没有类的概念,且没有断线重连等操作,因此新写了DeviceConnection.js]
  -- ipcPlayer.js 推数据流到视频播放器的相关逻辑
  ---------------------------------------------------------------
  -- 因为原始文档非 MD 格式,不好放在项目文件夹中.因此后续开发人员需要问相关人士索要.
  ---------------------------------------------------------------
  -- 逻辑须知
  -- 根据目前产品设计,点击监室即可获取到该监室内所有摄像流.
  -- 所以废弃了树状视频流及针对单个视频流的处理逻辑
  -- 后期如有必要,可以参考ipcPlayer.js进行调整
  -->
<template>
  <Alert type="error">
    很抱歉，当前电脑配置无法播放H.265协议的视频。
    <template slot="desc">
      <p>
        我们已将您选中的监控视频流信息推送到了GosunSocket软件。请在打开的视频播放器中查看监控信息。
      </p>
      <p style="margin-top: 8px">请确保本设备已安装并运行此软件。</p>
      <img style="margin-top: 5px; width: 100px" src="./image/logo.jpg" alt="" />
      <img style="margin-top: 5px; width: 100%" src="./image/software.jpg" alt="" />
    </template>
  </Alert>
</template>

<script>
import DeviceConnection from "./utils/DeviceConnection";
export default {
  name: "ExePlayer",
  components: {},
  props: {
    initConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    bvgUrl: {
      type: String,
      default: "",
    },
    getBase64: {
      type: Function,
    },
    autoPlay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nStreamType: "", // 码流类型
      videoWSObj: null,
      convertedPlayList: []
    };
  },
  created() { },
  methods: {
    play(playList, roomName) {
      this.convertedPlayList = playList.map((item, index) => {
        item.szNodeID = `111_#_${item.deviceId}_${item.channelId}_101`;
        item.PlayStream = index < 4; // 因为就文档描述，只能同时播放4个流的视频
        return item
      });
      this.initPlayer({ ipcArray: this.convertedPlayList, roomName });
    },
    /**
     * 调起本地播放器
     *
     */
    startVideo() {
      this.videoWSObj.send({ action: "startVideo" });
    },
    /**
     * 关闭本地播放器视频
     *
     * @param {any} type 选择要关闭的是实时（0）还是录像（1）
     */
    closeVideo(type) {
      this.videoWSObj.send({
        action: "closeVideo",
        type: type || 0,
      });
    },
    /**
     * 分屏
     *
     * @param {any} params 参数对象
     * @param {any} params.displayType 选择要设置分屏数的是实时（0）还是录像（1）
     * @param {any} params.displayNum 1、4、9
     */
    setDisplay(params) {
      this.videoWSObj.send({
        action: "setDisplay",
        displayType: 0,
        displayNum: 4,
        ...params,
      });
    },
    /**
     * 推送请求实时流参数
     *
     * @param {any} params 参数对象
     * @param {any} params.action 接口名：stream
     * @param {any} params.szNodeID 摄像头node id
     * @param {any} params.nStreamType 码流类型1为主码流，2为辅码流（录像播放没有码流类型）
     * @param {any} params.nVideoReqType 设备类型，固定101通道
     * @param {any} params.deviceConnectType 设备连接类型，固定0，非直连
     * @param {any} params.streamAgentType 转码（本地播放器GosuncnSocket没有用这个字段，by贵森） 0不使用流媒体转发 1使用rtsp流转发 2使用rtmp流转发 3使用rtp流转发 4使用webrtc流转发 5使用httpflv流转发 6使用hls流转发 7使用gsmp流转发
     * @param {any} params.CameraName 摄像头名称
     * @param {any} params.LocalPlayer 是否使用本地播放器
     * @param {any} params.Display 本地播放器是否顶置显示
     * @param {any} params.nIndex 从0开始的索引号，-1自动寻找空闲窗口
     * @returns
     */
    // 推送单个数据流的功能暂时用不上，屏蔽以减少干扰
    // sendStreamParams: function (params) {
    //   this.videoWSObj.send({
    //     action: "stream",
    //     szNodeID: "",
    //     CameraName: "",
    //     nVideoReqType: 101,
    //     nStreamType: 2,
    //     deviceConnectType: 0,
    //     streamAgentType: 5,
    //     LocalPlayer: true,
    //     Display: false,
    //     nIndex: -1,
    //     ...params,
    //   });
    // },
    /**
     * 推送请求实时流列表参数
     * @param {Array} params 视频参数对象
     * @param {Array} params.ipcArray 视频流数组 参数同单个播放
     */
    sendMultStreamParams(params) {
      const streamList = [];
      params.ipcArray.forEach((ipc) => {
        streamList.push({
          szNodeID: ipc.szNodeID,
          CameraName: ipc.deviceName,
          nVideoReqType: ipc.nVideoReqType || 101,
          nStreamType: params.nStreamType || 2,
          deviceConnectType: 0,
          streamAgentType: 5,
          Display: true,
          PlayStream: ipc.PlayStream,
        });
      });
      let theParams = {
        action: "deviceTree",
        LocalPlayer: true,
        Display: true,
        DeviceList: [
          {
            CameraName: params.roomName || "监控设备",
            DeviceList: streamList,
          },
        ],
      };
      this.videoWSObj.send(theParams);
    },
    /**
     * 推送请求录像列表流参数
     * @param {Object} ipcArray 参数对象
     * @param {Number} nFormStyle 0：normal；1：精简模式（屏蔽播放器进度条、播放相关按钮及【1窗口】、【4窗口】、【全屏】、【关闭所有】等按钮）
     * @returns
     */
    // 暂时没有相应需求，所以先屏蔽
    // sendMultiRecordParams: function (ipcArray = [], nFormStyle) {
    //   let recordList = [];
    //   ipcArray.forEach((item, index) => {
    //     let params = {
    //       nVideoReqType: 101,
    //       nRecordType: -1,
    //       deviceConnectType: 0,
    //       streamAgentType: 6,
    //       szNodeID: item.szNodeID,
    //       CameraName: item.deviceName,
    //       szStartTime: item.startTime,
    //       szEndTime: item.endTime,
    //       szCurrentPlayTime: item.currentTime || item.startTime,
    //       nStorageType: item.nStorageType || 0,
    //       LocalPlayer: true,
    //       Display: true,
    //       PlayNow: index < 4,
    //       nIndex: -1,
    //       nFormStyle: nFormStyle || 0,
    //     };
    //     params.szStartTime =
    //       params.szStartTime &&
    //       params.szStartTime.replace(" ", "-").replace(/:/g, "-");
    //     params.szEndTime =
    //       params.szEndTime &&
    //       params.szEndTime.replace(" ", "-").replace(/:/g, "-");
    //     params.szCurrentPlayTime =
    //       params.szCurrentPlayTime &&
    //       params.szCurrentPlayTime.replace(" ", "-").replace(/:/g, "-");
    //     recordList.push(params);
    //   });

    //   this.videoWSObj.send({
    //     action: "multiRecord",
    //     RecordList: recordList,
    //   });
    // },
    initPlayer(params) {
      console.log("initPlayer", params, this.bvgUrl);
      let len = params.displayNum
        ? params.displayNum
        : params.ipcArray && params.ipcArray.length
          ? params.ipcArray.length
          : 1;
      let index = 0;
      let setDisplay = false;
      // Notice 此流程因业务暂不触及，所以会被跳过
      // if (this.videoWSObj && params.videolate) {
      //   if (params.type === 1) {
      //     if (params.ipcArray && params.ipcArray.length) {
      //       this.sendMultiRecordParams(params.ipcArray, params.nFormStyle);
      //     } else {
      //       this.sendMultiRecordParams([].concat(params), params.nFormStyle);
      //     }
      //   }
      //   return;
      // }

      this.videoWSObj = new DeviceConnection(
        "videoUrl",
        (type, ret) => {
          switch (type) {
            case "onopen":
              this.videoWSObj.send({
                action: "login",
                url: this.bvgUrl,
              });
              break;
            case "onmessage":
              let result = JSON.parse(ret.data);
              if (result.code !== 0) {
                params.errorCallback && params.errorCallback(result);
                this.$Message.info(result.message);
                if (
                  !(
                    params.ipcArray &&
                    params.ipcArray.length &&
                    index < params.ipcArray.length
                  )
                ) {
                  return;
                }
              }
              if (result.action === "login") {
                this.startVideo();
              }
              if (result.action === "startVideo") {
                this.closeVideo();
              }
              if (result.action === "closeVideo") {
                if (!setDisplay) {
                  if (len === 1) {
                    len = 1;
                  } else if (len > 1 && len <= 4) {
                    len = 4;
                  } else if (len > 4) {
                    len = 9;
                  }
                  this.setDisplay({
                    displayType: params.type,
                    displayNum: len,
                  });
                  setDisplay = true;
                }
              }
              if (result.action === "setDisplay") {
                // 实时播放
                if (params.ipcArray && params.ipcArray.length) {
                  this.sendMultStreamParams(params);
                } else {
                  // 目前不存在推送单流的情况 按需打开
                  // this.sendStreamParams({
                  //   szNodeID: params.szNodeID,
                  //   nStreamType: params.nStreamType || 2,
                  //   CameraName: params.deviceName,
                  //   LocalPlayer: true,
                  //   Display: true,
                  // });
                }
              }
              if (result.action === "record") {
                const { progress } = result.data ? JSON.parse(result.data) : {};
                params.playprogressCallback &&
                  params.playprogressCallback(progress);
              }
              // 本地播放器关闭或退出 1 关闭 0开启
              if (result.action === "playerStatus") {
                params.playstatusCallback && params.playstatusCallback(result);
              }
              break;
            case "onclose":
              break;
            case "onerror":
              console.log(e);
              this.$Message.error(e);
              break;
          }
        },
        {
          reconnectInterval: 3000,
          maxReconnectAttempts: 10,
        }
      );

      // 连接设备
      this.videoWSObj
        .connect()
        .then(() => {
          console.log("Connected!");
          // this.videoWSObj.open();
        })
        .catch((error) => {
          console.error("Connection failed:", error);
        });
    },
    destroyWs() {
      this.videoWSObj && this.videoWSObj.close();
      this.videoWSObj = null;
    },
    /**
     * 关闭视频
     *
     * @param {any} type 选择要关闭的是实时（0）还是录像（1）
     */
    closeRelateVideo(type) {
      this.videoWSObj &&
        this.closeVideo(this.videoWSObj, {
          type: type,
        });
    },
  },
};
</script>

<style lang="less" scoped></style>
