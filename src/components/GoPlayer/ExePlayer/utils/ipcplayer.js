/* eslint-disable no-tabs */
/* eslint-disable indent */
/* eslint-disable handle-callback-err */
/* eslint-disable prefer-promise-reject-errors */
// @ts-nocheck
/* 视频播放对接
 * @Author: chenpeiyu
 * @Date: 2021-09-27 16:34:46
 * @Last Modified by: chen<PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-05-27 09:36:12
 */
const ipcplayer = {
	/**
	 * 调起本地播放器
	 *
	 * @param {any} wsObj ws对象
	 * @param {any} params 参数对象
	 */
	startVideo(wsObj, params) {
		const defaultParams = {
			action: "startVideo",
		};
		let theParams = Object.assign(defaultParams, params || {});
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 关闭本地播放器视频
	 *
	 * @param {any} wsObj ws对象
	 * @param {any} params 参数对象
	 * @param {any} params.action 接口名：closeVideo
	 * @param {any} params.type 选择要关闭的是实时（0）还是录像（1）
	 */
	closeVideo(wsObj, params) {
		const defaultParams = {
			action: "closeVideo",
			type: 0,
		};
		let theParams = Object.assign(defaultParams, params || {});
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 分屏
	 *
	 * @param {any} wsObj ws对象
	 * @param {any} params 参数对象
	 * @param {any} params.displayType 选择要设置分屏数的是实时（0）还是录像（1）
	 * @param {any} params.displayNum 1、4、9
	 */
	setDisplay(wsObj, params) {
		const defaultParams = {
			action: "setDisplay",
			displayType: 0,
			displayNum: 4,
		};
		let theParams = Object.assign(defaultParams, params || {});
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 推送请求实时流参数
	 *
	 * @param {any} wsObj ws对象
	 * @param {any} params 参数对象
	 * @param {any} params.action 接口名：stream
	 * @param {any} params.szNodeID 摄像头node id
	 * @param {any} params.nStreamType 码流类型1为主码流，2为辅码流（录像播放没有码流类型）
	 * @param {any} params.nVideoReqType 设备类型，固定101通道
	 * @param {any} params.deviceConnectType 设备连接类型，固定0，非直连
	 * @param {any} params.streamAgentType 转码（本地播放器GosuncnSocket没有用这个字段，by贵森） 0不使用流媒体转发 1使用rtsp流转发 2使用rtmp流转发 3使用rtp流转发 4使用webrtc流转发 5使用httpflv流转发 6使用hls流转发 7使用gsmp流转发
	 * @param {any} params.CameraName 摄像头名称
	 * @param {any} params.LocalPlayer 是否使用本地播放器
	 * @param {any} params.Display 本地播放器是否顶置显示
	 * @param {any} params.nIndex 从0开始的索引号，-1自动寻找空闲窗口
	 * @returns
	 */
	sendStreamParams: function (wsObj, params) {
		const defaultParams = {
			action: "stream",
			szNodeID: "",
			CameraName: "",
			nVideoReqType: 101,
			nStreamType: 2,
			deviceConnectType: 0,
			streamAgentType: 5,
			LocalPlayer: true,
			Display: false,
			nIndex: -1,
		};
		let theParams = Object.assign(defaultParams, params || {});
		wsObj.send(JSON.stringify(theParams));
	},

	/**
	 * 推送请求实时流  摄像头多级列表展示（支持树形多级递归展示） 参数
	 * @param {Object} wsObj webscoket 实例对象
	 * @param {Array} params 视频参数对象
	 * @param {Array} params.ipcArray 视频流数组 参数同单个播放
	 */
	sendMultListStreamParams(wsObj, params) {
		const DeviceList = [];
		const handleDevice = (deviceList = []) => {
			const devices = [];
			let isExpanded = false;
			deviceList.forEach((item) => {
				if (item.DeviceList && item.DeviceList.length) {
					// 目录
					const deviceInfo = handleDevice(item.DeviceList);
					DeviceList.push({
						isLeaf: false,
						isExpanded: deviceInfo.isExpanded,
						CameraName: item.CameraName,
						DeviceList: deviceInfo.devices || [],
					});
					isExpanded = false;
				} else {
					// 叶子节点
					devices.push({
						isLeaf: true,
						isExpanded: false,
						szNodeID: item.szNodeID,
						CameraName: item.CameraName,
						nVideoReqType: item.nVideoReqType || 101,
						nStreamType: item.nStreamType || params.nStreamType || 2,
						deviceConnectType: 0,
						streamAgentType: 5,
						PlayStream: item.PlayStream,
						ip: item.ipcIp || "",
					});
					isExpanded = isExpanded ? true : item.PlayStream;
				}
			});
			return { isExpanded, devices };
		};
		handleDevice(params.ipcArray);
		let theParams = {
			action: "deviceTreeEx",
			LocalPlayer: true,
			Display: false,
			DeviceList,
		};
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 推送请求实时流列表参数
	 * @param {Object} wsObj webscoket 实例对象
	 * @param {Array} params 视频参数对象
	 * @param {Array} params.ipcArray 视频流数组 参数同单个播放
	 */
	sendMultStreamParams(wsObj, params) {
		const streamList = [];
		params.ipcArray.forEach((ipc) => {
			streamList.push({
				szNodeID: ipc.szNodeID,
				CameraName: ipc.ipcName,
				nVideoReqType: ipc.nVideoReqType || 101,
				nStreamType: params.nStreamType || 2,
				deviceConnectType: 0,
				streamAgentType: 5,
				Display: true,
				PlayStream: true,
			});
		});
		let theParams = {
			action: "deviceTree",
			LocalPlayer: true,
			Display: false,
			DeviceList: [
				{
					CameraName: params.roomName || "",
					DeviceList: streamList,
				},
			],
		};
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 推送请求录像流参数
	 *
	 * @param {Object} wsObj ws对象
	 * @param {Object} params 参数对象
	 * @param {String} params.action 接口名：record
	 * @param {String} params.szNodeID 摄像头node id
	 * @param {any} params.nVideoReqType 设备类型，固定101通道
	 * @param {any} params.nStorageType 存储类型 GoVideo 7.0：0中心存储 1设备存储
	 * @param {any} params.nRecordType 录像类型，固定传-1，所有录像
	 * @param {any} params.szStartTime 录像开始时间，格式yyyy-MM-dd-HH-mm-ss
	 * @param {any} params.szEndTime 录像结束时间，格式yyyy-MM-dd-HH-mm-ss
	 * @param {any} params.szCurrentPlayTime 录像开始播放时间，格式yyyy-MM-dd-HH-mm-ss，不能超出开始和结束时间区间
	 * @param {any} params.deviceConnectType 设备连接类型，固定0，非直连
	 * @param {any} params.streamAgentType 转码（本地播放器GosuncnSocket没有用这个字段，by贵森） 0不使用流媒体转发 1使用rtsp流转发 2使用rtmp流转发 3使用rtp流转发 4使用webrtc流转发 5使用httpflv流转发 6使用hls流转发 7使用gsmp流转发
	 * @param {any} params.CameraName 摄像头名称
	 * @param {any} params.LocalPlayer 是否使用本地播放器
	 * @param {any} params.nIndex 从0开始的索引号，-1自动寻找空闲窗口
	 * @returns
	 */
	sendRecordParams: function (wsObj, params) {
		params.szStartTime =
			params.szStartTime &&
			params.szStartTime.replace(" ", "-").replace(/:/g, "-");
		params.szEndTime =
			params.szEndTime && params.szEndTime.replace(" ", "-").replace(/:/g, "-");
		params.szCurrentPlayTime =
			params.szCurrentPlayTime &&
			params.szCurrentPlayTime.replace(" ", "-").replace(/:/g, "-");
		const defaultParams = {
			action: "record",
			szNodeID: "",
			CameraName: "",
			nVideoReqType: 101,
			nStorageType: 1,
			nRecordType: -1,
			szStartTime: "",
			szEndTime: "",
			szCurrentPlayTime: "",
			deviceConnectType: 0,
			streamAgentType: 6,
			LocalPlayer: true,
			Display: false,
			nIndex: -1,
		};
		let theParams = Object.assign(defaultParams, params || {});
		wsObj.send(JSON.stringify(theParams));
	},
	/**
	 * 推送请求录像列表流参数
	 *
	 * @param {Object} wsObj ws对象
	 * @param {Object} ipcArray 参数对象
	 * @param {Number} nFormStyle 0：normal；1：精简模式（屏蔽播放器进度条、播放相关按钮及【1窗口】、【4窗口】、【全屏】、【关闭所有】等按钮）
	 * @returns
	 */
	sendMultiRecordParams: function (wsObj, ipcArray = [], nFormStyle) {
		let recordList = [];
		ipcArray.forEach((item, index) => {
			let params = {
				nVideoReqType: 101,
				nRecordType: -1,
				deviceConnectType: 0,
				streamAgentType: 6,
				szNodeID: item.szNodeID,
				CameraName: item.ipcName,
				szStartTime: item.startTime,
				szEndTime: item.endTime,
				szCurrentPlayTime: item.currentTime || item.startTime,
				nStorageType: item.nStorageType || 0,
				LocalPlayer: true,
				Display: true,
				PlayNow: index < 4,
				nIndex: -1,
				nFormStyle: nFormStyle || 0,
			};
			params.szStartTime =
				params.szStartTime &&
				params.szStartTime.replace(" ", "-").replace(/:/g, "-");
			params.szEndTime =
				params.szEndTime &&
				params.szEndTime.replace(" ", "-").replace(/:/g, "-");
			params.szCurrentPlayTime =
				params.szCurrentPlayTime &&
				params.szCurrentPlayTime.replace(" ", "-").replace(/:/g, "-");
			recordList.push(params);
		});
		let theParams = {
			action: "multiRecord",
			RecordList: recordList,
		};
		wsObj.send(JSON.stringify(theParams));
	},

	/**
	 * 获取本地播放器状态（promise 0：关闭 1：开启）
	 */
	getLocalPlayerState() {
		let p = new Promise((resolve, reject) => {
			let ws = device.init("infoUrl", (type, ret, unifyDealError) => {
				switch (type) {
					case "onopen":
						device.send(ws, JSON.stringify({ action: "localPlayer" }));
						break;
					case "onmessage":
						let result = JSON.parse(ret.data);
						if (result.action === "localPlayer") {
							if (result.code === 0) {
								if (result.data && result.data === 1) {
									resolve(1);
								} else {
									resolve(0);
								}
							} else {
								reject(result.message);
							}
							ws.close();
						}
						break;
					case "onclose":
						break;
					case "onerror":
						unifyDealError("连接视频服务失败,请下载安装智能助手服务！");
						reject();
						break;
				}
			});
		});
		return p;
	},

	/**
	 * 根据IpcMagicId获取通道信息（ID和名称）
	 */
	getSzNodeInfoByIpcMagicId(ipcMagicId) {
		let p = new Promise((resolve, reject) => {
			configApi.getSzNodeInfoByIpcMagicId(ipcMagicId).then(
				(res) => {
					if (res.code && res.data && res.data.channelId) {
						resolve(res.data);
					} else {
						reject("获取通道ID失败！");
					}
				},
				(err) => {
					reject("获取通道ID服务出错！");
				}
			);
		});
		return p;
	},
	/**
	 * 获取办案区配置的视频码流配置
	 */
	getVideoStreamType() {
		let p = new Promise((resolve, reject) => {
			configApi
				.getOneCenterConfWork(
					store.state.user.accessSite.magicId,
					"CENTER_VIDEO_STREAM"
				)
				.then(
					(res) => {
						resove(res.fieldValue || 2);
					},
					(err) => {
						resolve(2);
					}
				);
		});
		return p;
	},
	/**
	 * 直播（hls）
	 *
	 * @param {Object} params 参数对象
	 * @param {String} params.ipcMagicId ipcMagicId：ipcMagicId和szNodeID二选一
	 * @param {String} params.szNodeID szNodeID：ipcMagicId和szNodeID二选一
	 * @param {String} params.ipcName 若传ipcMagicId则根据ipcMagicId获取（选填）
	 * @param {Array} params.ipcArray 多路播放ipc参数数组,与上面参数一致（与上面参数二选一）
	 * @param {Array} params.roomName 多路播放ipc 传功能室名称
	 * @param {Function} params.playstatusCallback  播放器状态回调 1 关闭 0开启
	 */
	livePlay(params) {
		this.toLocalPlayer({
			type: 0,
			ipcMagicId: params.ipcMagicId || "",
			szNodeID: params.szNodeID || "",
			ipcName: params.ipcName || "",
			ipcArray: params.ipcArray || [],
			roomName: params.roomName || "",
			playstatusCallback: params.playstatusCallback || null,
		});
	},
	/**
	 * 回放
	 * @param {Object} params 参数对象
	 * @param {String} params.ipcMagicId ipcMagicId：ipcMagicId和szNodeID二选一
	 * @param {String} params.szNodeID szNodeID：ipcMagicId和szNodeID二选一
	 * @param {String} params.ipcName 若传ipcMagicId则根据ipcMagicId获取（选填）
	 * @param {String} params.startTime 录像开始时间 YYYY-MM-HH hh:mm:ss (必填)
	 * @param {String} params.endTime 录像结束时间 YYYY-MM-HH hh:mm:ss (必填)
	 * @param {String} params.currentTime 录像当前时间 YYYY-MM-HH hh:mm:ss (选填)
	 * @param {Array} params.ipcArray 多路录像播放ipc参数数组,与上面参数一致（与上面参数二选一）
	 * @param {Number} params.nStorageType 存储类型，0中心存储 1设备存储
	 */
	playBack(params) {
		this.toLocalPlayer({
			type: 1,
			ipcMagicId: params.ipcMagicId || "",
			szNodeID: params.szNodeID || "",
			ipcName: params.ipcName || "",
			startTime: params.startTime || "",
			endTime: params.endTime || "",
			currentTime: params.currentTime || "",
			ipcArray: params.ipcArray || [],
			nStorageType: params.nStorageType || 0,
		});
	},
	/**
	 * 本地播放器播放MP4url地址
	 * @param {Object} params
	 * @param {string} params.szUrl MP4播放地址
	 */
	async localPlayBackMp4(params) {
		if (!params.url && !this.bvgUrl) {
			// 获取bvgurl地址
			const res = await bsp.getParamsValue("BVG_URL", baseConfig.CSP_MARK);
			if (res.code === 200) {
				this.bvgUrl = params.url = res.data;
			} else {
				Vue.prototype.$basePrompt.info({
					type: "warning",
					showBtn: false,
					message: res.msg || "获取bvgUrl地址失败",
				});
				return;
			}
		} else if (!params.url) {
			params.url = this.bvgUrl;
		}
		// 校验MP4参数
		if (params.ipcArray && params.ipcArray.length) {
			params.ipcArray = params.ipcArray.filter((item) => {
				return item.szUrl;
			});
			if (!(params.ipcArray && params.ipcArray.length)) {
				Vue.prototype.$basePrompt.info({
					type: "warning",
					showBtn: false,
					message: "MP4视频缺少视频url地址！",
				});
				return;
			}
		} else if (!params.szUrl) {
			Vue.prototype.$basePrompt.info({
				type: "warning",
				showBtn: false,
				message: "MP4视频缺少视频url地址！",
			});
			return;
		}
		this.initPlayer({
			type: 1, // 播放类型，1:录像播放
			mimeType: "mp4",
			nRecordVideoType: 1, //播放MP4固定传1
			url: params.url || "", //bvgurl地址
			szUrl: params.szUrl || "", // mp4地址
			LocalPlayer: true,
			ipcArray: params.ipcArray || [], // 播放多个MP4文件
			fileName: params.fileName,
		});
	},
	/**
	 * 推送MP4录像流参数
	 *
	 * @param {Object} params 参数对象
	 * @param {Object} ws ws对象
	 */
	sendRecordMp4Params(params, ws, index) {
		console.log("mp4canshu", params);
		let theParams = {
			action: "record",
			nRecordVideoType: 1, //播放MP4固定传1
			szUrl:
				params.ipcArray && params.ipcArray.length
					? params.ipcArray[0].szUrl
					: params.szUrl,
			LocalPlayer: true,
			CameraName:
				params.ipcArray && params.ipcArray.length
					? params.ipcArray[0].fileName || null
					: params.fileName || null,
			nIndex: -1,
		};
		ws.send(JSON.stringify(theParams));
	},
	/**
	 * 推送MP4录像列表流参数
	 *
	 * @param {Object} wsObj ws对象
	 * @param {Object} ipcArray 参数对象
	 */
	sendMultiRecordMp4Params(ws, ipcArray) {
		const recordList = [];
		// console.log(ipcArray, 'ipcArray')
		ipcArray.forEach((item, index) => {
			let params = {
				nRecordVideoType: 1, //播放MP4固定传1
				LocalPlayer: true,
				szUrl: item.szUrl,
				CameraName: item.fileName || null,
				PlayNow: index < 4,
				nIndex: -1,
			};
			recordList.push(params);
		});
		let theParams = {
			action: "multiRecord",
			RecordList: recordList,
		};
		ws.send(JSON.stringify(theParams));
	},
	/**
	 * 回放MP4(弹窗播放)
	 * @param {Object} params 参数对象
	 * @param {String} params.title 弹窗title
	 * @param {String} params.data 播放视频list
	 * @param {String} params.className 弹窗类名
	 */
	playBackMp4(params) {
		Vue.prototype.$dialog({
			title: params.title || "录像视频",
			showHeader: true,
			showFooter: false,
			component: playBackMp4,
			componentParams: params,
			contentClass: params.className,
		});
	},
	nStreamType: "", // 码流类型
	bvgUrl: "", // bvgurl地址
	wsObj: null, // ws实例对象
	/**
	 * 判断打开本地播放器参数
	 * @param {Object} params 参数对象
	 * @param {Number} params.displayNum 分屏数 1、4、9
	 * @param {Boolean} params.isMultiList 摄像头多级列表展示（支持树形多级递归展示）
	 * @param {String} params.type 播放类型（必填）0：直播 1：回放
	 * @param {String} params.url 摄像头url地址
	 * @param {String} params.ipcMagicId ipcMagicId：ipcMagicId和szNodeID二选一
	 * @param {String} params.szNodeID szNodeID：ipcMagicId和szNodeID二选一
	 * @param {String} params.startTime 录像开始时间 YYYY-MM-HH hh:mm:ss (当type === 1 时必填)
	 * @param {String} params.endTime 录像结束时间 YYYY-MM-HH hh:mm:ss (当type === 1 时必填)
	 * @param {String} params.currentTime 录像当前时间 YYYY-MM-HH hh:mm:ss (当type === 1 时选填)
	 * @param {Array}  params.ipcArray ipc数组，对象为json,与上面的参数一致(仅本地播放器)
	 * @param {any}    params.nStorageType 存储类型，0中心存储 1设备存储
	 * @param {Number} params.nFormStyle 0：normal；1：精简模式（屏蔽播放器进度条、播放相关按钮及【1窗口】、【4窗口】、【全屏】、【关闭所有】等按钮）
	 * @param {Function} params.playstatusCallback  播放器状态回调 1 关闭 0开启
	 */
	async toLocalPlayer(params) {
		let self = this;
		if (!params.url && !self.bvgUrl) {
			// 获取bvgurl地址
			const res = await bsp.getParamsValue("BVG_URL", baseConfig.CSP_MARK);
			if (res.code === 200) {
				self.bvgUrl = params.url = res.data;
			} else {
				Vue.prototype.$basePrompt.info({
					type: "warning",
					showBtn: false,
					message: res.msg || "获取bvgUrl地址失败",
				});
				return;
			}
		} else if (!params.url) {
			params.url = self.bvgUrl;
		}
		if (params.ipcArray && params.ipcArray.length) {
			// 摄像头多级列表展示（支持树形多级递归展示）
			if (params.isMultiList) {
				self.openLocalPlayer(params);
				return;
			}
			for (let i in params.ipcArray) {
				if (
					params.type === 1 &&
					(!params.ipcArray[i].startTime || !params.ipcArray[i].endTime)
				) {
					Vue.prototype.$basePrompt.info({
						type: "warning",
						showBtn: false,
						message: "缺少录像时间参数！",
					});
					return;
				}
				if (!params.ipcArray[i].ipcMagicId && !params.ipcArray[i].szNodeID) {
					Vue.prototype.$basePrompt.info({
						type: "warning",
						showBtn: false,
						message: "缺少设备参数！",
					});
					return;
				}
				if (params.ipcArray[i].ipcMagicId && !params.ipcArray[i].szNodeID) {
					(function (i) {
						self.getSzNodeInfoByIpcMagicId(params.ipcArray[i].ipcMagicId).then(
							(ret) => {
								params.ipcArray[i].szNodeID = ret.ipcStreamParam;
								params.ipcArray[i].ipcName = ret.ipcName || "";
								params.ipcArray[i].nStorageType = ret.storageType;
								self.toLocalPlayer(params);
							},
							(ret) => {
								Vue.prototype.$basePrompt.info({
									type: "warning",
									showBtn: false,
									message: ret,
								});
							}
						);
					})(i);
					return;
				}
			}
			self.openLocalPlayer(params);
		} else {
			if (params.type === 1 && (!params.startTime || !params.endTime)) {
				Vue.prototype.$basePrompt.info({
					type: "warning",
					showBtn: false,
					message: "缺少录像时间参数！",
				});
				return;
			}
			if (!params.ipcMagicId && !params.szNodeID) {
				Vue.prototype.$basePrompt.info({
					type: "warning",
					showBtn: false,
					message: "缺少设备参数！",
				});
				return;
			}
			if (params.ipcMagicId && !params.szNodeID) {
				self.getSzNodeInfoByIpcMagicId(params.ipcMagicId).then(
					(ret) => {
						params.szNodeID = ret.ipcStreamParam;
						params.ipcName = ret.ipcName || "";
						params.nStorageType = ret.storageType;
						self.toLocalPlayer(params);
					},
					(ret) => {
						Vue.prototype.$basePrompt.info({
							type: "warning",
							showBtn: false,
							message: ret,
						});
					}
				);
				return;
			}
			// 打开本地播放器
			self.openLocalPlayer(params);
		}
	},

	/**
	 * 打开本地播放器
	 * @param {Object} params 参数对象
	 * @param {String} params.type 播放类型（必填）0：直播 1：回放
	 * @param {String} params.szNodeID szNodeID（必填）
	 * @param {String} params.ipcName ipc设备名称（选填）
	 * @param {String} params.url bvgUrl地址
	 * @param {String} params.nStreamType 码流类型1为主码流，2为辅码流
	 * @param {String} params.startTime 录像开始时间 YYYY-MM-HH hh:mm:ss (当type === 1 时必填)
	 * @param {String} params.endTime 录像结束时间 YYYY-MM-HH hh:mm:ss (当type === 1 时必填)
	 * @param {String} params.currentTime 录像当前时间 YYYY-MM-HH hh:mm:ss (当type === 1 时选填)
	 * @param {Array}  params.ipcArray ipc数组，对象为json,与上面的参数一致(仅本地播放器)
	 * @param {any}    params.nStorageType 存储类型，0中心存储 1设备存储
	 */
	async openLocalPlayer(params) {
		// 获取办案区视频码流字典配置
		if (!params.nStreamType && !this.nStreamType) {
			params.nStreamType = this.nStreamType = await this.getVideoStreamType();
		} else if (!params.nStreamType) {
			params.nStreamType = this.nStreamType;
		}
		this.initPlayer(params);
	},
	initPlayer(params) {
		let len = params.displayNum
			? params.displayNum
			: params.ipcArray && params.ipcArray.length
				? params.ipcArray.length : 1;
		let index = 0;
		let setDisplay = false;
		if (this.wsObj && params.videolate) {
			if (params.type === 1) {
				if (params.ipcArray && params.ipcArray.length) {
					ipcplayer.sendMultiRecordParams(
						this.wsObj,
						params.ipcArray,
						params.nFormStyle
					);
				} else {
					ipcplayer.sendMultiRecordParams(
						this.wsObj,
						[].concat(params),
						params.nFormStyle
					);
				}
			}
			return;
		}
		let ws = device.init("videoUrl", (type, ret, unifyDealError) => {
			switch (type) {
				case "onopen":
					ws.send(
						JSON.stringify({
							action: "login",
							url: params.url,
						})
					);
					break;
				case "onmessage":
					let result = JSON.parse(ret.data);
					if (result.code !== 0) {
						params.errorCallback && params.errorCallback(result);
						Vue.prototype.$basePrompt.info({
							type: "warning",
							showBtn: false,
							message: result.message,
						});
						if (
							!(
								params.ipcArray &&
								params.ipcArray.length &&
								index < params.ipcArray.length
							)
						) {
							return;
						}
					}
					if (result.action === "login") {
						ipcplayer.startVideo(ws);
					}
					if (result.action === "startVideo") {
						ipcplayer.closeVideo(ws);
					}
					if (result.action === "closeVideo") {
						if (!setDisplay) {
							if (len === 1) {
								len = 1;
							} else if (len > 1 && len <= 4) {
								len = 4;
							} else if (len > 4) {
								len = 9;
							}
							ipcplayer.setDisplay(ws, {
								displayType: params.type,
								displayNum: len,
							});
							setDisplay = true;
						}
					}
					if (result.action === "setDisplay") {
						// 实时播放
						if (params.type === 0) {
							if (params.ipcArray && params.ipcArray.length) {
								if (params.isMultiList) {
									ipcplayer.sendMultListStreamParams(ws, params);
								} else {
									ipcplayer.sendMultStreamParams(ws, params);
								}
							} else {
								ipcplayer.sendStreamParams(ws, {
									szNodeID: params.szNodeID,
									nStreamType: params.nStreamType,
									CameraName: params.ipcName,
									LocalPlayer: true,
									Display: true,
								});
							}
						}
						// 录像播放
						if (params.type === 1) {
							if (params.mimeType === "mp4") {
								// 播放MP4
								// ipcplayer.sendRecordMp4Params(params, ws, -1)
								if (params.ipcArray && params.ipcArray.length) {
									ipcplayer.sendMultiRecordMp4Params(ws, params.ipcArray);
								} else {
									ipcplayer.sendMultiRecordMp4Params(ws, [].concat(params));
								}
							} else {
								if (params.ipcArray && params.ipcArray.length) {
									ipcplayer.sendMultiRecordParams(
										ws,
										params.ipcArray,
										params.nFormStyle
									);
								} else {
									ipcplayer.sendMultiRecordParams(
										ws,
										[].concat(params),
										params.nFormStyle
									);
								}
							}
						}
					}
					if (result.action === "record") {
						const { progress } = result.data ? JSON.parse(result.data) : {};
						params.playprogressCallback &&
							params.playprogressCallback(progress);
					}
					// 本地播放器关闭或退出 1 关闭 0开启
					if (result.action === "playerStatus") {
						params.playstatusCallback && params.playstatusCallback(result);
					}
					break;
				case "onclose":
					break;
				case "onerror":
					unifyDealError();
					break;
			}
		});
		this.wsObj = ws;
	},
	// 检查视频流是否在播放
	checkSzNodeIdPlay(szNodeID) {
		let p = new Promise((resolve, reject) => {
			let ws = device.init("videoUrl", (type, ret, unifyDealError) => {
				switch (type) {
					case "onopen":
						ws.send(
							JSON.stringify({
								action: "checkStreamPlaying",
								szNodeID: szNodeID,
							})
						);
						break;
					case "onmessage":
						let result = JSON.parse(ret.data);
						if (result.action === "checkStreamPlaying") {
							// code 1:指定设备流正在播放,code 0指定设备流没有播放
							resolve(result.code);
						}
						break;
					case "onclose":
						break;
					case "onerror":
						reject(0);
						unifyDealError();
						break;
				}
			});
		});
		return p;
	},
	/**
	 *设置本地播放器视频播放状态
	 * @param {Object} params 参数对象
	 * @param {String} params.type  0:实时视频 1 录像视频
	 * @param {String} params.nIndex -1 控制所有播放视频
	 * @param {String} params.status 0 播放 1暂停
	 */
	setLocalVideoPlayStatus(params) {
		this.wsObj &&
			this.wsObj.send(
				JSON.stringify({
					action: "setPlayStatus",
					status: params.status,
					nIndex: -1,
					type: params.type,
				})
			);
	},
	/**
	 * 控制播放器窗口状态
	 * @param {Object} params
	 * @param {Object} params.status 窗口状态  status=0，窗口正常模式显示；status=1，窗口最大化显示；status=2，仅最小化；status=3，隐藏到托盘并关闭在播视频（与在界面点击X一致）
	 */
	closLocalPlayer(params) {
		this.wsObj &&
			this.wsObj.send(
				JSON.stringify({
					action: "setWindowStatus",
					status: params.status || 3,
				})
			);
	},
	destroyWs() {
		this.wsObj && this.wsObj.close();
		this.wsObj = null;
	},
	/**
	 * 关闭视频
	 *
	 * @param {any} type 选择要关闭的是实时（0）还是录像（1）
	 */
	closeRelateVideo(type) {
		this.wsObj &&
			this.closeVideo(this.wsObj, {
				type: type,
			});
	},
};

export default ipcplayer;
