/*
 *
 *
 */
class DeviceConnection {
  static DEVICE_URLS = {
    infoUrl: 'ws://127.0.0.1:8899/info', // 获取外设服务信息
    fingerUrl: 'ws://127.0.0.1:8899/finger', // 指纹仪
    pensignUrl: 'ws://127.0.0.1:8899/pensign', // 手写板
    idcardUrl: 'ws://127.0.0.1:8899/idcard', // 身份证/rfid电子标签读卡器
    urineUrl: 'ws://127.0.0.1:8899/urine', // 尿检仪
    EMPUrl: 'ws://127.0.0.1:8899/credentials', //  5:雄帝读卡器
    irisUrl: 'ws://127.0.0.1:8899/iris', // 6:虹膜仪
    haixinUrl: 'ws://127.0.0.1:8899/haixin', // 7:海鑫数据
    ESSSignUrl: 'ws://127.0.0.1:8899/ESSSign', // 10: 自研签名捺印板(投屏版)
    usbUrl: 'ws://127.0.0.1:8899/webcam', // usb摄像头
    videoUrl: 'ws://127.0.0.1:8899/video', // 视频播放
    screenUrl: 'ws://127.0.0.1:21601', // 投屏视频
    ESSUrl: 'ws://127.0.0.1:8899/ESS', // 自研签名捺印板(录制签名视频)
  }

  static HEARTBEAT_INTERVAL = 30000 // 30秒心跳
  static RECONNECT_INTERVAL = 5000 // 5秒重连间隔
  static MAX_RECONNECT_ATTEMPTS = 5 // 最大重连次数

  /**
   * 创建设备连接实例
   * @param {string} deviceType 设备类型 (e.g., 'fingerUrl')
   * @param {Function} [callback] 事件回调函数
   * @param {Object} [options] 配置选项
   * @param {number} [options.reconnectInterval] 重连间隔(毫秒)
   * @param {number} [options.maxReconnectAttempts] 最大重连次数
   */
  constructor(deviceType, callback, options = {}) {
    if (!DeviceConnection.DEVICE_URLS[deviceType]) {
      throw new Error(`Invalid device type: ${deviceType}`)
    }

    this.deviceType = deviceType
    this.callback = callback
    this.ws = null
    this.reconnectCount = 0
    this.heartbeatTimer = null
    this.isManualClose = false

    // 配置选项
    this.reconnectInterval = options.reconnectInterval || DeviceConnection.RECONNECT_INTERVAL
    this.maxReconnectAttempts = options.maxReconnectAttempts || DeviceConnection.MAX_RECONNECT_ATTEMPTS

    // 状态管理
    this.state = {
      isConnected: false,
      isConnecting: false,
      lastError: null
    }

    // 事件队列
    this.eventQueue = []
  }

  /**
   * 初始化WebSocket连接
   * @returns {Promise} 连接Promise
   */
  connect() {
    return new Promise((resolve, reject) => {
      if (this.state.isConnected || this.state.isConnecting) {
        return reject(new Error('Connection already in progress'))
      }

      this.state.isConnecting = true
      this.isManualClose = false

      try {
        this.ws = new WebSocket(DeviceConnection.DEVICE_URLS[this.deviceType])

        this.ws.onopen = (event) => {
          this._handleOpen(event)
          resolve(this)
        }

        this.ws.onmessage = (event) => {
          this._handleMessage(event)
        }

        this.ws.onclose = (event) => {
          this._handleClose(event)
        }

        this.ws.onerror = (event) => {
          this._handleError(event)
          reject(event)
        }
      } catch (error) {
        this.state.isConnecting = false
        this._triggerCallback('onerror', error)
        reject(error)
      }
    })
  }

  /**
   * 处理连接打开事件
   * @param {Event} event
   */
  _handleOpen(event) {
    this.state.isConnected = true
    this.state.isConnecting = false
    this.reconnectCount = 0
    this._triggerCallback('onopen', event)
    // 桌面软件没有监听心跳逻辑
    // this._startHeartbeat()
  }

  /**
   * 处理消息事件
   * @param {MessageEvent} event
   */
  _handleMessage(event) {
    this._triggerCallback('onmessage', event)
  }

  /**
   * 处理连接关闭事件
   * @param {CloseEvent} event
   */
  _handleClose(event) {
    this.state.isConnected = false
    this.state.isConnecting = false
    this._stopHeartbeat()

    if (!this.isManualClose && this.reconnectCount < this.maxReconnectAttempts) {
      setTimeout(() => this._reconnect(), this.reconnectInterval)
      this.reconnectCount++
    }

    this._triggerCallback('onclose', event)
  }

  /**
   * 处理错误事件
   * @param {Event} event
   */
  _handleError(event) {
    this.state.lastError = event
    this.state.isConnecting = false
    this._triggerCallback('onerror', event)
  }

  /**
   * 触发回调函数
   * @param {string} eventType 事件类型
   * @param {any} data 事件数据
   */
  _triggerCallback(eventType, data) {
    if (this.callback && typeof this.callback === 'function') {
      try {
        this.callback(eventType, data)
      } catch (error) {
        console.error('Callback execution error:', error)
      }
    }
  }

  /**
   * 开始心跳检测
   */
  _startHeartbeat() {
    this._stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.state.isConnected) {
        this.send('heartbeat')
      }
    }, DeviceConnection.HEARTBEAT_INTERVAL)
  }

  /**
   * 停止心跳检测
   */
  _stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 尝试重新连接
   */
  async _reconnect() {
    if (this.state.isConnecting || this.state.isConnected) return

    try {
      await this.connect()
    } catch (error) {
      console.warn(`Reconnection attempt ${this.reconnectCount} failed`, error)
    }
  }

  /**
   * 发送数据
   * @param {string} data 要发送的数据
   * @returns {boolean} 是否发送成功
   */
  send(data) {
    if (!this.state.isConnected || !this.ws) {
      console.warn('WebSocket not connected, cannot send')
      return false
    }

    try {
      this.ws.send(typeof data === "string" ? data : JSON.stringify(data))
      return true
    } catch (error) {
      console.log(55555, error)
      this._triggerCallback('onerror', error)
      return false
    }
  }

  open() {
    return this.send(JSON.stringify('open'))
  }

  /**
   * 关闭连接
   */
  close() {
    this.isManualClose = true
    this._stopHeartbeat()

    if (this.ws) {
      try {
        this.ws.close()
      } catch (error) {
        console.error('Error closing WebSocket:', error)
      } finally {
        this.ws = null
        this.state.isConnected = false
        this.state.isConnecting = false
      }
    }
  }

  /**
   * 获取当前连接状态
   * @returns {Object} 连接状态
   */
  getConnectionState() {
    return {
      ...this.state,
      deviceType: this.deviceType,
      reconnectCount: this.reconnectCount
    }
  }
}

export default DeviceConnection
