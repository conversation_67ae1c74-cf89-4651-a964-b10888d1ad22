/* eslint-disable no-tabs */
/*
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-09-17 14:04:23
 * @Last Modified by: yyy
 * @Last Modified time: 2022-07-13 15:54:57
 */
const Device = {
  infoUrl: 'ws://127.0.0.1:8899/info', // 获取外设服务信息
  fingerUrl: 'ws://127.0.0.1:8899/finger', // 指纹仪
  pensignUrl: 'ws://127.0.0.1:8899/pensign', // 手写板
  idcardUrl: 'ws://127.0.0.1:8899/idcard', // 身份证/rfid电子标签读卡器
  urineUrl: 'ws://127.0.0.1:8899/urine', // 尿检仪
  EMPUrl: 'ws://127.0.0.1:8899/credentials', //  5:雄帝读卡器
  irisUrl: 'ws://127.0.0.1:8899/iris', // 6:虹膜仪
  haixinUrl: 'ws://127.0.0.1:8899/haixin', // 7:海鑫数据
  ESSSignUrl: 'ws://127.0.0.1:8899/ESSSign', // 10: 自研签名捺印板(投屏版)
  usbUrl: 'ws://127.0.0.1:8899/webcam', // usb摄像头
  videoUrl: 'ws://127.0.0.1:8899/video', // 视频播放
  screenUrl: 'ws://127.0.0.1:21601', // 投屏视频
  ESSUrl: 'ws://127.0.0.1:8899/ESS', // 自研签名捺印板(录制签名视频)
  /**
	 * 初始化
	 *
	 * @param {any} typeUrl fingerUrl:指纹仪 pensignUrl:手写板 idcardUrl:身份证/rfid电子标签 infoUrl:获取外设服务信息 urineUrl:尿检仪 EMPUrl:雄帝读卡器 irisUrl:虹膜仪 haixinUrl:海鑫数据 ESSSignUrl: 自研签名捺印板
	 * @param {fun} callback websoket回调
	 * @returns {any} WebSocket对象
	 */
  init(typeUrl, callback) {
    let ws = null
    try {
      ws = new WebSocket(Device[typeUrl])
    } catch (e) {
      console.log(e)
      return ws
    }
    ws.onopen = function (e) {
      callback && callback('onopen', e)
    }
    ws.onmessage = function (ret) {
      callback && callback('onmessage', ret)
    }
    ws.onclose = function (e) {
      callback && callback('onclose', e)
    }
    ws.onerror = function (e) {
      callback && callback('onerror', e, Device.unifyDealError)
    }
    return ws
  },
  unifyDealError(msg) {
    console.log(msg || '连接智能助手失败，请下载安装智能助手！', 'error')
  },
  open(ws) {
    ws.send('open')
  },
  send(ws, param) {
    ws.send(param)
  },
  read(ws) {
    ws.send('read')
  },
  stop(ws) {
    ws.send('close')
  },
  close(ws) {
    ws.close()
  }
}

export default Device
