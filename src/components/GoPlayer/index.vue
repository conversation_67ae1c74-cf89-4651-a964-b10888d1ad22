<!-- 组件须知 -->
<!-- 本组件旨在解决,处理在不同设备环境下播放视频的流程.
  -- 1.设备支持播放 H.265 视频时,使用 comGoVideo 组件直接在 web 页面进行渲染
  -- 2.当检测当前设备不支持播放 H.265 协议视频时,自动将数据流信息推送到 C端视频播放器
  -- PS. 需安装高新兴智能助手
  ---------------------------------------------------------------
  -- 目前缺失功能【较难实现，因为接口目前只能返回channelId，而非视频流】
  -- 先根据视频流或后端返回参数判断该视频的协议,再判断该视频流是否能在当前设备播放.
  -- 1.支持    ===> 使用 comGoVideo 直接在浏览器播放
  -- 2.不支持  ===> 将视频设备信息推送到 C端视频播放器后,在该播放器进行播放
  -- PS. 因为 comGoVideo 是原始封装，且代码编译后不可读，所以较难以二次开发。
  -->
<template>
  <div style="height: 100%;">
    <comGoVideo v-if="isSupportH265" :init-config="playConfig" ref="player" />
    <exePlayer v-else :init-config="playConfig" ref="player" :bvgUrl="bvgUrl" />
  </div>
</template>

<script>
import { getBspSystemParam } from '@/libs/bsp-system-param'
import { isSupportH265 } from "./utils/checkSupportH265";
import comGoVideo from "./comGoVideo/index.vue";
import exePlayer from "./ExePlayer/index.vue";
export default {
  name: "GoPlayer",
  components: {
    comGoVideo,
    exePlayer,
  },
  props: {
    playConfig: {
      type: Object,
      require: true,
      default: () => { }
    }
  },
  data() {
    return {
      isSupportH265: true,
      defaultPlayConfig: { wndNums: 1 },
      bvgUrl: ""
    };
  },
  created() {
    this.checkH265Support();
    this.preInit();
  },
  methods: {
    async checkH265Support() {
      this.isSupportH265 = await isSupportH265();
    },
    // 预初始化
    preInit() {
      getBspSystemParam('BVG_URL').then(res => {
        if (res) {
          this.bvgUrl = res;
          console.log('BVG_URL地址:', res);
        } else {
          this.$Message.info("BVG_URL 获取失败，请联系管理员。");
          this.bvgUrl = "http://**********:8089";
        }
      }).catch(error => {
        this.$Message.info(`BVG_URL 获取失败，请联系管理员。${error}`);
      })
    },
    play(playList, roomName) {
      this.$refs.player.play(playList, roomName);
    }
  },
};
</script>

<style lang="less" scoped></style>
