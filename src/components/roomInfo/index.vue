<template>
  <div class="room-cont">
	<!-- <p>总人数：{{ dataInfo.count }}</p>
	<div class="delimiter"></div>
	<p><span style="background-color: #FA4242;" class="tag">一</span><specialName :orgType="orgType" propName="riskNum1" :defaultValue="defaultValue" />：{{ dataInfo.riskNum1 }}</p>
	<div class="delimiter"></div>
	<p><span style="background-color:#FF7700;" class="tag">二</span><specialName :orgType="orgType" propName="riskNum2" :defaultValue="defaultValue" />：{{ dataInfo.riskNum2 }}</p>
	<div class="delimiter"></div>
	<p><span style="background-color: #FFBD13;" class="tag">三</span><specialName :orgType="orgType" propName="riskNum3" :defaultValue="defaultValue" />：{{ dataInfo.riskNum3 }}</p>
	<div class="delimiter"></div>
	<p><span style="background-color: #7A7C86;" class="tag">病</span>重病号：{{ dataInfo.sickCount }}</p> -->
	<ul class="room-statistic">
      <li v-for="item in statisticList" :key="item.title" class="statistic-box">
        <span class="tip" :class="item.className" v-if="item.text">{{item.text}}</span>
        <span class="name">{{item.title}}：</span>
        <span class="count">{{dataInfo[item.key]}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { specialName} from 'gs-special-name'
export default {
	name: 'roomInfo',
	components: {
        specialName
    },
	props: {
		roomInfo: {
			type: Object,
			default: () => ({})
		}
	},
	data(){
		return {
			dataInfo: {},
			orgType: localStorage.getItem('orgType'),
			defaultValue: '1',
			statisticList: [
				{
				title: "总人数",
				key: "count",
				className: "total-box",
				},
				{
				title: "一级风险",
				key: "riskNum1",
				text: "一",
				className: "risk-one",
				},
				{
				title: "二级风险",
				key: "riskNum2",
				text: "二",
				className: "risk-two",
				},
				{
				title: "三级风险",
				key: "riskNum3",
				text: "三",
				className: "risk-three",
				},
				{
				title: "重病号",
				key: "sickCount",
				text: "重",
				className: "sick",
				}
			],
			statisticCount: {},
		}
	},
	created() {
		console.log(this.$store.state.common.orgType,'this.$store.state.common.orgType');
		console.log(localStorage.getItem('orgType'),'orgType');
		console.log(this.roomInfo,'roomInfo');
		this.getRoomInfo(this.roomInfo)
	},
	mounted() {
		console.log(this.roomInfo,'roomInf1111o');
	},
	methods: {
		...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
		getRoomInfo(row) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.bsp_pam_bed_getByRoomInfo,
				params: {
					orgCode: row.org_code,
					roomId: row.room_code
				}
			}).then(res => {
				console.log(res);
				if(res.success) {
					this.dataInfo = res.data
				} else {
					this.$Modal.error({
						title:'温馨提示',
						content: res.msg || '接口操作失败！'
					})
				}
			})
		  }
	}
}
</script>

<style lang="less" scoped>
.room-cont{
	width: 100%;
	height: 100%;
	padding: 8px;
	display: flex;
	align-content: center;
	align-items: center;
	justify-content: flex-end;
	p{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #415060;
		line-height: 22px;
		.tag{
			display: inline-block;
			width: 26px;
			height: 22px;
			// padding: 6px;
			text-align: center;
			border-radius: 4px 4px 4px 4px;
			font-size: 14px;
			color: #FFFFFF;
			margin-right: 8px;
		}
	}
	.delimiter{
		width: 1px;
		height: 22px;
		background: #E4EAF0;
		margin: 0 16px;
	}
	.delimiter::before {
		content: '';
		width: 1px;
		height: 22px;
		background: #E4EAF0;
	}
}

.room-statistic{
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    .statistic-box {
      display: flex;
      align-items: center;
      position: relative;
      height: 22px;
      line-height: 22px;
      border-left: 1px solid #E4EAF0;
      font-size: 16px;
      padding: 0 16px 0 15px;
      .tip {
        display: inline-block;
        width: 26px;
        height: 21px;
        text-align: center;
        line-height: 18px;
        color: #FFFFFF;
        margin-right: 8px;
        border-radius: 4px;
        border: 1px solid rgba(255,255,255,0.4);
      }
      .name {
        color: #8D99A5;
      }
      .count {
        color: #00244A;
      }
      &:nth-child(1) {
        padding-left: 0;
        border-left: 0px;
        .name {
          color: #415060;
        }
        .count {
          color: #00244A;
        }
      }
    }
    .risk-one{
      background: #FA4242;
    }
    .risk-two{
      background: #FF7700;
    }
    .risk-three{
      background: #FFBD13;
    }
    .sick {
      background: #7A7C86;
    }
    .group {
      background: #5864E4;
    }
  }
</style>