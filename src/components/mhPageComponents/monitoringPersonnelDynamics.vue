<template>
    <!-- 监室人员动态 -->
    <div class="room-dynamics">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>监室人员动态</span>
        </p>
        <div class="room-dynamics-warp" v-if="roomData && roomData.length>0">
            <div v-for="(ele, index) in roomData" :key="index" class="room-dynamics-warp-child" @click="$router.push(`/discipline/informationManage?org_code=${ele.org_code}&room_code=${ele.room_code}`)">
                <div class="room-dynamics-flex st" style="width: 100%;">
                    <p class="room-dynamics-flex" style="width: 50%;"><img src='@/assets/homeSite/4.png'
                            style="width: 30px;" /><span class="textOverflow" :title="ele.room_name ">{{ ele.room_name }}</span></p>
                    <p class="room-dynamics-flex" style="width: 50%;"><img v-if="ele.police_info" src='@/assets/images/window/5.png'
                            style="width: 30px;" /><span class="textOverflow" :title="ele.police_info">{{ ele.police_info }}</span></p>
                </div>
                <div class="room-dynamics-flex" style="flex-wrap: wrap;">
                    <span v-for="(item, i) in ele.childrenData" :key="i + 'childrenData'" class="room-num-wrap"><a
                            class="font-line">{{
                                item.title }}</a>&nbsp;&nbsp;<a class="num font-line">{{ item.num }}</a></span>
                </div>
            </div>
        </div>
        <div class="room-dynamics-warp" v-else> <noData /></div>
        <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
</template>
<script>
import noData from "@/components/bsp-empty/index.vue"
export default {
    components:{noData},
    data() {
        return {
            spinShow: false,
            childrenData: [{ title: '羁押人数', num: 0, file: 'zyry_count' }, { title: '风险人数', num: 0, file: 'fxdj_count' }, { title: '外籍人数', num: 0, file: 'foreign_count' }, { title: '戒具使用', num: 0, file: 'jjsy_count' }, { title: '重病号', num: 0, file: 'zbh_count' }, { title: '精神异常', num: 0, file: 'jsyc_count' }, { title: '65岁以上', num: 0, file: 'age_gt65_count' }, { title: '特殊身份', num: 0, file: 'tssf_count' }],

            roomData: []
        }
    },
    mounted() {
        this.getData(this.globalAppCode + ':sy-jsrydt')
    },
    methods: {
        getData(mark) {
            let params = {
                modelId: mark,
                // condis:timeRange?`[{"name":"timeRange","op":"=","value":"${timeRange}","valueType":"string"}]`:''
            }
            this.$store.dispatch("postRequest", {
                url: this.$path.get_query_grid,
                params: params,
            }).then(res => {
                if (res.success) {
                    this.spinShow = false
                    if (res.rows && res.rows.length > 0) {
                        // console.log(res.rows, 'mark', mark)
                        res.rows.forEach(ele => {
                            let children = [{ title: '羁押人数', num: 0, file: 'zyry_count' }, { title: '风险人数', num: 0, file: 'fxdj_count' }, { title: '外籍人数', num: 0, file: 'foreign_count' }, { title: '戒具使用', num: 0, file: 'jjsy_count' }, { title: '重病号', num: 0, file: 'zbh_count' }, { title: '精神异常', num: 0, file: 'jsyc_count' }, { title: '65岁以上', num: 0, file: 'age_gt65_count' }, { title: '特殊身份', num: 0, file: 'tssf_count' }]

                            children.forEach(item => {
                                for (let i in ele) {
                                    // console.log(i,ele[i],item.file,i == item.file, '1212')
                                    if (i == item.file) {
                                        this.$set(item, 'num', ele[i])
                                        // console.log(item.num)
                                    }
                                }
                            })
                            this.$set(ele,'childrenData',children)
                            // console.log(children, 'childrenchildrenchildren')

                        })
                        this.roomData=res.rows
                        // Object.assign(this.dataList[index].children[i], res.rows[0])
                    } else {
                    }
                } else {
                    this.spinShow = false
                }
            })
        },
    }
}
</script>
<style scoped lang="less">
.room-dynamics {
    width: 100%;
    height: 100%;
    font-size: 16px;
    color: #5F709A;

    .room-dynamics-warp {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin: 16px 15px;
        font-size: 15px;
        height: calc(~'100% - 60px');
        overflow: auto;
        .room-dynamics-warp-child {
            width: 31%;
            background: linear-gradient(167deg, #FFFFFF 0%, #F0F8FF 100%);
            border-radius: 4px 4px 4px 4px;
            border: 1px solid rgba(206, 224, 240, 0.7);
            padding: 10px;
            margin: 0 16px 16px 0;
            cursor: pointer;
            max-height: 165px;
            &:hover {
                border-color: blue;
            }
        }

        .room-dynamics-warp-child:nth-of-type(3n) {
            margin-right: 0;
        }
    }
}

.room-dynamics-flex {
    display: flex;
    width: 100%;
    align-items: center;
}

.st {
    justify-content: space-between;
    border-bottom: 1px solid #CEE0F0;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.room-num-wrap {
    width: 49%;
    display: inline-block;
    margin-right: 1%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .num {
        margin-right: 8px;
        display: inline-block;
    }
}

.font-line {
    color: #5F709A;
}
</style>