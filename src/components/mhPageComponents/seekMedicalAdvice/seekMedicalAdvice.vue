<template>
  <div style="width: 100%;height: 100%;">
    <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
      <span>{{ orgType == '01' ? '风险' : '巡视' }}管控情况</span>
    <ul class="tabList">
      <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{
        item.title }}</li>
    </ul>
    </p>
    <div class="seekMedicalAdvice d-flex ai-center jc-between">
      <div class="tilePreview ">
        <!-- <div class="title">违规登记分析</div> -->
        <div class="line d-flex ai-center" style="flex-wrap: wrap;">
          <div v-for="(item, index) in optionData.dataList" :key="index" class="d-flex jc-between" style="margin-bottom: 12px;width: 48%;padding: 12px;background: linear-gradient( 195deg, #E7F6FF 0%, #B9D7FF 100%);
border-radius: 4px 4px 4px 4px;">
            <p class="d-flex"><img style="width: 24px;" :src="item.imgUrl" />&nbsp;&nbsp;<span>{{ item.title }}</span>
            </p><span style="font-weight: 700;">{{ item.value }}</span>
          </div>
          <!-- <div class="violation">
                <el-progress type="circle" :width= '150' :stroke-width="14" stroke-linecap="round" define-back-color="#DDE8FF" color="#3DCCAB"  :percentage="20"></el-progress>
              </div>
              <div class="desc_pie">
                <span class="value">60</span><span class="end">人</span>
                <div class="pie_text">一般违规数量</div>
              </div>
              <div class="violation" >
                  <el-progress type="circle"  :width= '150' :stroke-width="14" stroke-linecap="round"  define-back-color-="#DDE8FF" color="#FF7B02"   :percentage="30"></el-progress>
              </div>
              <div class="desc_pie">
                <span class="value">40</span><span class="end">人</span>
                <div class="pie_text">严重违规数量</div>
              </div> -->
        </div>

        <div class="title" style="margin-top: 10px;">所情等级分析</div>
        <div class="line_patrolControl">
          <seekUpdatesVue :optionData="optionData" />
        </div>
      </div>

      <div class="dynamics">
        <div class="title">所情来源分析</div>
        <div class="list" v-if="optionData.dynamics && optionData.dynamics.length > 0">
          <div class="caseLine" v-for="(item, i) in optionData.dynamics" :key="i">
            <span class="indexBox">{{ Number(i+1) }}</span><span class="indexTitle textOverflow" :title="item.title">{{
              item.title }}</span><Progress :percent="item.per" hide-info :stroke-color="['#42A3FE']" /><span class="indexNum">{{ item.value }}</span>
          </div>
          <!-- <div class="caseLine" v-for="(item, i) in optionData.dynamics" :key="i">
            <div class="flex-line d-flex ai-center flex-wrap">
              <span :class="['tip', item.lx]">{{ item.lx == 'xkdj' ? '巡控登记' : '违规登记' }}</span>
              <span class="desc text-ellipsis">登记人：{{ item.operator_xm ? item.operator_xm : '-' }}</span>
              <span class="time">{{ item.operator_time }}</span>
            </div>
            <div class="flex-line d-flex ai-center">
              <span class="name text-ellipsis" :title="item.context">{{ item.context }}</span>
              <span class="time"></span>
            </div>
          </div> -->
        </div>
        <noEmpty v-else />
      </div>
    </div>
    <Spin size="large" fix v-if="spinShow"></Spin>

  </div>
</template>

<script>
import { initPatrolControlEcharts, initViolationCharts } from "./seekMedicalAdvice"
import seekUpdatesVue from "./seekUpdates.vue"
import noEmpty from "@/components/bsp-empty"
export default {
  components: { seekUpdatesVue, noEmpty },
  data() {
    return {
      orgType: localStorage.getItem('orgType'),
      violationCharts_1: null,
      violationCharts_2: null,
      patrolControlCharts: null,
      sxDate: [
        { title: '今日', check: true, type: 1 },
        { title: '本周', check: false, type: 2 },
        { title: '本月', check: false, type: 3 },
        { title: '本年', check: false, type: 4 }
      ],

      type: 1,
      spinShow: false,
      optionData: {
        // 一般违规数量
        general: 60,
        // 严重违规数量
        serious: 40,
        // 巡控登记分析
        patrolControl: [
          { name: "一级风险", value: 0, per: "0%", filed: 'level1', },
          { name: "二级风险", value: 0, per: "0%", filed: 'level2', },
          { name: "三级风险", value: 0, per: "0%", filed: 'level3', },
          { name: "四级风险", value: 0, per: "0%", filed: 'level4', },

        ],
        dataList: [
          { title: '待核实', value: 0, filed: 'dhs', imgUrl: require('@/assets/homeSite/9.png') },
          { title: '待处置', value: 0, filed: 'dcz', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '待审批', value: 0, filed: 'dsh', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '已办结', value: 0, filed: 'ybj', imgUrl: require('@/assets/homeSite/9.png'), },
        ],
        dynamics: [
          { title: '视频异常报警', value: 0, filed: 'spycbj', imgUrl: require('@/assets/homeSite/9.png') },
          { title: '所情登记', value: 0, filed: 'sqdj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '人员手环报警', value: 0, filed: 'ryshbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '监室内屏报警', value: 0, filed: 'jsnpbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '监室外屏报警', value: 0, filed: 'jswpbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '周界报警', value: 0, filed: 'zjbj', imgUrl: require('@/assets/homeSite/9.png') },
          { title: '海康门禁报警', value: 0, filed: 'hkmjbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '风场门禁报警', value: 0, filed: 'fcmjbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '海康应急报警', value: 0, filed: 'hkyjbj', imgUrl: require('@/assets/homeSite/9.png'), },
          { title: '快鱼音频报警', value: 0, filed: 'kyypbj', imgUrl: require('@/assets/homeSite/9.png'), },

        ]

      }
    }
  },
  mounted() {
    // this.initEcharts();
    this.getData(this.globalAppCode + ':sy-fxgk-sqly', 'dynamics')
    this.getData(this.globalAppCode + ':sy-fxgk-sqdj', 'patrolControl')
    this.getData(this.globalAppCode + ':sy-fxgk-sqzt', 'dataList')
  },
  methods: {
    getData(mark, filed) {
      this.spinShow = true
      let params = {
        modelId: mark,
        condis: `[{"name":"timeRange","op":"=","value":"${this.type}","valueType":"string"}]`
      }
      this.$store.dispatch("postRequest", {
        url: this.$path.get_query_grid,
        params: params,
      }).then(res => {
        if (res.success) {
          this.spinShow = false
          if (filed == 'dataList' || filed == 'dynamics' || filed == 'patrolControl') {
            if (res.rows && res.rows.length > 0) {
              let total = 0
              this.optionData[filed].forEach(ele => {
                for (let i in res.rows[0]) {
                  if (i == ele.filed) {
                    total += res.rows[0][i]
                    this.$set(ele, 'value', res.rows[0][i])
                  }
                }
                this.$set(ele, 'total', total)
                this.$set(ele, 'per', total > 0 ? (Number(ele.value / ele.total).toFixed
                  (2) * 100) : 0)

              })
            }
          } else {
            console.log(res.rows, 'res.rowsres.rowsres.rows', filed)

            this.$set(this.optionData, filed, res.rows ? res.rows : [])
          }
        } else {
          this.spinShow = false
        }
      })
    },
    // 初始化图实例
    initEcharts() {
      // 初始化巡控等级分析
      initPatrolControlEcharts(this)
      // 初始化违规等级分析
      initViolationCharts(this)
    },
    changeTab(item) {

      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(this, 'type', item.type)
      this.$set(item, 'check', true)
      this.getData(this.globalAppCode + ':sy-fxgk-sqly', 'dynamics')
      this.getData(this.globalAppCode + ':sy-fxgk-sqdj', 'patrolControl')
      this.getData(this.globalAppCode + ':sy-fxgk-sqzt', 'dataList')
    },
  }
}
</script>

<style lang="less" scoped>
@import "./seekMedicalAdvice.less";
</style>