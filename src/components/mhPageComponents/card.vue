<template>
  <div class="card-com">
    <div class="home-com1" v-for="(ele, index) in dataList"
      style="padding-bottom: 10px;border-bottom: 1px solid #E9EDF5;">
      <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px 10px;border:none;">
        <span>{{ ele.title }}</span>
      <ul class="tabList" v-if="ele.select">
        <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in ele.sxDate" :key="i"
          @click="changeTab(item, ele, index, i)">{{
            item.title }}</li>
      </ul>
      </p>
      <div class="zyqk-wrap">
        <div v-for="(item, indexchildren) in ele.children" :key="indexchildren + 'indexchildren'" class="personchild"
          @click="item.drupModel ? $router.push(`/drillDown?modelId=${item.drupModel}&sfzs=${item.sfzs}&hjlx=${item.hjlx}&type=${item.curTitleYear ? item.curTitleYear : item.curTitle}&fxdj=${item.fxdj}`) : ''">
          <p class="flex-personArr"><img :src="item.imgUrl" style="width:24px" /><span class="textOverflow tit"
              :title="item.title">{{ item.title }}</span></p>
          <span class="num" v-if="index == 1">{{ ele.select ? item[item.file] : 0 }}</span>
          <span class="num" v-else-if="index == 4">{{ ele.select ?item[ele.curFlie] : 0 }}</span>
          <span class="num" v-else-if="index == 5">{{ ele.select ? item.count : (item.month_count || item.count || 0)
            }}</span>
          <span class="num" v-else>{{ (item.month_count || item.count || 0) }}</span>
        </div>
      </div>
      <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      appCode: serverConfig.APP_CODE,
      curTotal: 0,
      curTotalCs: 0,
      curN: 0,
      curL: 0,
      ljjy: 0,
      curcs: 0,
      nPercent: 0,
      sxDate: [
        { title: '今日', check: true, file: 'day_count' },
        { title: '本周', check: false, file: 'week_count' },
        { title: '本月', check: false, file: 'month_count' },
        { title: '本年', check: false, file: 'year_count' }
      ],
      orgType: localStorage.getItem('orgType'),
      personArr: [],
      dataList: [
        {
          title: '在押情况', children: [
            { title: '总人数', count: 0, imgUrl: require('@/assets/homeSite/9.png'), drupModel: 'sy-zylb', modelId: this.globalAppCode + ':sy-xzyqk-zrs', type: 'all', sfzs: '' },
            { title: '在押人数', count: 0, imgUrl: require('@/assets/homeSite/10.png'), drupModel: 'sy-zylb', modelId: this.globalAppCode + ':sy-xzyqk-zaiyars', type: 'all', sfzs: '1' },
            { title: '住院人数', count: 0, imgUrl: require('@/assets/homeSite/10.png'), drupModel: 'sy-zylb', modelId: this.globalAppCode + ':sy-xzyqk-zyrs', type: 'all', sfzs: '2' },
          ]
        },
        {
          title: '出入所情况', select: true, curFlie: 'day_count', sxDate: [
            { title: '今日', check: true, file: 'day_count', timeRange: '1' },
            { title: '本周', check: false, file: 'week_count', timeRange: '2' },
            { title: '本月', check: false, file: 'month_count', timeRange: '3' },
            { title: '本年', check: false, file: 'year_count', timeRange: '4' }
          ],
          children: [
            { title: '新入所', curTitle: '今日', month_count: 0, year_count: 0, drupModel: 'sy-sylb', file: 'rs', imgUrl: require('@/assets/homeSite/9.png'), modelId: this.globalAppCode + ':sy-xcrsqktj', type: 'all' },
            { title: '出所', curTitle: '今日', month_count: 0, year_count: 0, drupModel: 'sy-cslb', file: 'cs', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-xcrsqktj', type: 'all' },
            { title: '今年累计入所', curTitleYear: '今年', month_count: 0, year_count: 0, drupModel: 'sy-sylb', file: 'year_total_rs', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-xcrsqktj', type: 'all' },
            { title: '今年累计出所', curTitleYear: '今年', month_count: 0, year_count: 0, drupModel: 'sy-cslb', file: 'year_total_cs', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-xcrsqktj', type: 'all' },
          ]
        },
        {
          title: '风险情况', children: [
            { title: '一级风险', count: 0, drupModel: 'sy-fxdjlb', fxdj: '1', imgUrl: require('@/assets/homeSite/9.png'), modelId: this.globalAppCode + ':sy-fxry-yjfx', type: 'all' },
            { title: '二级风险', count: 0, drupModel: 'sy-fxdjlb', fxdj: '2', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-fxry-ejfx', type: 'all' },
            { title: '三级风险', count: 0, drupModel: 'sy-fxdjlb', fxdj: '3', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-fxry-sjfx', type: 'all' },
          ]
        },
        {
          title: '重点人员', children: [
            { title: '重点关注', month_count: 0, year_count: 0, drupModel: 'sy-zdry-zdgzlb', imgUrl: require('@/assets/homeSite/9.png'), modelId: this.globalAppCode + ':sy-zdry-zdgzry', type: 'all' },
            { title: '戒具使用', month_count: 0, year_count: 0, drupModel: 'sy-zdry-jjsyrylb', imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-zdry-jjsyry', type: 'all' },
            { title: '重病号', month_count: 0, year_count: 0, drupModel: 'sy-zdry-zbhrylb', imgUrl: require('@/assets/homeSite/11.png'), modelId: this.globalAppCode + ':sy-zdry-zbhry', type: 'all' },
            { title: '禁闭', month_count: 0, year_count: 0, drupModel: 'sy-zdry-jbrylb', imgUrl: require('@/assets/homeSite/12.png'), modelId: this.globalAppCode + ':sy-zdry-jbry', type: 'all' },
            { title: '单独关押', month_count: 0, year_count: 0, drupModel: 'sy-zdry-ddgyrylb', imgUrl: require('@/assets/homeSite/14.png'), modelId: this.globalAppCode + ':sy-zdry-ddgyry', type: 'all' },
            { title: '外籍人员', month_count: 0, year_count: 0, drupModel: 'sy-zdry-wjrylb', imgUrl: require('@/assets/homeSite/15.png'), modelId: this.globalAppCode + ':sy-zdry-wjry', type: 'all' },
            { title: '艾滋病', month_count: 0, year_count: 0, drupModel: 'sy-zdry-azbrylb', imgUrl: require('@/assets/homeSite/13.png'), modelId: this.globalAppCode + ':sy-zdry-azbry', type: '01' },
            { title: '重刑人员', month_count: 0, year_count: 0, drupModel: 'sy-zdry-zxrylb', imgUrl: require('@/assets/homeSite/13.png'), modelId: this.globalAppCode + ':sy-zdry-zxry', type: '01' },
            { title: '特殊身份人员', month_count: 0, year_count: 0, drupModel: 'sy-zdry-tssfrylb', imgUrl: require('@/assets/homeSite/13.png'), modelId: this.globalAppCode + ':sy-zdry-tssf', type: '01' },
            { title: '65岁以上人员', month_count: 0, year_count: 0, drupModel: 'sy-zdry-65sysrylb', imgUrl: require('@/assets/homeSite/13.png'), modelId: this.globalAppCode + ':sy-zdry-65sysry', type: '01' },
            { title: '精神异常人员', month_count: 0, year_count: 0, drupModel: 'sy-zdry-jsycrylb', imgUrl: require('@/assets/homeSite/13.png'), modelId: this.globalAppCode + ':sy-zdry-jsycry', type: '01' },
          ]
        },
        {
          title: '会见情况', select: true, curFlie: 'day_count', sxDate: [
            { title: '今日', check: true, file: 'day_count', timeRange: '1' },
            { title: '本周', check: false, file: 'week_count', timeRange: '2' },
            { title: '本月', check: false, file: 'month_count', timeRange: '3' },
            { title: '本年', check: false, file: 'year_count', timeRange: '4' }
          ],
          children: [
            { title: '提讯', curTitle: '今日', hjlx: '4', drupModel: 'sy-hjdtlb', day_count: 0, week_count: 0, month_count: 0, year_count: 0, imgUrl: require('@/assets/homeSite/9.png'), modelId: this.globalAppCode + ':sy-hjdt-tx', type: 'all' },
            { title: '提解', curTitle: '今日', hjlx: '5', drupModel: 'sy-hjdtlb', day_count: 0, week_count: 0, month_count: 0, year_count: 0, imgUrl: require('@/assets/homeSite/10.png'), modelId: this.globalAppCode + ':sy-hjdt-tj', type: 'all' },
            { title: '律师会见', curTitle: '今日', hjlx: '1', drupModel: 'sy-hjdtlb', day_count: 0, week_count: 0, month_count: 0, year_count: 0, imgUrl: require('@/assets/homeSite/11.png'), modelId: this.globalAppCode + ':sy-hjdt-lshj', type: 'all' },
            { title: '使馆领事会见', curTitle: '今日', hjlx: '2', drupModel: 'sy-hjdtlb', day_count: 0, week_count: 0, month_count: 0, year_count: 0, imgUrl: require('@/assets/homeSite/12.png'), modelId: this.globalAppCode + ':sy-hjdt-jshj', type: 'all' },
            { title: '家属会见', curTitle: '今日', hjlx: '3', drupModel: 'sy-hjdtlb', day_count: 0, week_count: 0, month_count: 0, year_count: 0, imgUrl: require('@/assets/homeSite/14.png'), modelId: this.globalAppCode + ':sy-hjdt-slghj', type: 'all' },
          ]
        },
        {
          title: '就医情况', select: true, curFlie: 'day_count', sxDate: [
            { title: '今日', check: true, file: 'day_count', timeRange: '1' },
            { title: '本周', check: false, file: 'week_count', timeRange: '2' },
            { title: '本月', check: false, file: 'month_count', timeRange: '3' },
            { title: '本年', check: false, file: 'year_count', timeRange: '4' }
          ], children: [
            { title: '出所就医', count: 0,curTitle: '今日',drupModel: 'sy-jydt-csjylb', imgUrl: require('@/assets/homeSite/41.png'), modelId: this.globalAppCode + ':sy-jydt-csjy' },
            { title: '重病号', count: 0,curTitle: '今日',drupModel: 'sy-jydt-zbhlb', imgUrl: require('@/assets/homeSite/42.png'), modelId: this.globalAppCode + ':sy-jydt-zbh' },
            { title: '长期医嘱', count: 0,curTitle: '今日',drupModel: 'sy-jydt-cqyzlb', imgUrl: require('@/assets/homeSite/43.png'), modelId: this.globalAppCode + ':sy-jydt-cqyz' },
            { title: '短期医嘱', count: 0,curTitle: '今日',drupModel: 'sy-jydt-dqyzlb', imgUrl: require('@/assets/homeSite/44.png'), modelId: this.globalAppCode + ':sy-jydt-dqyz' },
            { title: '已送', count: 0,curTitle: '今日',drupModel: 'sy-jydt-ysyplb', imgUrl: require('@/assets/homeSite/45.png'), modelId: this.globalAppCode + ':sy-jydt-ysyp' },
            { title: '未送', count: 0,curTitle: '今日',drupModel: 'sy-jydt-wsyplb', imgUrl: require('@/assets/homeSite/46.png'), modelId: this.globalAppCode + ':sy-jydt-wsyp' },
            { title: '拒服药', count: 0,curTitle: '今日',drupModel: 'sy-jydt-jfylb', imgUrl: require('@/assets/homeSite/47.png'), modelId: this.globalAppCode + ':sy-jydt-jfy' },
            { title: '缺服药视频', count: 0,curTitle: '今日',drupModel: 'sy-jydt-qfysplb', imgUrl: require('@/assets/homeSite/48.png'), modelId: this.globalAppCode + ':sy-jydt-qfysp' },
          ]
        }
      ],
      dataArr: [],
      dataAll: [],
      spinShow: false,
      yyDicList: [],
      curTitle: '今日',
      riskData: [],
      zxData: []
    }
  },
  mounted() {

    this.dataList.forEach((item, index) => {
      if (item && item.children && item.children.length > 0) {
        item.children.forEach((ele, i) => {
          // console.log(ele,ele.title)
          this.getData(ele.modelId, index, i, ele, (item.select ? '1' : ''))  //ele.title== "会见情况"?'':
        })
      }
    })
    // this.getData (this.globalAppCode+':sy-zdry-fxryfbqk',)
    // this.getData (this.globalAppCode+':sy-zdry-zxry',)
  },
  methods: {

    changeTab(item, ele, index, i) {
      ele.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(item, 'check', true)
      this.$set(ele, 'curFlie', item.file)
      // this.$set(item, 'check', true)
      if (ele && ele.children && ele.children.length > 0) {
        ele.children.forEach((child, inx) => {
          this.$set(child, 'curTitle', item.title)
          this.getData(child.modelId, index, inx, ele, ele.title == "会见情况" ? '' : item.timeRange)
        })
      }
    },

    getData(mark, index, i, ele, timeRange) {
      // this.spinShow = true
      let params = {
        modelId: mark,
        condis: timeRange ? `[{"name":"timeRange","op":"=","value":"${timeRange}","valueType":"string"}]` : ''
      }
      this.$store.dispatch("postRequest", {
        url: this.$path.get_query_grid,
        params: params,
      }).then(res => {
        if (res.success) {
          this.spinShow = false
          // console.log(res.rows, 'res.rowsres.rowsres.rowsres.rows', mark, ele.title)
          if (res.rows && res.rows.length > 0) {
            // console.log(this.dataList[index].children[i], 'this.dataList[index].children[i]', res.rows[0])
            Object.assign(this.dataList[index].children[i], res.rows[0])
          } else {
          }
        } else {
          this.spinShow = false
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie {
  width: 100%;
  height: 50%;
  margin-top: 8px;
}

.flex-personArr {
  display: flex;
  align-items: center;
  width: 80%;

  img {
    margin-right: 6px;
  }
}

.person-pie {
  width: 100%;
  display: flex;
  margin: 16px;
  padding-top: 16px;

  .w50 {
    width: 50%;
    height: 100%;

    p {}
  }
}

.dqzy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #5F709A;
  line-height: 50px;
}

.curTotal {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  color: #2B3646;
  line-height: 30px;
}

.flex-box-cs {
  display: flex;
  justify-content: space-between;
}

.curNum {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #2B3346;
  line-height: 28px;
}

/deep/ .ivu-progress-success-bg {
  background: linear-gradient(90deg, #5284E1 0%, #97B9E8 100%) !important;
}

.ljjy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #2B3346;
}

.tabList {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #CFD9F0;
  display: flex !important;
  justify-content: space-between;
  display: inline-block;

  // height: 24px;
  .tabTag {
    cursor: pointer;
    width: 44px;
    background: #fafafa;
    text-align: center;
    display: inline-block;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #4c6a99;
    //   line-height: 28px;
  }

  .active {
    background: #538ef9 !important;
    color: #fff !important;
  }
}

.yy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #2B3646;
  // line-height: 40px;
}

.yyNum {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #5F709A;
}

.zyqk-wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 16px;
  //  height: 100px;
}

.personchild {
  width: 22%;
  height: 54px;
  background: linear-gradient(195deg, #E7F6FF 0%, #B9D7FF 100%);
  border-radius: 4px 4px 4px 4px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-right: 10px;
  padding: 12px 8px;
  margin-bottom: 8px;
  cursor: pointer;

  .num {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #2B3646;
  }
}

.card-com {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 16px;
}
</style>