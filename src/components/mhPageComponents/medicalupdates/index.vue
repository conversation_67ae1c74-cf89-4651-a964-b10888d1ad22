<template>
  <div class="home-com">
    <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
      <span>就医动态</span>
    <ul class="tabList">
      <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{
        item.title }}</li>
    </ul>
    </p>
    <div class="zyqk-wrap" style="margin-bottom: 0;">
      <div class="left-cs-box">
        <p v-for="(item, index) in daysData"
          @click="item.drupModel ? $router.push(`/drillDown?modelId=${item.drupModel}&type=${item.curTitle}`) : ''"
          :key="index" class="flex-box-cs zyqk-wrap-top" style="width: 160px;"><span class="dayTitle"><img
              :src="item.url" style="width: 40px;margin-right: 8px;" />{{ item.title }}</span><span class="dayNum">{{
                item.count ? item.count:0 }}</span></p>
      </div>
      <div class="sytx">送药提醒</div>
      <div class=" flex-box-cs" style="width:48%">
        <div v-for="(item, index) in tipsData" :key="index" style="width: 160px;text-align: center;"  @click="item.drupModel ? $router.push(`/drillDown?modelId=${item.drupModel}&type=${item.curTitle}`) : ''">
          <img :src="item.url" style="width: 60px;margin-right: 8px;" />
          <p class="ys">{{ item.title }}</p>
          <p class="dayNum">{{ item.count ? item.count : 0 }}</p>
        </div>

      </div>
    </div>
    <div class="zyqk-wrap" style="height: 55%;margin-top: 0px;">
      <div style="margin-top: 10px;height: 100%;width: 100%;overflow: overlay;background:transparent" class="csqk-box">
        <medicalupdates :monthData="monthData" :itemData="barData" />
      </div>

    </div>
    <Spin size="large" fix v-if="spinShow"></Spin>

  </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'
import medicalupdates from './medicalupdates'
export default {
  components: { medicalupdates },
  data() {
    return {
      appCode: serverConfig.APP_CODE,
      curTotal: 0,
      curTotalCs: 0,
      curN: 0,
      curL: 0,
      ljjy: 0,
      curcs: 0,
      nPercent: 0,
      sxDate: [
        { title: '今日', check: false, type: 1 },
        { title: '本周', check: true, type: 2 },
        { title: '本月', check: false, type: 3 },
        { title: '本年', check: false, type: 4 }
      ],
      dataArr: [],
      dataAll: [],
      spinShow: false,
      yyDicList: [],
      daysData: [
        { title: '出所就医', num: 0, curTitle: '本周', drupModel: 'sy-jydt-csjylb', url: require('@/assets/homeSite/41.png'), moduleId: this.globalAppCode + ':sy-jydt-csjy' },
        { title: '重病号', num: 0, curTitle: '本周', drupModel: 'sy-jydt-zbhlb', url: require('@/assets/homeSite/42.png'), moduleId: this.globalAppCode + ':sy-jydt-zbh' },
        { title: '长期医嘱', num: 0, curTitle: '本周', drupModel: 'sy-jydt-cqyzlb', url: require('@/assets/homeSite/43.png'), moduleId: this.globalAppCode + ':sy-jydt-cqyz' },
        { title: '短期医嘱', num: 0, curTitle: '本周', drupModel: 'sy-jydt-dqyzlb', url: require('@/assets/homeSite/44.png'), moduleId: this.globalAppCode + ':sy-jydt-dqyz' },
      ],
      tipsData: [
        { title: '已送', num: 0, curTitle: '本周', drupModel: 'sy-jydt-ysyplb', url: require('@/assets/homeSite/45.png'), moduleId: this.globalAppCode + ':sy-jydt-ysyp' },
        { title: '未送', num: 0, curTitle: '本周', drupModel: 'sy-jydt-wsyplb', url: require('@/assets/homeSite/46.png'), moduleId: this.globalAppCode + ':sy-jydt-wsyp' },
        { title: '拒服药', num: 0, curTitle: '本周', drupModel: 'sy-jydt-jfylb', url: require('@/assets/homeSite/47.png'), moduleId: this.globalAppCode + ':sy-jydt-jfy' },
        { title: '缺服药视频', num: 0, curTitle: '本周', drupModel: 'sy-jydt-qfysplb', url: require('@/assets/homeSite/48.png'), moduleId: this.globalAppCode + ':sy-jydt-qfysp' },
      ],
      barData: [
        { name: '现场巡诊', moduleId: this.globalAppCode + ':sy-jyqkfx-xcxz' },
        { name: '所内就医', moduleId: this.globalAppCode + ':sy-jyqkfx-snjy' },
        { name: '远程问诊', moduleId: this.globalAppCode + ':sy-jyqkfx-ycwz' },
      ],
      monthData: [],//x轴
      type: 2,
    }
  },
  mounted() {
    this.daysData.forEach((item, index) => {
      this.getData(item.moduleId, index, 'daysData')
    })
    this.tipsData.forEach((item, index) => {
      this.getData(item.moduleId, index, 'tipsData')
    })
    this.barData.forEach((item, index) => {
      this.getData(item.moduleId, index, 'barData')
    })
  },
  methods: {
    changeTab(ele) {
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(this, 'type', ele.type)
      this.$set(ele, 'check', true)
      this.daysData.forEach((item, index) => {
        this.$set(item, 'curTitle', ele.title)
        this.getData(item.moduleId, index, 'daysData')
      })
      this.tipsData.forEach((item, index) => {
        this.$set(item, 'curTitle', ele.title)
        this.getData(item.moduleId, index, 'tipsData')
      })
      this.barData.forEach((item, index) => {
        this.getData(item.moduleId, index, 'barData')
      })
    },
    getData(mark, index, dataTag) {
      this.spinShow = true
      let params = {
        modelId: mark,
        condis: `[{"name":"timeRange","op":"=","value":"${this.type}","valueType":"string"}]`

      }
      this.$store.dispatch("postRequest", {
        url: this.$path.get_query_grid,
        params: params,
      }).then(res => {
        if (res.success) {
          this.spinShow = false
          this.monthData = []
          if (dataTag == 'barData') {
            this.$set(this[dataTag][index], 'data', [])
            res.rows.forEach(item => {
              this.monthData.push(item.date_display)
              this[dataTag][index].data.push(item.count)
            })
            // console.log(this.barData,this.monthData,'barDatabarDatabarDatabarDatabarData')
          } else {
            Object.assign(this[dataTag][index], res.rows[0])
          }
          // console.log(this.daysData,'this.daysData')
        } else {
          this.spinShow = false
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
.left-cs-box {
  width: 42%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .flex-box-cs {
    width: 48%;
  }
}

.sytx {
  width: 30px;
  height: 112px;
  background: #FFF9EF;
  text-align: center;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #FF7B02;
  display: flex;
  align-items: center;
}

.zyqk-wrap-pie {
  width: 100%;
  height: 50%;
  margin-top: 8px;
}

.zyqk-wrap {
  // background: #DDF5EE;
  border-radius: 4px 4px 4px 4px;
  margin: 16px 0px 16px 0;
  padding: 0 16px 8px;
  display: flex;
  justify-content: space-between;
}

.dqzy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #5F709A;
  // line-height: 50px;
}

.curTotal {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  color: #2B3646;
  line-height: 30px;
}

.flex-box-cs {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.curNum {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #2B3346;
  line-height: 28px;
}

/deep/ .ivu-progress-success-bg {
  background: linear-gradient(90deg, #5284E1 0%, #97B9E8 100%) !important;
}

.ljjy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #2B3346;
}

.tabList {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #CFD9F0;
  display: flex !important;
  justify-content: space-between;
  display: inline-block;

  // height: 24px;
  .tabTag {
    cursor: pointer;
    width: 60px;
    //   height: 24px;
    background: #fafafa;
    text-align: center;
    //   margin-top: -2px;
    display: inline-block;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #4c6a99;
    //   line-height: 28px;
  }

  .active {
    background: #538ef9 !important;
    color: #fff !important;
  }
}

.yy {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #2B3646;
  // line-height: 40px;
}

.yyNum {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #5F709A;
}

.zyqk-wrap-top {
  background: linear-gradient(167deg, #FFFFFF 0%, #F0F8FF 100%);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(206, 224, 240, 0.7);
  padding: 4px 8px;
  margin-bottom: 8px;
  margin-right: 8px;

}

.ys {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #5F709A;
}

.rb {
  margin-right: 16px;
  border-right: 1px solid #E9EDF5;
  padding-right: 16px;
}

.dayTitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #5F709A;
  display: flex;
  align-items: center;
}

.dayNum {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #2B3346;
}
</style>