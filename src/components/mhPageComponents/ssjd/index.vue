<template>
       <div class="home-com">
         <p class="home-title">诉讼阶段</p>
         <div class="ss-wrap">
            <div v-for="(item,index) in dataAll" :key="index" class="ss-wrap-box">
                 <div class="ss-wrap-left">
                    <div class="ss-wrap-left-left" style="text-align: center;">
                       <img :src="item.icon"  />
                       <p style="margin-bottom:6px">{{ item.name }}</p>
                      
                    </div>
                    <p class="ss-wrap-left-right"><span class="num-big">{{ item.num }}</span></p>
                    
                 </div>
                 <div class="ss-wrap-right" style="width: 100%;" v-if="item.children && item.children.length>2">
                    <p v-for="(ele,i) in item.children" class="bg" :key="i+'child'" v-if="ele.name" style="width: 44%;"><span class="sm-name " :title="ele.name">{{ ele.name }}</span><span class="sm-num">{{ ele.num }}&nbsp;</span></p>
                 </div>
                 <div class="ss-wrap-right" style="width: 100%;" v-else>
                    <p v-for="(ele,i) in item.children" class="bg" :key="i+'child'" v-if="ele.name" style="width: 100%;"><span class="sm-name " :title="ele.name">{{ ele.name }}</span><span class="sm-num">{{ ele.num }}</span></p>
                 </div>
            </div>
         </div>
         <Spin size="large" fix v-if="spinShow"></Spin>
        </div>
</template>

<script>
export default {
     data(){
        return{
            dataAll:[
                {name:'公安',icon:require('@/assets/images/header/sj04.png'),num:0,children:[]},
                {name:'检察院',icon:require('@/assets/images/header/sj01.png'),num:0,children:[]},
                {name:'法院',icon:require('@/assets/images/header/sj02.png'),num:0,children:[]},
                {name:'其他',icon:require('@/assets/images/header/sj03.png'),num:0,children:[]},
            ],
            spinShow:false,
            yyDicList:[],
            appCode:serverConfig.APP_CODE,
        }
     },
   mounted(){
    this.dicName ('ZD_SSJD',this.appCode)
   },
   methods:{
     dicName (dicName,appCode) {
      let name = []
      return new Promise((resolve, reject) => {
        this.$store.dispatch('axiosGetRequest', {url: '/bsp-com/static/dic/' + appCode + '/' + `${dicName}` + '.js'
      }).then(res => {
          if (res.status === 200) {
            let arr = []
            let func = { getData: eval('(' + res.data + ')') }
            arr = func.getData()
            this.yyDicList=arr
            this.getData(this.globalAppCode+':syssjdtj')
            resolve(arr)
          } else {
            this.getData(this.globalAppCode+':syssjdtj')
          }
        })
      })
    },
    getData(mark) {
        this.spinShow=true
        let params={
          modelId: mark
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,// '/bsp-com/com/form/handle/executeMultiQuery',
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
             if(res.rows && res.rows.length>0){
                this.dataAll[0].num=res.rows[0].ga_total
                this.dataAll[0].children=res.rows[0].ga_total_detail?JSON.parse(res.rows[0].ga_total_detail.value):[]
                this.dataAll[1].num=res.rows[0].jcy_total
                this.dataAll[1].children=res.rows[0].jcy_total_detail?JSON.parse(res.rows[0].jcy_total_detail.value):[]
                this.dataAll[2].num=res.rows[0].fy_total
                this.dataAll[2].children=res.rows[0].fy_total_detail?JSON.parse(res.rows[0].fy_total_detail.value):[]
                this.dataAll[3].num=res.rows[0].qt_total
                this.dataAll[3].children=res.rows[0].qt_total_detail?JSON.parse(res.rows[0].qt_total_detail.value):[]
                this.dataAll.forEach(chile=>{
                  // 过滤空对象
                  chile.children = chile.children.filter(element => {
                     if (Object.keys(element).length !== 0) {
                     return true;
                     }
                     return false;
                     });
                  chile.children.forEach((item,i)=>{
                    this.yyDicList.forEach(ele=>{
                        if(item.sshjCode==ele.code){
                            this.$set(item,'name',ele.name)
                        }
                    })
                  })
               })
               console.log(this.dataAll,'this.dataAll')
             }

            }else{
              this.spinShow=false
            }
          })
      },
   }

}
</script>

<style lang="less" scoped>
.ss-wrap{
   margin: 16px;
   display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
   .ss-wrap-box{
    width: 46%;
    min-height: 83px;
    display: flex;
    margin-bottom: 16px;
    align-content: center;
    align-items: center;
    background: linear-gradient( 193deg, #FFFFFF 0%, #F0F8FF 100%);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(206,224,240,0.7);
    padding: 15px 18px 15px 28px;
   }
}
.ss-wrap-left{
   display: flex;
   align-content: center;
   align-items: center;
   width: 34%;
   justify-content: space-between;
   border-right: 1px solid #F0F4FA;
   padding-right: 15px;
}
.num-big{
   font-family: Microsoft YaHei, Microsoft YaHei;
   font-weight: bold;
   font-size: 20px;
   color: #2B3346;
   line-height: 28px;
}
.sm-name{
   font-family: Source Han Sans CN, Source Han Sans CN;
   font-weight: 400;
   font-size: 16px;
   color: #5F709A;
   display: inline-block;
   // width: 60%;
   margin-left: 16px;

}
.sm-num{
   font-family: Source Han Sans CN, Source Han Sans CN;
   font-weight: bold;
   font-size: 16px;
   color: #2B3346;
}
.ss-wrap-right{
   display: flex;
   align-items: center;
   flex-wrap: wrap;
   justify-content: space-between;
   p{
      display: flex;
      justify-content: space-between;
   }
}
.bg{
   background: linear-gradient(195deg, #E7F6FF 0%, #E9F0FF 60%);
   margin: 6px;
   border-radius: 4px;
}
</style>