<!-- 岗位协同组件 

@props
  labelWidth: 120
  isShowModal： false


@event
  on-pushText 推送文本在组件外编辑好文本，并进行自动推送
  on-Ok 点击确定返回表单内容 

 
@data
isPostCoordination  是否岗位协同  1 是 0 否
coordinationPosts 岗位协同岗位
pushTargetIdCards 其他人员
coordinationPostsList:[
    postId:'', 岗位id
    postName:'', 岗位名称
    selectStatus:0, // 0 未选择 1 已选择
    personList:[
        {
            policeId:'',  岗位人员id
            policeName:'',  岗位人员名称
            selectStatus:0,  0 未选择 1 已选择
        }
    ]
]


-->


<template>
    <div>
        <div class="post-ollaboration">
            <Form ref="formData" :model="formData" :label-width="labelWidth" :rules="rules">
                <FormItem label="是否岗位协同" prop="isPostCoordination">
                    <RadioGroup v-model="formData.isPostCoordination" @on-change="changeIsPostCoordination">
                        <Radio label="1">是</Radio>
                        <Radio label="0">否</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem label="岗位协同" v-if="formData.isPostCoordination == '1'" prop="coordinationPosts">
                    <CheckboxGroup v-model="formData.coordinationPosts" @on-change="changecoordinationPosts">
                        <Checkbox v-for="(item, index) in coordinationPostsList" :key="item.postId"
                            :label="item.postId">{{
                                item.postName }}</Checkbox>
                    </CheckboxGroup>
                </FormItem>
                <FormItem label="推送对象"
                    v-if="formData.isPostCoordination == '1' && formData.coordinationPosts.includes('zhidingrengyuan')">
                    <user-selector v-model="formData.pushTargetIdCards" tit="用户选择" @onSelect="onSelectUs"
                        :text.sync="formData.pushTarget" returnField="idCard" :selectPost="true">
                    </user-selector>
                </FormItem>
                <FormItem label="推送内容" v-if="formData.isPostCoordination == '1'">
                    <Input placeholder="请输入推送内容" v-model="formData.pushContent"></Input>
                </FormItem>
            </Form>
        </div>
        <Modal v-model="showModel" width="40%" title="岗位协同" @on-ok="ok">
            <CheckboxGroup v-model="formData.coordinationPosts" @on-change="changePosts">
                <Row v-for="(item, index) in coordinationPostsList" :key="item.postId" type="flex" justify="center"
                    style="margin-bottom: 10px;">
                    <Col span="4">
                    <Checkbox :label="item.postId">{{
                        item.postName }}</Checkbox>
                    </Col>
                    <Col span="16" v-if="item.postId == 'zhidingrengyuan'">
                    <user-selector v-if="formData.coordinationPosts.includes('zhidingrengyuan')"
                        v-model="formData.pushTargetIdCards" tit="用户选择" @onSelect="onSelectUs"
                        :text.sync="formData.pushTarget" returnField="idCard" :selectPost="true">
                    </user-selector>
                    </Col>
                    <Col span="16" v-else>
                    <Select v-model="item.arrPoliceId" multiple @on-change="changeSelect($event, index, item)">
                        <Option v-for="item in item.personList" :value="item.policeId" :key="item.policeId">{{
                            item.policeName }}</Option>
                    </Select>
                    </Col>
                </Row>
            </CheckboxGroup>
        </Modal>
    </div>
</template>


<script>
import { userSelector } from 'gs-user-selector'
export default {
    components: {
        userSelector
    },
    props: {
        labelWidth: {     // 表头宽度
            type: Number,
            default: 120
        },
        isShowModal: {     // 是否需要弹窗
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            formData: {
                isPostCoordination: '0', // 是否岗位协同
                coordinationPosts: [], // 岗位协同选择岗位
                coordinationPostsList: [],// 岗位协同选择岗位和岗位值班人员
                pushContent: '' // 推送内容
            },
            coordinationPostsList: [],
            showModel: false,
            rules: {
                isPostCoordination: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                coordinationPosts: [
                    { required: true, message: '请选择', trigger: 'change', type: 'array' }
                ]
            }
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        getData() {
            this.$store.dispatch('getRequest', { url: this.$path.com_dutyPostSynergism, params: {} }).then(resp => {
                if (resp.success) {
                    this.coordinationPostsList = resp.data
                    this.coordinationPostsList.forEach((item, index) => {
                        this.$set(item, 'arrPoliceId', item.personList.map(item => item.policeId))
                    })
                    let other = {
                        postName: '指定人员',
                        postId: 'zhidingrengyuan',
                        selectStatus: 0,
                        personList: [],
                    }
                    this.coordinationPostsList.push(other)

                }
            }
            )
        },
        changeIsPostCoordination(val) {
            this.formData.coordinationPosts = []
            this.formData.coordinationPostsList = []
            this.formData.pushTarget = ''
            this.formData.pushTargetIdCard = ''
            this.formData.pushContent = ''
        },
        onSelectUs(data) {
            let user = data.map(item => {
                return {
                    policeName: item.name,
                    policeId: item.idCard,
                    id: item.id,
                    selectStatus: 1

                }
            })
            this.formData.coordinationPostsList.find(item => item.postId == 'zhidingrengyuan').personList = user;
        },
        changecoordinationPosts(data) {
            if (this.isShowModal) {
                this.showModel = true
            }
            this.formData.coordinationPostsList = this.coordinationPostsList.map(item => ({
                ...item,
                selectStatus: data.includes(item.postId) ? 1 : 0,
                personList: item.personList.map(person => ({
                    ...person,
                    selectStatus: data.includes(item.postId) ? ((item.arrPoliceId || []).includes(person.policeId) ? 1 : 0) : 0 // 修改人员的selectStatus
                }))
            }));
            this.$emit('on-pushText', this.formData)
        },
        changePosts(data) {
            this.formData.coordinationPostsList = this.coordinationPostsList.map(item => ({
                ...item,
                selectStatus: data.includes(item.postId) ? 1 : 0,
                personList: item.personList.map(person => ({
                    ...person,
                    selectStatus: data.includes(item.postId) ? ((item.arrPoliceId || []).includes(person.policeId) ? 1 : 0) : 0 // 修改人员的selectStatus
                }))
            }));
        },
        changeSelect() {
            let data = this.formData.coordinationPosts
            this.formData.coordinationPostsList = this.coordinationPostsList.map(item => ({
                ...item,
                selectStatus: data.includes(item.postId) ? 1 : 0,
                personList: item.personList.map(person => ({
                    ...person,
                    selectStatus: data.includes(item.postId) ? ((item.arrPoliceId || []).includes(person.policeId) ? 1 : 0) : 0 // 修改人员的selectStatus
                }))
            }));
        },
        ok() {
            console.log(this.formData, 'ok');
            this.$emit('on-Ok', this.formData)
        },
        pushText(text) {
            this.formData.pushContent = text;
        }

    }
}
</script>