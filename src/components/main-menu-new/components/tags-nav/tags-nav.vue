<template>
    <div class="tags-nav">
 
        <ul
            v-show="visible"
            :style="{left: contextMenuLeft + 'px', top: contextMenuTop + 'px'}"
            class="contextmenu"
        >
            <li
                v-for="(item, key) of menuList"
                @click="handleTagsOption(key)"
                :key="key"
            >{{item}}</li>
        </ul>
        <i class="left-btn-fff" v-if="list && list.length>12"></i>
        <div class="side-action" v-if="showSlide" @click="changeSide"><i :class="openSide?'back':'forward'" /></div>
        <div class="btn-con left-btn" v-if="list && list.length>12">
            <Button
                type="text"
                @click="handleScroll(240)"
            >
                <Icon
                    :size="20"
                    color="#323233"
                    type="ios-arrow-back"
                />
            </Button>
        </div>
        <!-- 关闭按钮 -->
        <div class="close-con">
            <Dropdown transfer @on-click="handleTagsOption" style="margin-top:7px;" >
                <i class="tab-close"></i>
                <DropdownMenu slot="list">
                    <DropdownItem name="close-all">关闭所有</DropdownItem>
                    <DropdownItem name="close-others">关闭其他</DropdownItem>
                </DropdownMenu>
            </Dropdown>
        </div>
        <i class="right-btn-fff" v-if="list && list.length>12"></i>
        <div class="btn-con right-btn" v-if="list && list.length>12">
            <Button
                type="text"
                @click="handleScroll(-240)"
            >
                <Icon
                    :size="20"
                    color="#323233"
                    type="ios-arrow-forward"
                />
            </Button>
        </div>
        <div
            class="scroll-outer"
            ref="scrollOuter"
            @DOMMouseScroll="handlescroll"
            @mousewheel="handlescroll"
        >
            <div
                ref="scrollBody"
                class="scroll-body"
                :style="{left: tagBodyLeft + 'px'}"
            >
                <transition-group name="taglist-moving-animation" >
                    <Tag
                        type="dot"
                        v-for="(item, index) in list"
                        v-if="showTitleInside(item)"
                        :class="[item.path === $route.path?'active':'']"
                        ref="tagsPageOpened"
                        :key="`tag-nav-${index}`"
                        :name="item.path"
                        :data-route-item="item"
                        @on-close="handleClose(item)"
                        @click.native="handleClick(item)"
                        :closable="item.path !== '/'+$config.homeName "
                        :color="isCurrentTag(item) ? 'primary' : 'default'"
                        @contextmenu.prevent.native="contextMenu(item, $event)"
                    ><i class="homeIcon" v-if="item.path === '/'+$config.homeName"></i>{{ showTitleInside(item) }}</Tag>
                </transition-group>
                
            </div>
        </div>
    </div>
</template>

<script>
import { showTitle, routeEqual } from "@/libs/util";
import beforeClose from "@/router/before-close";
export default {
    name: "TagsNav",
    props: {
        value: Object,
        list: {
            type: Array,
            default () {
                return [];
            },
        },
        showSlide:Boolean,
        openSideMenu:Boolean,
    },
    data () {
        return {
            tagBodyLeft: 0,
            rightOffset: 40,
            outerPadding: 4,
            contextMenuLeft: 0,
            contextMenuTop: 0,
            visible: false,
            menuList: {
                others: "关闭其他",
                all: "关闭所有",
            },
            num:0,
            openSide:this.openSideMenu,
            isMove:false
        };
    },
    computed: {
        currentRouteObj () {
            const { path, params, query } = this.value;
            return { path, params, query };
        },
    },
    methods: {
        changeSide(){
          this.openSide=!this.openSide
          this.isMove=!this.openSide
          this.$emit('changeSide',this.openSide,this.isMove)
        },
        handlescroll (e) {
            var type = e.type;
            let delta = 0;
            if (type === "DOMMouseScroll" || type === "mousewheel") {
                delta = e.wheelDelta ? e.wheelDelta : -(e.detail || 0) * 40;
            }
            // //console.log(delta,'delta')
            this.handleScroll(delta);
        },
        handleScroll (offset) {
            const outerWidth = this.$refs.scrollOuter.offsetWidth;
            const bodyWidth = this.$refs.scrollBody.offsetWidth;
            // //console.log(outerWidth,'outerWidth',Math.floor(bodyWidth,bodyWidth*0.92),this.list.length,this.tagBodyLeft)
            if(this.list && this.list.length && this.list.length>12){

            if (offset > 0) {
                // this.tagBodyLeft = Math.min(0, this.tagBodyLeft + offset);
                // this.tagBodyLeft = -125*this.num++
                // //console.log(this.tagBodyLeft,'this.tagBodyLeft')
                this.tagBodyLeft=this.tagBodyLeft>-(this.list.length-12)*130?this.tagBodyLeft-130:-(this.list.length-12)*130
            } else {
                
                // this.tagBodyLeft = 125*this.num++
                // //console.log(outerWidth < bodyWidth,'outerWidth < bodyWidth')
                if (outerWidth < bodyWidth) {
                    // //console.log(this.tagBodyLeft < -(bodyWidth - outerWidth),'this.tagBodyLeft < -(bodyWidth - outerWidth)')
                    if (this.tagBodyLeft < -(bodyWidth - outerWidth)) {
                        this.tagBodyLeft =this.tagBodyLeft;
                    } else {
                        // //console.log(this.tagBodyLeft + offset,outerWidth - bodyWidth)
                        this.tagBodyLeft = Math.max(
                            this.tagBodyLeft + offset,
                            outerWidth - bodyWidth
                        );
                    }
                } else {
                    if(this.tagBodyLeft>=0){
                        this.tagBodyLeft =0
                    }else{
                        this.tagBodyLeft = this.tagBodyLeft +130<0? this.tagBodyLeft +130:0//0;
                    }
                }
            }
            }else{
                this.tagBodyLeft =0
            }
            // //console.log(offset,'offset',this.tagBodyLeft)

        },
        handleTagsOption (type) {
            //console.log(type,'handleTagsOption',this.menuList)
            if (type.includes("all")) {
                // 关闭所有，除了home
                let res = this.list.filter(
                    (item) => item.path === this.$config.homeName
                );
                this.$emit("on-close", res, "all");
            } else if (type.includes("others")) {
                // 关闭除当前页和home页的其他页
                let res = this.list.filter((item) =>
                        routeEqual(this.currentRouteObj, item) || item.path === this.$config.homeName || (this.currentRouteObj.path.indexOf('/help/component') > -1  && item.path.indexOf('/help/component') > -1)
                );
                console.log(this.currentRouteObj,'this.currentRouteObj',res)
                // return

                this.$emit("on-close", res, "others", this.currentRouteObj);
                setTimeout(() => {
                    this.getTagElementByRoute(this.currentRouteObj);
                }, 100);
            }
        },
        handleClose (current) {
            //console.log(current,'current',this.$route.path)
            if(current.path === this.$route.path){
                this.$router.push('/'+this.$config.homeName )
            }
            if (
                current.meta &&
                current.meta.beforeCloseName &&
                current.meta.beforeCloseName in beforeClose
            ) {
                new Promise(beforeClose[current.meta.beforeCloseName]).then(
                    (close) => {
                        if (close) {
                            this.close(current);
                        }
                    }
                );
            } else {
                this.close(current);
            }
        },
        close (route) {
            let res = []
            if(route.path.indexOf('/help/component') > -1) {
                res = this.list.filter((item) => {
                    return item.path != '/help/component'
                });

            } else {
                res = this.list.filter((item) => !routeEqual(route, item));
            }
            this.$emit("on-close", res, undefined, route);
        },
        handleClick (item) {
            //  是否是首页 首页比较特殊
            let isHome = false;
            if (item.path === '/home') {
                isHome = true
            }
            this.$emit("input", item, isHome);
        },
        showTitleInside (item) {
            return showTitle(item, this);
        },
        isCurrentTag (item) {
            if(item.path === '/help/component') {
                if(this.currentRouteObj.path.indexOf('/help/component') > -1) {
                    return true
                }
            }
            return routeEqual(this.currentRouteObj, item);
        },
        moveToView (tag) {
            const outerWidth = this.$refs.scrollOuter.offsetWidth;
            const bodyWidth = this.$refs.scrollBody.offsetWidth;
            if (bodyWidth < outerWidth) {
                this.tagBodyLeft = 0;
            } else if (tag.offsetLeft < -this.tagBodyLeft) {
                // 标签在可视区域左侧
                this.tagBodyLeft = -tag.offsetLeft + this.outerPadding;
            } else if (
                tag.offsetLeft > -this.tagBodyLeft &&
                tag.offsetLeft + tag.offsetWidth <
                    -this.tagBodyLeft + outerWidth
            ) {
                // 标签在可视区域
                this.tagBodyLeft = Math.min(
                    0,
                    outerWidth -
                        tag.offsetWidth -
                        tag.offsetLeft -
                        this.outerPadding
                );
            } else {
                // 标签在可视区域右侧
                this.tagBodyLeft = -(
                    tag.offsetLeft -
                    (outerWidth - this.outerPadding - tag.offsetWidth)
                )-120;
            }
        },
        getTagElementByRoute (route) {
            this.$nextTick(() => {
                this.refsTag = this.$refs.tagsPageOpened;
                if(this.refsTag && this.refsTag.length>0){
                     this.refsTag.forEach((item, index) => {
                        if (routeEqual(route, item.$attrs["data-route-item"])) {
                            let tag = this.refsTag[index].$el;
                            this.moveToView(tag);
                            }
                        });
                    }
            });
        },
        contextMenu (item, e) {
            if (item.name === this.$config.homeName) {
                return;
            }
            this.visible = true;
            const offsetLeft = this.$el.getBoundingClientRect().left;
            this.contextMenuLeft = e.clientX - offsetLeft + 10;
            this.contextMenuTop = e.clientY - 64;
        },
        closeMenu () {
            this.visible = false;
        },
    },
    watch: {
        $route (to) {
            this.getTagElementByRoute(to);
        },
        visible (value) {
            if (value) {
                document.body.addEventListener("click", this.closeMenu);
            } else {
                document.body.removeEventListener("click", this.closeMenu);
            }
        },
        openSideMenu:{
            handler(n,o){
                this.openSide=n
            },deep:true,
            immediate:true
        }
    },
    mounted () {
        setTimeout(() => {
            this.getTagElementByRoute(this.$route);
        }, 200);
    },
};
</script>

<style lang="less" scoped>
@import "./tags-nav.less";
.homeIcon{
    background-image: url('~@/assets/images/header/home.png');
    background-position: 50% 30%;
    width: 30px;
    height: 36px;
    display: inline-block;
}
</style>
