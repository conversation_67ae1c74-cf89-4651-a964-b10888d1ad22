<template>
  <!-- 左侧菜单 -->
  <div class="side-menu-wrap-main" v-clickOutside="handleBlur">
    <Input search v-model="menuName" placeholder="搜索" class="searchBox" @change="getNewMenu" @on-search="getNewMenu" />
    <div class="side-menu-wrap" @scroll="handleScroll">
      <div v-for="(item, index) in moduleList" :key="index" :title="item.name"
        :class="[curMenu == item.id ? 'activeSide' : '', 'side-child']" @click="getMenu(item, index)">
        <img :class="item.imgPath ? 'iconImgPic' : 'iconImgw'"
          :src="item.imgPath ? item.imgPath : (curMenu == item.id ? defaultIcon_selected : defaultImg)" />
        <span>{{ formatMenuName(item.name) }}</span>
      </div>
    </div>
    <div class="side-menu-children" v-if="curMenu == curModuleObj.id && openMenu" :style="getStyle()"
      @scroll="handleScrollSecond">
      <p class="module-name detail-title" style="margin-bottom: 12px;">{{ curModuleObj.name }}</p>
      <hr class="side-menu-hr">
      <div class="side-menu-children-flex">
        <div v-for="(ele, i) in curModuleData" :key="i + 'menu'"
          :class="[secondMenu.id == ele.id ? 'activeSideSecond' : '', 'side-menu-children-box', ele.children && ele.children.length == 0 && ele.url == '/' ? 'noDisabled' : '']"
          @click="getSecondMenu(ele, i)" :title="ele.name">
          <sizeComp v-if="ele.imgPath" :type="ele.imgPath" :size="28"
            :color="secondMenu.id == ele.id ? '#fff' : '#2b5fda'" />
          <img v-else class="iconImgw"
            :src="ele.imgPath ? ele.imgPath : (secondMenu.id == ele.id ? defaultIcon_selected : defaultImg)" />
          <p style="font-size: 15px;" >{{ formatMenuName(ele.name) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import sizeComp from '@/components/common-icon/common-icon.vue'
import tem from 'template_js'
import { getToken } from '@/libs/util'
export default {
  components: { sizeComp },
  data() {
    return {
      openMenu: false,
      moduleList: [],
      orginModuleList: [],
      appCode: serverConfig.APP_CODE,
      defaultImg: require("@/assets/side/defaultIcon.png"),
      defaultIcon_selected: require("@/assets/side/defaultIcon_selected.png"),
      curMenu: "",
      curModuleId: '',
      curModuleData: [],
      curModuleObj: {},
      curindex: 0,
      scrollPx: 0,
      secondMenu: {},
      menuName: ''
    };
  },
  watch: {
    '$route': {
      handler(val) {
        let that = this
        this.$nextTick(() => {
          if (that.moduleList && that.moduleList.length > 0) {
            let data = that.findRouteParent(that.moduleList, val.path)
            that.durpData(data)
          }
        })
      },
      deep: true,
      immediate: true
    },
  },
  created() {
    this.getDataMenu();
  },
  methods: {
    findRouteParent(menuData, currentUrl) {
      let that = this
      // 递归搜索函数
      function searchNode(nodes, parent = null, level = 1) {

        for (const node of nodes) {
          // 检查当前节点是否匹配
          // console.log(this.extractPathAfterHash(node.url), this.extractPathAfterHash(currentUrl),'this.extractPathAfterHash(node.url) === this.extractPathAfterHash(currentUrl)')
          if (that.extractPathAfterHash(node.url) === that.extractPathAfterHash(currentUrl)) {
            return {
              parentNode: parent,
              currentLevel: level,
              currentNode: node
            };
          }

          // 递归检查子节点
          if (node.children && node.children.length > 0) {
            const found = searchNode(node.children, node, level + 1);
            if (found) return found;
          }
        }
        return null;
      }
      // 遍历所有模块
      for (const module of menuData) {
        // 检查模块本身
        if (that.extractPathAfterHash(module.url) === that.extractPathAfterHash(currentUrl)) {
          return {
            parentNode: null,
            currentLevel: 1,
            currentNode: module
          };
        }
        // 检查模块的子节点
        if (module.children && module.children.length > 0) {
          const result = searchNode(module.children, module, 2);
          if (result) return result;
        }
      }
      return null;
    },
    //     /**
    //  * 从含 hash (#) 的路由路径中提取 `/#` 后的路径（不包含查询参数）
    //  * @param {string} fullPath 完整路径，如 "http://192.168.3.251:999/#/app/public/model?appId=123"
    //  * @returns {string} 提取的路径，如 "/app/public/model"
    //  */
    extractPathAfterHash(fullPath) {
      if (fullPath) {
        // 1. 找到 `/#` 的位置
        const hashIndex = fullPath.indexOf('/#');
        if (hashIndex === -1) {
          // 3. 去除查询参数（通过 split('?')[0]）
          const pathWithoutQuery = fullPath.split('?')[0];
          return pathWithoutQuery;
        } else {
          // 2. 提取 `/#` 后的部分（包括 `/`）
          const pathWithQuery = fullPath.substring(hashIndex + 1); // 从 `/#` 的下一个字符开始

          // 3. 去除查询参数（通过 split('?')[0]）
          const pathWithoutQuery = pathWithQuery.split('?')[0];
          return pathWithoutQuery;
        }
      } else {
        return fullPath
      }
    },

    //点击其它区域
    handleBlur(event) {
      this.openMenu = false
    },
    getDataMenu() {
      this.$store
        .dispatch("authGetRequest", {
          url: "/bsp-uac/uac/menu/getUserMenu",
          params: { appCode: this.appCode, cascadeFilter:true },
        })
        .then((res) => {
          if (res.success) {
            this.orginModuleList = JSON.parse(JSON.stringify(res.data))
            localStorage.setItem(this.appCode+'menuList',JSON.stringify(res.data))
            // console.log(this.moduleList ,'this.moduleList =',res.data)
            this.moduleList = res.data;
            if (res.data && res.data.length > 0) {
              let data = this.findRouteParent(this.moduleList, this.$route.path)
              //  console.log(data,this.moduleList,'1212',this.$route.path)
              this.durpData(data)
              this.$store.dispatch('get_permission').then((result) => {})
            }
          } else {
            this.$Modal.error({
              title: "错误提示",
              content: res.msg,
            });
          }
        });
    },
    durpData(data) {
      // console.log(data, 'durpData')
      // return
      if (data && data.currentLevel && data.currentLevel == 2) {
        this.$set(this, 'curModuleObj', data.currentNode)
        this.$set(this, 'curMenu', data.currentNode.moduleId)
        this.$emit('getThirdMenu', {}, data.currentNode.id, false)
      } else if (data && data.currentLevel && data.currentLevel == 3) {
        this.secondMenu = data.parentNode
        this.$set(this, 'curModuleObj', data.currentNode)
        this.$set(this, 'curMenu', data.currentNode.moduleId)
        this.$emit('getThirdMenu', data.parentNode, data.currentNode.id, false)
      } else {
        this.$set(this, 'curModuleObj', '')
        this.$set(this, 'curMenu', '')
        this.$emit('getThirdMenu', {}, '', false)
      }
    },
    getNewMenu() {
      this.openMenu = false
      if (this.menuName) {
        let newMenu = this.fuzzySearchTree(this.orginModuleList, this.menuName)
        this.$set(this, 'moduleList', newMenu)
      } else {
        this.$set(this, 'moduleList', this.orginModuleList)
      }

      // console.log(newMenu,this.menuName,'newMenu')
    },
    /**
     * 模糊搜索树结构，返回包含匹配项及其所有父级的完整树
     * @param {Array} tree - 原始树结构数据
     * @param {string} keyword - 搜索关键词
     * @returns {Array} 返回过滤后的树结构（保留匹配项及其所有父级）
    */
    fuzzySearchTree(tree, keyword) {
      if (!Array.isArray(tree) || !keyword) return [];

      const lowerKeyword = keyword.toLowerCase();

      // 递归检查节点是否匹配或包含匹配的子节点
      function filterNode(node) {
        // 检查当前节点是否匹配
        const isNodeMatch = node.name && node.name.toLowerCase().includes(lowerKeyword);

        // 处理子节点
        let filteredChildren = [];
        if (node.children && node.children.length > 0) {
          filteredChildren = node.children
            .map(child => filterNode(child))
            .filter(child => child !== null);
        }

        // 如果当前节点匹配或子节点有匹配，则保留该节点
        if (isNodeMatch || filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          };
        }

        return null;
      }

      return tree
        .map(node => filterNode(node))
        .filter(node => node !== null);
    },
    getMenu(item, index) {
      this.curindex = index
      this.openMenu = !this.openMenu
      if (this.curMenu != item.id) {
        this.openMenu = true
      }
      this.curMenu = item.id;
      this.curModuleObj = item
      this.curModuleData = item.children
      // console.log( this.curModuleData,' this.curModuleData')
    },
    getStyle() {
      // console.log(this.curModuleData,'curModuleData',Number(90* this.curindex+this.scrollPx))
      let curWidth = false
      if (this.curModuleData && this.curModuleData.length > 10) {
        curWidth = true
      }
      return {
        'top': curWidth ? '0px' : ((Number(this.curindex + 1) == this.moduleList.length && this.menuName == '' && this.moduleList.length > 1) ? Number(90 * this.curindex - this.scrollPx - 200) + 'px' : ((Number(this.curindex + 1) == this.moduleList.length && this.menuName ? Number(90 * this.curindex - this.scrollPx) + 'px' : Number(90 * this.curindex - this.scrollPx) + 'px')))
        // 'background-image': `url(${imageUrl})`,
        // 'width': '200px', // 设置每个元素的宽度，可以根据需要调整
        // 'height': '150px', // 设置每个元素的高度，可以根据需要调整
        // 'margin': '10px' // 设置元素之间的间距，可以根据需要调整
      };
    },
    template(tpl, data) {
      tem.config({ sTag: '{{', eTag: '}}', escape: true })
      return tem(tpl, data)
    },
    getSecondMenu(ele, i) {
      // console.log(ele,i,'getSecondMenu')
      this.secondMenu = ele
      this.openMenu = false
      if (ele.children && ele.children.length > 0) {
        this.$emit('getThirdMenu', ele, '', true)
      } else {
        if (ele.url && ele.url.indexOf('http') > -1) {
          let req = Object.assign({}, this.$route.query)
          let temValue = { user: this.$store.getters.sessionUser, req: req, token: getToken() }
          let openUrl = this.template(ele.url, temValue)
          if (ele.openMode && ele.openMode == '0') {
            window.location.href = openUrl
          } else {
            window.open(openUrl)
          }
        } else if (ele.url && ele.url != '/') {
          if (ele.openMode && ele.openMode == '0') {
            this.$router.push(ele.url)
          } else {
            window.open('/#'+ele.url)
          }
        } else {
          this.$Message.warning('功能开发中！！')
        }
      }
    },
    // 元素滚动事件
    handleScroll(event) {
      this.scrollPx = event.target.scrollTop
      // 处理滚动事件
      //  console.log(event.target.scrollTop,'event.target.scrollTop'); // 获取滚动位置
      this.openMenu = false  //关掉二级
    },
    handleScrollSecond(event) {
      console.log(event.target.scrollTop)
    },
    // 格式化菜单名称
    formatMenuName(name) {
      if(name && name.length > 6){
        return name.substring(0, 5) + '...'
      }
      else{
        return name
      }
    }
  },
};
</script>

<style lang="less" scoped>
.module-name {}

.side-menu-wrap-main {
  background: #fff;
}

.iconImgw {
  width: 28px;
  // height: 28px;
  padding: 8px 0;
  filter: grayscale(1);
}

.iconImgPic {
  width: 50px;
  height: 50px;
}

.activeSide .iconImgw {
  filter: grayscale(0) !important;
}

.side-menu-wrap {
  height: calc(~'100vh - 110px');
  padding: 8px 8px 0;
  border-radius: 0 6px 0 0;
  background: linear-gradient(1turn, #f5f8ff, hsla(0, 0%, 100%, .9));
  overflow: overlay;

  .side-child {
    width: 100%;
    height: 90px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: #415060;
    cursor: pointer;
    font-size: 16px !important;

    // position: relative;
    &:hover {
      background: linear-gradient(47deg, rgba(230, 246, 255, 0.25), rgba(87, 155, 239, 0.25));
      box-shadow: 0 5px 15px 1px rgba(0, 51, 255, 0.33);
      border-radius: 8px 8px 8px 8px;
      transition: all 0.3s;
    }

  }
}

.side-child p {
  font-size: 16px !important;
}

.activeSideSecond {
  background: linear-gradient(47deg, rgba(230, 246, 255, 0.25), rgba(87, 155, 239, 0.25));
  box-shadow: 0 5px 15px 1px rgba(0, 51, 255, 0.33);
  border-radius: 8px 8px 8px 8px;
  transition: all 0.3s;
  color: #2250e7 !important;
}

.activeSide {
  background: linear-gradient(47deg, #2250e7, #579bef) !important;
  box-shadow: 0 5px 15px 1px rgba(0, 51, 255, 0.33);
  border-radius: 8px 8px 8px 8px;
  transition: all 0.3s;
  color: #FFF !important;
}

.side-menu-children {
  max-height: 84vh;
  position: fixed;
  top: 0;
  left: 130px;
  background: linear-gradient(105deg, hsla(0, 0%, 100%, .95), #f5f8ff 20%, hsla(0, 0%, 100%, .9));
  width: 500px;
  min-height: 180px;
  border-radius: 9px 9px 9px 9px;
  backdrop-filter: blur(50px);
  border: 1px solid #eee;
  box-shadow: 0 2px 6px 0 rgba(0, 34, 84, .12);
  z-index: 9999999;
  border-radius: 6px;
  padding: 16px;
  overflow: overlay;
  border-width: 2px;
  border-color: #538ef9;

  .side-menu-children-flex {
    display: flex;
    flex-wrap: wrap;
    max-height: 100%;
    overflow-y: auto;

    .side-menu-children-box {
      text-align: center;
      width: 24%;
      text-align: center;
      cursor: pointer;
      margin-bottom: 16px;
      padding: 6px 0;
      border: 1px solid transparent;

      &:hover {
        // background: #d5f0ff;
        background: linear-gradient(105deg, rgba(213, 240, 255, .95), rgba(229, 245, 252, .95) 20%, rgba(213, 240, 255, .9));
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #d5f0ff;
        backdrop-filter: blur(50px);
        box-shadow: 0 2px 6px 0 rgba(0, 34, 84, .12);
      }
    }
  }

}

.noDisabled {
  cursor: not-allowed !important;
  filter: grayscale(100%);
}

.searchBox {
  cursor: pointer;
  width: 90%;
  margin: 6px;
  border-radius: 10px;
  border: 2px solid #4B81FF;
}

.searchBox /deep/ .ivu-input {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15px 15px 15px 15px;
  border: rgba(255, 255, 255, 0.3);
}

.side-menu-hr {
  background-color: skyblue;
  border: none;
  height: 1px;
}
</style>
