<template>
    <div class="org-tree" id="orgTree" v-clickOutside="handleBlur">
        <!-- 机构选择 -->
        <Select v-model="appCode" style="min-width:200px" @on-change="getOrgCode" :disabled="rootData.length == 0">
            <Option v-for="item in rootData" :value="item.code" :key="item.code">{{ item.name }}</Option>
        </Select>
        <!--  -->
        <!-- <Input v-model="orgName" :disabled="rootData.length == 0" @on-focus="openTree" icon="ios-arrow-down" @on-click="openTree" />
        <Tree :data="rootData" class="tree-box" v-if="showTree" @on-select-change="select" @mouseout‌="over"></Tree> -->
    </div>
</template>

<script>
import Cookies from 'js-cookie'
import { getToken } from "@/libs/util";
export default {
    data() {
        return {
            isBuild: false,
            currentNodeId: '',
            searchTxt: '',
            loaded: false,
            selectedOrg: [],
            rootData: [],
            checkedKeys: [this.$store.state.common.orgCode],
            expandedKeys: [],
            spinShow: false,
            curOrg: this.$store.state.common.orgCode,
            orgCode: this.$store.state.common.orgCode,    //'110000113',//
            orgName: this.$store.state.common.orgName,
            appCode: serverConfig.APP_CODE,
            orgList: [],
            showTree: false,
            drupUrl: ''
        }
    },
    mounted() {
        this.getOrgData()
    },
    beforeDestroy() {
    },
    methods: {
        //点击其它区域
        handleBlur(event) {
            this.showTree = false
        },
        over() {
            this.showTree = false; // 否则，隐藏 div
        },
        openTree() {
            // console.log(this.showTree, 'this.showTree')
            this.showTree = true
        },
        select(arr, value) {
            this.orgCode = value.id
            this.orgName = value.name
            this.changeOrg()
        },
        getOrgCode(data) {
            if (this.rootData && this.rootData.length > 0) {
                this.rootData.forEach(item => {
                    if (data == item.code) {
                        this.drupUrl = item.url + '/#?singleSignOnToken=' + getToken()
                        this.orgCode = item.orgCode
                    }
                })
            }
            if (!this.orgCode) {
                return
            }
            this.$store.dispatch('postRequest', { url: '/bsp-uac/uac/rs/switchUserOrg', params: { orgCode: this.orgCode } }).then(resp => {
                if (resp.success) {
                    this.$Message.success(resp.msg || '切换成功')
                    window.open(this.drupUrl, '_self')
                }
            })
        },
        getOrgData() {
            this.spinShow = true
            this.selectedOrg = []
            this.$store.dispatch('postRequest', { url: '/bsp-uac/uac/rs/getAuthOrgs' }).then(resp => {
                this.spinShow = false

                if (resp.success && resp.data && resp.data.length > 0) {

                    resp.data.forEach(ele => {
                        ele.title = ele.name
                        if (this.orgCode == ele.id) {
                            ele.selected = true
                        }
                    })
                    this.rootData = resp.data
                    // let roots = this.transData(resp.data)
                    // if (roots && roots.length > 0) {
                    //     for (let root of roots) {
                    //         root.expanded = true
                    //     }
                    // }
                    // let arr = [...roots]
                    // this.rootData = arr
                    // console.log(this.rootData, 'this.rootData')
                    // //    this.getCurrentIds(resp.data)
                    // this.isBuild = true
                    this.$forceUpdate()
                }
            })
        },
        // 递归构造初始选中树形结构
        getNewTree(data) {
            data.forEach(item => {
                item.check = false
                if (this.checkedKeys && this.checkedKeys.length > 0 && this.checkedKeys.includes(item.id)) {
                    item.check = true
                }
                if (item.children && item.children.length > 0) {
                    item.allCheck = false
                    this.getNewTree(item.children)
                    let arr = item.children.map(e => {
                        let result = null
                        if (e.children && e.children.length > 0) {
                            result = e.check && e.allCheck
                        } else {
                            result = e.check
                        }
                        return result
                    }).concat(item.check)
                    if (arr.every(d => d)) {
                        item.allCheck = true
                    }
                }
            })
            return data
        },
        buildTreeData(datas) {
            let root = []
            let mapping = this.treeIdMapping(datas)
            datas.forEach(el => {
                if (this.checkedKeys.length > 0 && this.checkedKeys.includes(el.id)) {
                    this.selectedOrg.push(el)
                }
                if (!el.parentId || el.id === this.rootOrgId) {
                    root = el
                    return
                }
                // 用映射表找到父元素
                const parentEl = datas[mapping[el.parentId]]
                // 把当前元素添加到父元素的`children`数组中
                parentEl.children = [...(parentEl.children || []), el]
            })
            return root
        },
        transData(datas) {
            var r = [], hash = {}, id = 'id', pid = 'parentId', children = 'children', i = 0, j = 0, len = datas.length
            for (; i < len; i++) {
                hash[datas[i][id]] = datas[i]
                if (this.checkedKeys.length > 0 && this.checkedKeys.includes(datas[i].id)) {
                    this.selectedOrg.push(datas[i])
                }
            }
            for (; j < len; j++) {
                var aVal = datas[j], hashVP = hash[aVal[pid]]
                if (hashVP) {
                    !hashVP[children] && (hashVP[children] = [])
                    hashVP[children].push(aVal)
                } else {
                    r.push(aVal)
                }
            }
            return r

        },
        treeIdMapping(data) {
            return data.reduce((acc, el, i) => {
                acc[el.id] = i
                return acc
            }, {})
        },
    }
}
</script>

<style lang="less">
.org-tree {
    width: 300px;
    margin-right: 200px;
    position: absolute;
    z-index: 999;
}

.tree-box {
    width: 300px;
    position: absolute;
    height: 200px;
    background: #fff;
    overflow: auto;
    box-shadow: 0px 3px 3px 0px #ced5e5;
    border-radius: 6px;

    .ivu-tree-title,
    .ivu-tree-children {
        line-height: 30px;
        height: 30px !important;
    }
}
</style>