/**
 * BSP布局组件配置转换工具
 * 帮助开发者在不同阶段之间无缝升级
 */

/**
 * 阶段1 → 阶段2 配置转换
 * EasyLayout → SimpleDetailCardLayout
 */
export function convertStage1ToStage2(easyLayoutConfig) {
  const { leftWidth, person, cards } = easyLayoutConfig
  
  return {
    // 左侧配置转换
    leftTitle: person.title || '人员信息',
    leftIcon: person.icon || 'ios-person',
    leftWidth: leftWidth || '350px',
    data: person.data || {},
    
    // 卡片配置转换
    cards: cards.map((card, index) => ({
      title: card.title,
      icon: card.icon,
      slot: card.slot || `card-${index}`
    })),
    
    // 默认操作
    actions: [
      { name: 'save', label: '保存', type: 'primary' }
    ]
  }
}

/**
 * 阶段2 → 阶段3 配置转换
 * SimpleDetailCardLayout → DetailCardLayout
 */
export function convertStage2ToStage3(simpleConfig) {
  const { 
    leftTitle, 
    leftIcon, 
    leftWidth, 
    collapsedWidth,
    data, 
    cards, 
    actions 
  } = simpleConfig
  
  return {
    // 左侧配置转换
    leftConfig: {
      width: leftWidth || '350px',
      collapsedWidth: collapsedWidth || '60px',
      title: leftTitle,
      icon: leftIcon,
      iconColor: '#5b8ff9',
      showHeader: true,
      collapsible: true,
      data: data || {}
    },
    
    // 右侧卡片配置转换
    rightCards: cards.map(card => ({
      name: card.name || card.title.toLowerCase().replace(/\s+/g, '-'),
      title: card.title,
      icon: card.icon,
      iconColor: '#5b8ff9',
      slot: card.slot,
      showHeader: true,
      data: card.data || {},
      actions: card.actions || []
    })),
    
    // 底部操作转换
    bottomActions: actions.map(action => ({
      name: action.name,
      label: action.label,
      type: action.type || 'default',
      icon: action.icon,
      size: action.size || 'large'
    }))
  }
}

/**
 * 直接从阶段1转换到阶段3
 * EasyLayout → DetailCardLayout
 */
export function convertStage1ToStage3(easyLayoutConfig) {
  const stage2Config = convertStage1ToStage2(easyLayoutConfig)
  return convertStage2ToStage3(stage2Config)
}

/**
 * 生成升级代码模板
 */
export function generateUpgradeTemplate(fromStage, toStage, config) {
  const templates = {
    '1-2': generateStage1To2Template,
    '2-3': generateStage2To3Template,
    '1-3': generateStage1To3Template
  }
  
  const templateKey = `${fromStage}-${toStage}`
  const generator = templates[templateKey]
  
  if (!generator) {
    throw new Error(`不支持从阶段${fromStage}到阶段${toStage}的转换`)
  }
  
  return generator(config)
}

/**
 * 生成阶段1到阶段2的升级模板
 */
function generateStage1To2Template(config) {
  const converted = convertStage1ToStage2(config)
  
  return `
<!-- 升级前：EasyLayout -->
<EasyLayout left-width="${config.leftWidth || '350px'}">
  <template #left>
    <!-- 你的左侧内容 -->
  </template>
  <template #right>
    <!-- 你的右侧内容 -->
  </template>
</EasyLayout>

<!-- 升级后：SimpleDetailCardLayout -->
<SimpleDetailCardLayout
  left-title="${converted.leftTitle}"
  left-icon="${converted.leftIcon}"
  :data="person"
  :cards="cards"
  :actions="actions"
  @action="handleAction"
  @update:data="person = $event"
>
  <!-- 插槽内容完全一样，只需要加上参数 -->
  <template #left="{ data, update }">
    <!-- 原来的左侧内容，把 person 改为 data -->
  </template>
  
  <!-- 卡片插槽名字保持一样 -->
  ${converted.cards.map(card => `
  <template #${card.slot}>
    <!-- 原来 EasyCard 里的内容 -->
  </template>`).join('')}
</SimpleDetailCardLayout>

<script>
export default {
  data() {
    return {
      person: ${JSON.stringify(converted.data, null, 6)},
      cards: ${JSON.stringify(converted.cards, null, 6)},
      actions: ${JSON.stringify(converted.actions, null, 6)}
    }
  },
  methods: {
    handleAction(action) {
      if (action.name === 'save') {
        this.$Message.success('保存成功')
      }
    }
  }
}
</script>
  `.trim()
}

/**
 * 生成阶段2到阶段3的升级模板
 */
function generateStage2To3Template(config) {
  const converted = convertStage2ToStage3(config)
  
  return `
<!-- 升级前：SimpleDetailCardLayout -->
<SimpleDetailCardLayout
  left-title="${config.leftTitle}"
  :data="person"
  :cards="cards"
>

<!-- 升级后：DetailCardLayout -->
<DetailCardLayout
  :left-config="leftConfig"
  :right-cards="rightCards"
  :bottom-actions="bottomActions"
  @card-action="handleCardAction"
  @bottom-action="handleBottomAction"
>
  <!-- 插槽内容完全不变！ -->
  <template #left="{ data, updateData }">
    <!-- 完全一样的内容，updateData 对应原来的 update -->
  </template>
  
  <!-- 卡片插槽完全一样 -->
  ${converted.rightCards.map(card => `
  <template #${card.slot}>
    <!-- 完全一样的内容 -->
  </template>`).join('')}
</DetailCardLayout>

<script>
export default {
  data() {
    return {
      leftConfig: ${JSON.stringify(converted.leftConfig, null, 6)},
      rightCards: ${JSON.stringify(converted.rightCards, null, 6)},
      bottomActions: ${JSON.stringify(converted.bottomActions, null, 6)}
    }
  },
  methods: {
    handleCardAction({ action, card, index }) {
      // 更强大的事件处理
    },
    handleBottomAction({ action }) {
      // 底部操作处理
    }
  }
}
</script>
  `.trim()
}

/**
 * 生成阶段1到阶段3的升级模板
 */
function generateStage1To3Template(config) {
  const converted = convertStage1ToStage3(config)
  
  return `
<!-- 一步升级：EasyLayout → DetailCardLayout -->
<DetailCardLayout
  :left-config="leftConfig"
  :right-cards="rightCards"
  :bottom-actions="bottomActions"
>
  <!-- 插槽内容保持一样 -->
  <template #left="{ data, updateData }">
    <!-- 原来的左侧内容 -->
  </template>
  
  ${converted.rightCards.map(card => `
  <template #${card.slot}>
    <!-- 原来 EasyCard 里的内容 -->
  </template>`).join('')}
</DetailCardLayout>

<script>
export default {
  data() {
    return {
      leftConfig: ${JSON.stringify(converted.leftConfig, null, 6)},
      rightCards: ${JSON.stringify(converted.rightCards, null, 6)},
      bottomActions: ${JSON.stringify(converted.bottomActions, null, 6)}
    }
  }
}
</script>
  `.trim()
}

/**
 * 验证配置是否有效
 */
export function validateConfig(stage, config) {
  const validators = {
    1: validateStage1Config,
    2: validateStage2Config,
    3: validateStage3Config
  }
  
  const validator = validators[stage]
  if (!validator) {
    throw new Error(`不支持阶段${stage}的配置验证`)
  }
  
  return validator(config)
}

function validateStage1Config(config) {
  const errors = []
  
  if (!config.person) {
    errors.push('缺少 person 配置')
  }
  
  if (!config.cards || !Array.isArray(config.cards)) {
    errors.push('缺少 cards 配置或格式错误')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

function validateStage2Config(config) {
  const errors = []
  
  if (!config.data) {
    errors.push('缺少 data 配置')
  }
  
  if (!config.cards || !Array.isArray(config.cards)) {
    errors.push('缺少 cards 配置或格式错误')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

function validateStage3Config(config) {
  const errors = []
  
  if (!config.leftConfig) {
    errors.push('缺少 leftConfig 配置')
  }
  
  if (!config.rightCards || !Array.isArray(config.rightCards)) {
    errors.push('缺少 rightCards 配置或格式错误')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 使用示例
 */
export const examples = {
  stage1Config: {
    leftWidth: '350px',
    person: {
      title: '人员信息',
      icon: 'ios-person',
      data: { name: '张三', code: 'P001' }
    },
    cards: [
      { title: '基本信息', icon: 'ios-information', slot: 'basic-info' },
      { title: '操作功能', icon: 'ios-options', slot: 'actions' }
    ]
  },
  
  usage: `
// 使用示例
import { convertStage1ToStage2, generateUpgradeTemplate } from './configConverter'

// 转换配置
const stage2Config = convertStage1ToStage2(myStage1Config)

// 生成升级代码
const upgradeCode = generateUpgradeTemplate(1, 2, myStage1Config)
console.log(upgradeCode)
  `
}
