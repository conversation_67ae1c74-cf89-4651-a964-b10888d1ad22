<template>
  <div class="common-icon-box" :class="menuMode == 'side' ? 'w50' : 'w30'">
    <!-- <svg :xmlns="'/bsp-com/static/icon/' + appCode + '/' + iconName.replace('^custom^', '')" width="32" height="32" v-if="iconName.indexOf('^custom^') > -1 && iconName.split('.')[1]=='svg'">
      </svg>  -->
    <!-- <SvgIcon  :class-name="iconName.split('.')[0]" :icon-path="'/bsp-com/static/icon/' + appCode + '/' + iconName.replace('^custom^', '')" v-if="iconName.indexOf('^custom^') > -1 && iconName.split('.')[1]=='svg'" /> -->
    <img :src="'/bsp-com/static/icon/' + appCode + '/' + iconName.replace('^custom^', '')" alt=""
      v-if="iconName.indexOf('^custom^') > -1" />
    <component style="margin:auto" :is="iconType" :type="iconName" :color="iconColor" :size="iconSize" v-else />
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import SvgIcon from './index.vue'
import Icons from '_c/icons'
export default {
  name: 'CommonIcon',
  components: { Icons, SvgIcon },
  props: {
    type: {
      type: String,
      required: true
    },
    color: String,
    size: [Number, String]
  },
  data() {
    return {
      appCode: serverConfig.APP_CODE,
      menuMode: localStorage.getItem('menuMode') ? localStorage.getItem('menuMode') : 'side'
    }
  },
  computed: {
    iconType() {
      return this.type.indexOf('_') === 0 ? 'Icons' : 'Icon'
    },
    iconName() {
      return this.iconType === 'Icons' ? this.getCustomIconName(this.type) : this.type
    },
    iconSize() {
      return this.size || (this.iconType === 'Icons' ? 12 : undefined)
    },
    iconColor() {
      console.log(this.color,'this.colorthis.colorthis.color')
      return this.color || '#2b5fd9'
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    getCustomIconName(iconName) {
      return iconName.slice(1)
    },
    getImgData(iconName) {
      setTimeout(() => {
        this.$store.dispatch('axiosGetRequest', { url: '/bsp-com/static/icon/' + this.appCode + '/' + iconName.replace('^custom^', '') }).then(res => {
          if (res.status === 200) {
          } else {
          }
        })
      }, 400)
    }
  },
  mounted() {
    if (this.iconName.indexOf('^custom^') > -1) {
      //  this.getImgData(this.iconName) 
    }
  }
}
</script>

<style lang="less" scoped>
.w50 {
  width: 50px;
  height: 50px;
}

.w30 {
  width: 30px;
  height: 30px;
}

.common-icon-box {

  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;

  >img {
    width: 100%;
    height: 100%;
    margin: auto;
    position: relative;
    z-index: 99;
  }
}
</style>
