<template>
	<div class="bed-arrangement-container">
	  <div class="content">
		<!-- 右侧：床位布局 -->
		<div class="bed-layout">
		  <div class="tooltip-container" v-if="isPermission">
			<div 
				class="room-background"
				@dragover.prevent
				@drop="onDrop($event)"
			>
				<img v-if="roomBgImgUrl" :src="roomBgImgUrl" alt="">
				<!-- 床位区域 -->
				<div 
					v-for="(bed, index) in roomData.getByRoomId.layoutConfigs" 
					:key="bed.id"
					class="bed-area"
					:style="getBedStyle(bed)"
					>
					<div style="overflow: hidden;">
						<!-- 固定床位的宽度 -->
						<div :style="getBedStyle1(bed)" v-if="bed.bedType == '0'">
							<div 
								v-if="bed.bedList"
								v-for="(i, n) in bed.bedList"
								:key="n"
								draggable="true"
								class="bedItem"
								:style="getBedStyleBg(bed)"
							>
								<div class="bed-bg" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
										<!-- <Button>Bottom Center</Button> -->
									<div class="bed-ryInfo" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
										<img :src="i.frontPhoto" alt="" style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;">
										<p>{{ i.jgryxm ?  i.jgryxm : ' ' }}</p>
										<span>{{ i.cwh }}</span>
									</div>
								</div>
							</div>
						</div>
						<div :style="getBedStyle1(bed)" v-else>
							<div 
								v-if="bed.bedList"
								v-for="(i, n) in bed.bedList"
								:key="n"
								draggable="true"
								class="bedItem"
								:style="getBedStyleBgs(bed)"
							>
								<div class="bed-bgs" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfos">
										<img :src="i.frontPhoto" alt="" style="width: 88px; height: 84px; background: #D8D8D8; border-radius: 4px;">
										<div style="display: flex; flex-direction: column; justify-content: space-around; align-items: center; margin-left: 4px;">
											<p>{{ i.jgryxm ?  i.jgryxm : ' ' }}</p>
											<span>{{ i.cwh }}</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 可添加床位区域 -->
					<!-- <div 
						v-if="bed.isAllowedAdd"
						class="add-bed-area"
						:style="getAddBedStyle(bed)"
					>
						<div class="add-bedCont" @click="addNewBed(bed)">
							<Icon type="ios-add" size="35" color="#fff" />
							<div class="add-bed-btn">添加空床位</div>
						</div>
						<Button type="primary" @click="getJgryInfo(bed)" style="width: 100px; height: 32px;">查看全部</Button>
					</div> -->
					<!-- <div v-show="bed.bedType == '0'">
						<span v-if="hoverBedId === bed.id" class="scroll-bar scroll-bar-left" @click="leftArrow('topScrollBox')" :style="getScrollBarStyle(bed, 'left')"> <img src="@/assets/images/roomManage/left_arrow_icon.png" > </span>
          				<span v-if="hoverBedId === bed.id" class="scroll-bar scroll-bar-right"  @click="rightArrow('topScrollBox')" :style="getScrollBarStyle(bed, 'right')"> <img src="@/assets/images/roomManage/right_arrow_icon.png" alt=""> </span>
					</div>
					<div v-show="bed.bedType == '1'">
						<span v-if="hoverBedId === bed.id" class="scroll-bar scroll-bar-left" @click="topArrow('topScrollBox',bed)" :style="getScrollBarStyle(bed, 'top')"> ^ </span>
          			    <span v-if="hoverBedId === bed.id" class="scroll-bar scroll-bar-right"  @click="bottomArrow('topScrollBox',bed)" :style="getScrollBarStyle(bed, 'bottom')"> v </span>
					</div> -->
					
				</div>
			</div>
		  </div>
		  <div class="tooltip-container" v-if="!isPermission">
			<div class="room-backgrounds" @dragover.prevent @drop="onDrop($event)" :style="{ backgroundImage: `url(${perBgImage})` }" v-if="!roomData.getByRoomId.layoutConfigs[0].x1 == undefined || !roomData.getByRoomId.layoutConfigs[0].x1 == null || !roomData.getByRoomId.layoutConfigs[0].x1 == ''">
				<!-- 特殊自定义 -->
				<div class="roomBed-contentPer" style="position: relative;">
					<div class="roomBed-gridPer" :style="gridStylePer(formData,customData)">
						<div 
							v-if="customData.bedList"
							v-for="(item,index) in customData.bedList" :key="index"
							draggable="true"
							class="bedItem"
							:style="gridStyles(item,customData)"
							@dragstart="onOccupiedDragStarts($event, item, customData, index)"
							@dragover.prevent
							@drop="onBedDrops($event, item, customData, index)"
						>
							<Tooltip :content="getTooltipContent(item)" placement="bottom" :disabled="!item.enterDay && !item.riskLevelName">
								<div class="bed-bg" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfo" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" ref="tabContainer">
										<img class="close" v-if="item.jgrybm !== '' && item.jgrybm !== null" @click="closeRyInfos(item,customData)" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZNJREFUSEu9VjEvQ1EU/s5tpRNS7UtELR0kjYGJyWQTCbFbDCxI/IT+BAkTg8UuJNhMJiYGaWLooiJ9fa2ni0b7jtzWbV7r9XrC7Rvf/e73nfPdc8+5BM3nOM54g6LLBCwy8xSIkk04c4mI7hk4j3D9NJFIPPWioaAF27bHEIllAV5j5qguCCKqA3SERi1rWdZzN/abgF2pLLFHxwAGdcQBa1USvGrF42f+tQ6BUsXd9jzeBSB+Sa7gnhC0k4wP76kfbYGvyE/+QN4WIcErKpOmgPScxUDOb0vhxUZq1AqVSAC2St5HRp5JS6D8dsDsrSs2ueHq+gaZiTRmpie1Ird3D8g95jE/N9sREJE4tEaGNkiWokfRfHe1qI06ER1GVpfgepqKZXcTzPtBYeoIwgQAoi2yy+4FMy/08iGIKBQ5ACK6pKLzKm9hSme0n1DipOdhzgdAQVr0DubYT+WiRCQuJLlModYHAdMWmT9k02Vq/KIZbxV9aXZNkdagMdOu1SUzOnCUiNGR2RYxOfT9Pek/ni2fbwJmLvnex0wAAAAASUVORK5CYII=" alt="">
										<img v-if="item.jgrybm !== null" :src="item.frontPhoto" alt="" style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;">
										<img v-else style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=" alt="">
										<p style="color: #2390FF;" @click="yryd(item)">{{ item.jgryxm  }}</p>
										<span>{{ item.cwh }}</span>
									</div>
								</div>
							</Tooltip>
						</div>
					</div>
				</div>
			</div>
			<div 
				class="room-backgrounds"
				@dragover.prevent
				:style="{ backgroundImage: `url(${perBgImage})` }"
				v-else
			>
				<div class="roomBed-content">
					<!-- {{ customData.layoutColumn }}
					{{ customData.layoutRow }} -->
					<div class="roomBed-grid" :style="gridStyle(customData)">
						<div 
							v-if="customData.bedList"
							v-for="(item,index) in customData.bedList" :key="index"
							draggable="true"
							class="bedItem"
							:style="gridStyles(item,customData)"
						>
							<Tooltip :content="getTooltipContent(item)" placement="bottom" :disabled="!item.enterDay && !item.riskLevelName">
								<div class="bed-bg" :style="{ backgroundImage: `url(${bedBgImgUrl})` }">
									<div class="bed-ryInfo" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" ref="tabContainer">
										<!-- <img class="close" v-if="item.jgrybm !== '' && item.jgrybm !== null" @click="closeRyInfos(item,customData)" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAZNJREFUSEu9VjEvQ1EU/s5tpRNS7UtELR0kjYGJyWQTCbFbDCxI/IT+BAkTg8UuJNhMJiYGaWLooiJ9fa2ni0b7jtzWbV7r9XrC7Rvf/e73nfPdc8+5BM3nOM54g6LLBCwy8xSIkk04c4mI7hk4j3D9NJFIPPWioaAF27bHEIllAV5j5qguCCKqA3SERi1rWdZzN/abgF2pLLFHxwAGdcQBa1USvGrF42f+tQ6BUsXd9jzeBSB+Sa7gnhC0k4wP76kfbYGvyE/+QN4WIcErKpOmgPScxUDOb0vhxUZq1AqVSAC2St5HRp5JS6D8dsDsrSs2ueHq+gaZiTRmpie1Ird3D8g95jE/N9sREJE4tEaGNkiWokfRfHe1qI06ER1GVpfgepqKZXcTzPtBYeoIwgQAoi2yy+4FMy/08iGIKBQ5ACK6pKLzKm9hSme0n1DipOdhzgdAQVr0DubYT+WiRCQuJLlModYHAdMWmT9k02Vq/KIZbxV9aXZNkdagMdOu1SUzOnCUiNGR2RYxOfT9Pek/ni2fbwJmLvnex0wAAAAASUVORK5CYII=" alt=""> -->
										<img v-if="item.jgrybm !== null" :src="item.frontPhoto" alt="" style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;">
										<img v-else style="width: 84px; height: 88px; background: #D8D8D8; border-radius: 4px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABwCAYAAACAVlfhAAAAAXNSR0IArs4c6QAABDBJREFUeF7t3F9uElEYBfDvwkChgpXWGFJr/F+jiemDPnQBXYG7cQWuRlfgAnzQB2OisWrUWJvG2CJ/Zwozc82dpEqt2ME5X4H08NjMHO78OBfuzBSMiEgQ2NVYwkdWZEOsPev+xkdKAWNaRuRpTryHpZLZNA4zsuEzEVtLGcHN/ipgGnnjrZteMHhsrX1ApewCxpgnphsMmpzm2TGTBDf9u37fguIY40wJiu0BQbGebCjYk6AERQuA8/geSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4jg0lKFgAHMeGEhQsAI5jQwkKFgDHsaEEBQuA406kof0wlkanL1E8mX9FzeeM1CpFKXo5MN/ROHXQbhDK5nZ7YpgHh+xQV5ercqbkqaKqg77+0pTefqR6EGnD5+fycufSQtrN/2s7ddDn7/f+a2BaO92/sagVneQSFMxLUIJmE+CUz+Z3ZG+CEvSwQNpP+UI+lyy+vbwZizCMbHLSMIjiVPudioaemfPk5nJ1bMwDQYf6brst3f3wWNRTAXp75WzmMxh3RvZmq0VQJ3Dvek2MGW+q/ylnrZUXHxoEdQKoaZjm/Rr1XKNeualY2KMO8tSBuosT5WJe3Cd6Lvd7ii8vlo+dqmk22N7zf20Wxzb55Pf70aGLM6gXb2INffmpIfVzZVmsFhPISTwc7F67Lzs/fFm7ovtLIOpT3jVluI2TAD14zpMYizroMKC7ct/xB9IPrcRW9+p9zhgpekYq5cKJXKk/OM4TAW37A/m660snOH7hrdHgSsmTi0tlqZYLGvGHMlVB3drwy/eefGvuqx9Imie4sDAnl87PZ17z/uu5VEE/7HSS8+xpeixWinKtXlEbkhroTiOQrd2e2sCzBK8szUu9VsoSMXJfFdAwiuXV5+bE73SOOmp3B/Tu5QXxFJZxKqA7DV+2dn8vslWqkDF0Zaks9RrmhGJ4KCqgb7+2pO1P5hM9rXO17Mmti/jfTVQBffmxIYNId52ZFm7UdoW8kbWr+LMmFdA0FymygiD21zivJyjilRnKIChBcQKc8jjLJImgBAULgOPYUIKCBcBxM9lQ1A04lOXwjbyZBNUYdBbc4bM4jbGpL+w1Bn3qQIen1TRPeY2xqTQ0S4NmfV+Cgl9BghIULACOY0MJChYAx7GhBAULgOPYUIKCBcBxbChBwQLgODaUoGABcBwbSlCwADiODSUoWAAcx4YSFCwAjmNDCQoWAMexoQQFC4Dj2FCCggXAcWwoQcEC4Dg2lKBgAXAcG0pQsAA4znSDQVOsxX/PGTzQmYgzpmV6weCxtfbBTAx4ygdpjHligsCuRjZ8JmLxXx6fcgDs8Ewjb7z15AfoHGos4SMrssHpPyazm+YiT3PiPSyVzOZPEanYnwm7hbcAAAAASUVORK5CYII=" alt="">
										<p>{{ item.jgryxm  }}</p>
										<span>{{ item.cwh }}</span>
									</div>
								</div>
							</Tooltip>
						</div>
					</div>
					<!-- <Grid :col="customData.layoutColumn" v-for="(item,index) in customData.bedList" :key="index">
						<GridItem>{{item.cwh}}</GridItem>
					</Grid> -->
					<!-- <Grid :col="4">
						<GridItem>1</GridItem>
						<GridItem>2</GridItem>
						<GridItem>3</GridItem>
						<GridItem>4</GridItem>
						<GridItem>5</GridItem>
						<GridItem>6</GridItem>
						<GridItem>7</GridItem>
						<GridItem>8</GridItem>
					</Grid> -->
				</div>
				<!-- <img v-if="BgImgUrl" style="width: 100%; height: 100%;" :src="BgImgUrl" alt=""> -->
				<!-- 床位区域 -->
				
			</div>
		  </div>
		</div>
	  </div>

	    <!-- 自动床位配置 -->
	    <Modal
			v-model="automaticBedModal"
			:mask-closable="false"
			:closable="false"
			width="860"
		>
			<div class="flow-modal-title" slot="header">
				<span style="font-size: 17px;">自动床位规则</span>
				<span @click="automaticBedCancel" style="position: absolute; right: 6px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="select-use" style="color: #2b2b2b;">
				<RadioGroup v-model="vertical" vertical v-for="(e,i) in cwglList" :key="i">
					<Radio :label="e.code">
						<span>{{ e.name }}</span>
					</Radio>
				</RadioGroup>
				<p style="line-height: 35px; position: absolute; bottom: 0; color: #8D99A5;">注：提交后将重新对当前监室自动安排床位</p>
			</div>
			<div slot="footer">
				<Button type="primary" @click="automaticBedOk(roomData.rowData)" class="save">确 定</Button>
				<Button @click="automaticBedCancel" class="save">取 消</Button>
			</div>
	    </Modal>

		<!-- 床位布局配置管理 -->
	    <Modal
			v-model="bedInformationModal"
			:mask-closable="false"
			:closable="false"
			class-name="select-use-modals"
			width="1100"
		>
			<div class="flow-modal-title" slot="header">
				<span style="font-size: 17px;">床位信息配置</span>
				<span @click="bedInformatioCancel" style="position: absolute; right: 6px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="bed-config" style="color: #2b2b2b;">
				<Form ref="formData" :model="formData" :label-width="120" :label-colon="true" >
					<Row>
						<Col span="4" style="font-size: 16px; color: #00244A; font-family: MicrosoftYaHei, MicrosoftYaHei; text-align: center;">床位布局类型</Col>
						<Col span="20">
							<div style="width: 100%;height: 450px; overflow-y: auto; display: flex; flex-direction: row; flex-wrap: wrap; ">
								<div v-for="(item,index) in orgBedList" :key="index" style="width: 17%;">
									<div class="orgBed-class" :class="{ 'active-border': activeItem === item.id }" @click="getBedInfo(item)">
										<img :src="item.layoutImageUrl" alt="">
										<span>{{ item.layoutName }}</span>
									</div>
								</div>
							</div>
						</Col>
					</Row>
					<Row style="margin-top: 20px">
						<Col span="4" style="font-size: 16px; color: #00244A; font-family: MicrosoftYaHei, MicrosoftYaHei; text-align: center;">床位规格</Col>
						<Col span="20" style="display: flex; flex-direction: column;">
							<div style="display: flex; flex-direction: column;">
								<span style="font-size: 12px; color: #D8D8D8;">长x宽x高</span>
								<p>
									<input v-model="formData.bedSpaceLength" type="text" class="bedSize">*<input v-model="formData.bedSpaceWidth" type="text" class="bedSize">*<input type="text" v-model="formData.bedSpaceHeight" class="bedSize" style="border-radius: 4px 0px 0px 4px;"><span class="dw">cm</span>
								</p>
							</div>
							<div style="display: flex; flex-direction: column;">
								<span style="font-size: 12px; color: #D8D8D8;">称重</span>
								<p><input type="text" v-model="formData.bedSpaceBearing" class="zl" style="width: 180px; height: 36px; border-color: #1890ff; border-radius: 4px 0px 0px 4px;"><span class="dw">kg</span></p>
							</div>
						</Col>
					</Row>
				</Form>
			</div>
			<div slot="footer">
				<Button @click="bedInformatioCancel" class="save">取 消</Button>
				<Button type="primary" @click="bedInformatioOk" class="save">确 定</Button>
			</div>
	    </Modal>

	</div>
  </template>
  
  <script>
   import { mapActions } from 'vuex'
  export default {
	props: {
	//   modalTitle: String,
	  roomDatas: Object
	},
	data() {
	  return {
		// roomBgImgUrl: require('@/assets/images/roomManage/室内图（不含床）@2x.png'),
		roomBgImgUrl: '',
		unassignedPersons: [],
		bedLayouts: [],
		rowData: {},
		currentDragPerson: null,
		currentDragBed: null,
		modalTitle: '',
		curTab: 1,
		bedBgImgUrl: require('@/assets/images/roomManage/单个床.png'),
		BgImgUrl: require('@/assets/images/roomManage/围墙.png'),
		automaticBedModal: false,
		vertical: '01',
		bedInformationModal: false,
		bedRyInfonModal: false,
		isHover: false,
		hoverBedId: null,
		showTooltip: null,
		cwglList: [],
		orgBedList: [],
		formData: {},
		activeItem: null, 
		roomData: {},
		scrollStates: {}, // 存储每个床位的滚动状态
		dragState: {
			sourceBed: null,
			sourceIndex: null,
			sourceItem: null
		},
		isCustom: false,
		isPermission: false,
		customData:{},
		orgCode: this.$store.state.common.orgCode,
		enableBorrowApprove: '0',
		perBgImage: ''
	  }
	},
	watch:{
		roomDatas: {
			handler(val){
				if(val) {
					this.roomData = this.roomDatas
					this.initData()
				}
			},
			deep: true,
			immediate: true // 立即执行
		}
	},
	created() {
		// console.log(this.roomData ,'roomData111');
	//   this.initData();
	},
	computed: {
		
	},

	methods: {
	  ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
	  initData() {
		// 从props获取数据
		if(this.roomData.rowData){
			this.modalTitle = this.roomData.rowData.room_name
			this.rowData = this.roomData.rowData
		}

		if (this.roomData.getByRoomId) {
			if(this.roomData.getByRoomId.layoutType == '02'){
				this.isPermission = false
				console.log('自定义');
				this.customData = this.roomData.getByRoomId?this.roomData.getByRoomId.layoutConfigs[0]:{}
				this.formData.layoutRow = this.roomData.getByRoomId.layoutRow
				this.formData.layoutColumn = this.roomData.getByRoomId.layoutColumn
				this.perBgImage = this.roomData.getByRoomId.layoutUrl
			} else if(this.roomData.getByRoomId.layoutType == '01') {
				this.isPermission = true
				console.log('已有');
			}
		  this.roomBgImgUrl = this.roomData.getByRoomId ? this.roomData.getByRoomId.layoutUrl : '';
		// this.roomBgImgUrl = 'http://172.26.0.22:9010/dam/%E5%AE%A4%E5%86%85%E5%9B%BE%EF%BC%88%E4%B8%8D%E5%90%AB%E5%BA%8A%EF%BC%89.png'
		  this.bedLayouts = this.roomData.getByRoomId ?this.roomData.getByRoomId.layoutConfigs : [];
		}
		
		if (this.roomData.prisoner && this.roomData.prisoner.notPlan) {
		//   this.unassignedPersons = this.roomData.prisoner.filter(p => !p.bedId);
			this.unassignedPersons = this.roomData.prisoner.notPlan
			console.log(this.unassignedPersons);
		}
		if (this.roomData.getByRoomId && this.roomData.getByRoomId.layoutConfigs) {
			this.roomData.getByRoomId.layoutConfigs.forEach(bed => {
				this.$set(this.scrollStates, bed.id, {
					offset: 0,
					maxOffset: 0
				});
			});
		}
	  },
	  tabChange(val) {
	    this.curTab = val;
	  },
	  
	  // 获取床位样式
	  getBedStyle(bed) {
		// console.log(bed,bed.y1,bed.x1,'bed');
		const style = {
		  position: 'absolute',
		  display: 'flex',
		  top: `${bed.y1}px`,
		  left: `${bed.x1}px`,
		//   height: '168px',
		//   overflow: 'hidden'
		};
		if(bed.bedType == '0') {
			style.width = 116 * bed.entityBedCount + 'px'
			style.height = '168px'
		} else if (bed.bedType == '1') {
			style.width = '184px'
			style.height = 116 * bed.entityBedCount + 'px'
		}
		// 根据bedType设置方向
		// if (bed.bedType === '1') { // 竖向排列
		// //   style.width = '168px';
		//   style.height = 168 * bed.entityBedCount + 'px',
		//   style.flexDirection= 'column'
		// }
		
		return style;
	  },
	  getBedStyle1(bed){
		const style = {
			display: 'flex',
			flexDirection: bed.bedType == '0' ? 'row' : 'column',
			position: 'relative'
		}
		if(bed.bedType == '0') {
			style.width = 116 * bed.entityBedCount + 'px'
			style.height = '168px'
		} else if (bed.bedType == '1') {
			style.width = '184px'
			style.height = 116 * bed.entityBedCount + 'px'
		}
		return style
	  },
	  getBedStyle2(bed){
		console.log(this.bedBgImgUrl,'this.bedBgImgUrl');
		const style = {
			width: '116px',
			height: '168px',
			// backgroundImage: `url(${this.bedBgImgUrl})`,
			// backgroundImage: `url(${require('@/assets/images/roomManage/单个床 (1).png')})`,
			// backgroundSize: '100% 100%',
			// backgroundRepeat: 'no-repeat'
		}
		return style;
	  },
	  
	  getBedStyleBg(bed){
		const style = {
			width: '116px',
			height: '168px',
			// backgroundImage: `url(${require('@/assets/images/roomManage/单个床.png')})`,
			// backgroundRepeat: 'no-repeat'
		}
		return style
	  },
	  getBedStyleBgs(bed){
		const style = {
			width: '184px',
			height: '116px',
			// backgroundImage: `url(${require('@/assets/images/roomManage/单个床.png')})`,
			// backgroundRepeat: 'no-repeat'
		}
		return style
	  },
		getScrollBarStyle(bed, side) {
			const baseStyle = {
			display: this.isHover ? 'block' : 'none',
			position: 'absolute',
			width: '32px',
			height: '50px',
			color: '#fff',
			background: 'rgba(0, 0, 0, 0.4)',
			cursor: 'pointer',
			'z-index': 3,
			'text-align': 'center',
			'line-height': '50px',
			'border-radius': '4px',
			transform: 'translateY(-50%)',
			};

			if (bed.bedType === '1') {
			// 竖向排列，滚动条定位在top和bottom
			if (side === 'top') {
				const style = {
					...baseStyle,
					top: '0',
					left: '50%',
					transform: 'translateX(-50%)',
					'border-radius': '4px 4px 0 0',
					height: '32px',
					width: '50px',
					'line-height': '32px',
				}
				if(bed.isAllowedAdd && bed.addLocation == 'top') {
					style.top = '-116px'
				}
				return style;
			} else if (side === 'bottom') {
				const style = {
					...baseStyle,
					bottom: '0',
					left: '50%',
					transform: 'translateX(-50%)',
					'border-radius': '0 0 4px 4px',
					height: '32px',
					width: '50px',
					'line-height': '32px',
				}
				if(bed.isAllowedAdd && bed.addLocation == 'bottom') {
					style.bottom = '-116px'
				} else {
					console.log('bottom');
					// style.bottom = '-116px'
					style.marginTop = '200px'
					// style.backgroundColor = '#f00'
				}
				return style;
			}
			} else {
				// 横向排列，滚动条定位在left和right
				if (side === 'left') {
					// console.log(bed.isAllowedAdd,'先判断左边 如果是左边的话，判断左边有没有添加空床位');
					// console.log(bed.addLocation == 'left','看看空床位有没有在左边');
					const style = {
						...baseStyle,
						left: '0',
						top: '50%',
						'border-radius': '0 4px 4px 0',
					}
					if(bed.isAllowedAdd && bed.addLocation == 'left') {
						style.left = '-116px'
					}
					return style;
				} else if (side === 'right') {
					const style = {
						...baseStyle,
						right: '0',
						top: '50%',
						'border-radius': '4px 0 0 4px',
					}
					if(bed.isAllowedAdd && bed.addLocation == 'right') {
						style.right = '-116px'
					}
					return style;
				}
			}
		},
		getTooltipStyle(bed) {
			// 根据床位位置计算Tooltip应该出现的位置
			return {
			position: 'absolute',
			top: `${bed.y1 + 168}px`, // 调整这个值
			left: `${bed.x1}px`,
			zIndex: 999
			};
		},

	  // 获取可添加床位区域样式
	  getAddBedStyle(bed) {
		const style = {
		  position: 'absolute',
		  width: '116px',
		  height: '168px',
		  backgroundImage: `url(${this.bedBgImgUrl})`
		};

		if(bed.addLocation == 'left') {
			style.left =  '-116px'
		} else if(bed.addLocation == 'right') {
			style.right =  '-116px'
		} else if(bed.addLocation == 'top') {
			style.top =  '-168px'
		} else if(bed.addLocation == 'bottom') {
			style.bottom =  '-168px'
		}
		
		// if (bed.bedType === '0') { // 横向排列
		//   style.left = '116px';
		//   style.top = '0';
		// } else { // 竖向排列
		//   style.top = '116px';
		//   style.left = '0';
		//   style.width = '168px';
		//   style.height = '116px';
		// }
		
		return style;
	  },

	  gridStyle(data){
		return {
			'display': 'grid',
			'grid-template-rows': `repeat(${data.layoutRow}, 168px)`,
			'grid-template-columns': `repeat(${data.layoutColumn}, 116px)`,
			'justify-items': 'center',
			'justify-content': 'center'
		}
	  },
	  gridStylePer(data,customData){
			return {
				'display': 'grid',
				'grid-template-rows': `repeat(${data.layoutRow}, 168px)`,
				'grid-template-columns': `repeat(${data.layoutColumn}, 116px)`,
				'justify-items': 'left',
				'justify-content': 'left',
				'position': 'absolute',
				'top': `${customData.y1}`+'px',
				'left': `${customData.x1}`+'px'
			}
		},
	  gridStyles(item) {
		// console.log(item,'item');
		return {
			'width': '116px',
			'height': '168px'
		}
	  },
	  
	  // 拖拽开始
	  onDragStart(event, person) {
		this.currentDragPerson = person;
		event.dataTransfer.setData('text/plain', person.id);
	  },
	  
	  // 拖拽到床位区域
	  onBedDrop(event, bed) {
		if (!this.currentDragPerson || !bed.isAllowedAdd) return;
		
		// 分配人员到床位
		this.assignPersonToBed(this.currentDragPerson, bed);
		this.currentDragPerson = null;
	  },
	  
	  // 拖拽到房间背景（取消分配）
	  onDrop(event) {
		if (this.currentDragBed) {
		  this.removePerson(this.currentDragBed);
		  this.currentDragBed = null;
		}
	  },
	   getTooltipContent(item) {
		// console.log(item,'tooltip');
		if(!item.enterDay && item.riskLevelName) {
			return;
		}
		
		let content = '';
		if (item.riskLevel) {
			content += `风险等级: ${this.getRiskName(item.riskLevel)}`;
		}
		content += `入监天数: ${item.enterDay || '-'}`;
		return content;
	   },
	  
	  // 已分配床位拖拽开始
	  onOccupiedDragStart(event, bed) {
		this.currentDragBed = bed;
		event.dataTransfer.setData('text/plain', bed.id);
	  },
	  
	  // 分配人员到床位
	  assignPersonToBed(person, bed) {
		// 从原床位移除（如果已分配）
		const prevBedIndex = this.bedLayouts.findIndex(b => b.cwhList && b.cwhList.id === person.id);
		if (prevBedIndex >= 0) {
		  this.bedLayouts[prevBedIndex].cwhList = null;
		}
		
		// 添加到新床位
		bed.cwhList = {
		  id: person.id,
		  name: person.name
		};
		
		// 从未分配列表移除
		// this.unassignedPersons = this.unassignedPersons.filter(p => p.id !== person.id);
	  },
	  
	  // 移除人员
	  removePerson(bed) {
		if (!bed.cwhList) return;
		
		// 添加到未分配列表
		this.unassignedPersons.push({
		  id: bed.cwhList.id,
		  name: bed.cwhList.name
		});
		
		// 从床位移除
		bed.cwhList = null;
	  },
	  
	  // 添加新床位
	  addNewBed(bed) {
		if (!bed.isAllowedAdd) return;
		
		// 创建新床位配置
		const newBed = {
		  id: `new-${Date.now()}`,
		  x1: bed.bedType === '0' ? (parseFloat(bed.x1) + 116) : bed.x1,
		  y1: bed.bedType === '1' ? (parseFloat(bed.y1) + 116) : bed.y1,
		  x2: bed.x2,
		  y2: bed.y2,
		  bedType: bed.bedType,
		  isAllowedAdd: false, // 新增床位不能再添加
		  cwhList: null
		};
		
		this.bedLayouts.push(newBed);
	  },
	  
	  // 保存安排
	  saveArrangement() {
		const assignedBeds = this.bedLayouts
		  .filter(bed => bed.cwhList)
		  .map(bed => ({
			bedId: bed.id,
			personId: bed.cwhList.id
		  }));
		
		this.$emit('submit', {
		  roomId: this.roomData.rowData.room_id,
		  assignments: assignedBeds
		});
		
		this.$emit('on_show_table');
		this.$Message.success('床位安排保存成功');
	  },
	  bedLayoutEvent(orgCode){
	    console.log('床位布局设置');
	  },
	  bedLayoutCahnge(orgCode){
		console.log('床位布局配置');
		this.getByOrgCode(orgCode)
	  },
	  getByOrgCode(orgCode) {
		this.$store.dispatch('authGetRequest',{
			url: this.$path.bsp_pam_bed_getByOrgCode,
			params: {
				orgCode
			}
		}).then(res => {
			if(res.success) {
				this.orgBedList = res.data
				this.$nextTick(() => {
					this.bedInformationModal = true
				})
			} else {
				this.$Modal.error({
					title: '温馨提示',
					conent: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  automaticbedEvent() {
		this.getCwglList()
		console.log('自动床位设置');
	  },
	  automaticbedChange(row){
		console.log('自动床位配置');
		this.$store.dispatch('authGetRequest',{
			url: this.$path.bsp_pam_bed_autoBedByConfig,
			params: {
				orgCode: row.org_code,
				roomId: row.room_code
			}
		}).then(res => {
			if(res.success) {
				console.log(res,'res')
				this.roomData.getByRoomId = res.data
				this.roomData.prisoner.notPlan = res.data.notPlan
				this.roomData.rowData.org_code = res.data.orgCode
				this.roomData.rowData.room_code = res.data.roomId
				this.roomBgImgUrl = res.data.layoutUrl
			} else {
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  getCwglList() {
		this.$store.dispatch('authGetRequest',{
			url: `/bsp-com/static/dic/acp/ZD_CWGL_ZDCWPZ.js`
		}).then(res => {
			let arr = []
			let numTon = eval('(' + res + ')')
			arr = numTon()
			console.log(arr,'arr')
			this.cwglList = arr
			this.$nextTick(() => {
				this.automaticBedModal = true
			})
		})
	  },
	  lastBedArrangement(){
		console.log('上次床位安排');
	  },
	  setControlPersonnel(){
		console.log('设置夹控人员');
	  },
	  automaticBedCancel(){
		this.automaticBedModal = false
	  },
	  automaticBedOk(row){
		console.log(row,'提交');
		this.$store.dispatch('authPostRequest',{
			url: this.$path.bsp_pam_bed_update,
			params: {
				bedAutoConfig: this.vertical,
				orgCode: row.org_code,
				roomId: row.room_code
			}
		}).then(res => {
			if(res.success) {
				console.log(res);
				this.$nextTick(() => {
					this.automaticBedModal = false
				})
			} else{
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '接口操作失败！'
				})
			}
		})
	  },
	  bedInformatioCancel(){
		console.log(11,'1')
		this.bedInformationModal = false
	  },
	  getBedInfo(item) {
		console.log(item,'床位');
		this.formData.layoutId = item.id
      	this.activeItem = item.id;
		// 这里可以继续处理其他逻辑
	  },
	  bedInformatioOk(){
		console.log('床位',this.roomData)
		if(this.roomData) {
			this.formData.orgCode = this.roomData.rowData.org_code
			this.formData.roomId = this.roomData.rowData.room_code
			if(this.roomData.getByRoomId && this.roomData.getByRoomId.id != '') {
				this.formData.id = this.roomData.getByRoomId.id
			}
		}
		console.log(this.formData,'formData')
		if(!this.formData.layoutId) {
			this.$Modal.error({
				title: '温馨提示',
				content: '请选择床位布局类型'
			})
			return;
		}
		this.$store.dispatch('authPostRequest',{
			url: this.$path.bsp_pam_bed_createBedByConfig,
			params: this.formData
		}).then(res => {
			if(res.success) {
				console.log(res,'res');
				this.$nextTick(() => {
					this.bedInformationModal = false
				})
			} else {
				this.$Modal.error({
					title: '温馨提示',
					content: res.msg || '操作失败！'
				})
			}
		})
	  },
	  getJgryInfo(bed){
		this.bedRyInfonModal = true
	  },
	  leftArrow(box) {
      // 你的方法逻辑
	  console.log('left');
      },
      rightArrow(box) {
      // 你的方法逻辑
	  console.log('right');
      },
	  topArrow(box) {
		console.log('top');
	  },
	  bottomArrow(box) {
		console.log('bottom');
	  },
	  handleMouseEnter(bedId) {
		this.isHover = true;
		this.hoverBedId = bedId;
	  },
	  handleMouseLeave() {
		this.isHover = false;
		this.hoverBedId = null;
	  },
	  getRiskName(level) {
		switch(level) {
		case '1':
			return '一级风险';
		case '2':
			return '二级风险';
		case '3':
			return '三级风险';
		default:
			return '重病号';
		}
	  },
	}
  };
  </script>
  
  <style lang="less" scoped>
  .bed-arrangement-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
  }
  
  .header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	border-bottom: 1px solid #e8e8e8;
  }
  
  .content {
	width: 100%;
	display: flex;
	flex: 1;
	overflow: hidden;
  }
  
  .unassigned-list {
	width: 358px;
	padding: 0px 16px;
	border-right: 1px solid #e8e8e8;
	// overflow-y: auto;
  }
  
  .person-list {
	margin-top: 10px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	overflow-y: auto;          /* 新增 */
  	// min-height: calc(100vh - 200px); /* 根据实际情况调整高度 */
	height: 58vh;
  }
  .person-lists{
	height: 58vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 100px;
  }
  
  .person-item {
	width: 148px;
	height: 222px;
	padding: 8px;
	margin-bottom: 8px;
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	cursor: move;
	background: #fafafa;
	transition: all 0.3s;
	img{
		width: 132px;
		height: 176px;
		background: #D8D8D8;
		border-radius: 4px 4px 4px 4px;
	}
	p{
		text-align: center;
		line-height: 20px;
		font-size: 16px;
		color: #00244A;
		font-weight: 600;
	}
  }
  .person-item:nth-of-type(2n + 1){
	// background: red;
	margin-right: 12px;
  }
  
  .person-item:hover {
	border-color: #1890ff;
	background: #e6f7ff;
  }
//   .notPlanInfo{
// 	width: 148px;
// 	height: 192px;
// 	border-radius: 6px 6px 6px 6px;
// 	border: 1px solid #2390FF;
	
//   }
.tab-outter{
	display: flex;
    width: 100%;
    height: 38px;
    background: #FFFFFF;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #C4CED8;
	.tab-cls {
		width: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #2390FF;
		cursor: pointer;
	}
	.tab-active {
		background: #EAF4FF;
		border-radius: 6px;
		border: 1px solid #2390FF;
	}
}
  
  .bed-layout {
	flex: 1;
	padding: 15px;
	.action-btn-cont{
		width: 100%;
		height: 66px;
		display: flex;
		text-align: right;
		flex-direction: row-reverse;
		.actionBtns{
			font-size: 16px;
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			color: #2390FF;
			text-align: center;
			line-height: 37px;
			padding: 0 8px;
			height: 38px;
			background: #FFFFFF;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #2390FF;
		}
		.actionBtn{
			// width: 140px;
			height: 38px;
			background: #FFFFFF;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #2390FF;
			display: flex;
			align-items: center;
			cursor: pointer;
			p{
				// width: 108px;
				// height: 21px;
				line-height: 38px;
				font-family: MicrosoftYaHei, MicrosoftYaHei;
				font-weight: normal;
				font-size: 16px;
				color: #2390FF;
				text-align: center;
				font-style: normal;
				text-transform: none;
				border-right: 1px solid #2390FF;
				padding: 0 8px;
			}
			img{
				width: 20px;
				height: 20px;
				margin: 0px 5px;
				// border: 2px solid #2390FF;
			}
		}
	}
  }
  
  .bed-content{
	width: 100%;
	height: 600px;
	overflow: auto;
	margin: 0 auto;
  }
  
  .bed-area {
	position: absolute;
	// border: 2px dashed #1890ff;
	display: flex;
	justify-content: center;
	align-items: center;
  }
  
  .occupied-bed {
	// width: 116px;
	// height: 168px;
	// background: rgba(24, 144, 255, 0.2);
	// display: flex;
	// justify-content: center;
	// align-items: center;
	// position: relative;
  }
  
  .bed-bg{
	width: 116px;
	height: 168px;
	padding: 12px 8px;
	// border: 1px solid #e4eaf0;

	// background: url();
  }
  .bed-bg:hover{
	// border: 1px solid #1890ff;
  }
  .bed-bgs{
	width: 184px;
	height: 116px;
	padding: 8px 12px;
	border: 1px solid #e4eaf0;

	// background: url();
  }
  .bed-bgs:hover{
	border: 1px solid #1890ff;
  }
  .bed-ryInfo{
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #E4EAF0;
	display: flex;
	flex-direction: column;
	padding: 8px;
	p{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #00244A;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	span{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #8D99A5;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
  }
  .bed-ryInfo:hover{
	border: 1px solid #1890ff;
	cursor: pointer;
  }
  .bed-ryInfos{
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border-radius: 6px 6px 6px 6px;
	border: 1px solid #E4EAF0;
	display: flex;
	// flex-direction: column;
	padding: 8px;
	// margin-left: 8px;
	p{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #00244A;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
	span{
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #8D99A5;
		text-align: center;
		font-style: normal;
		text-transform: none;
	}
  }

  .add-bedCont{
	width: 100px;
	height: 100px;
	border: 1px dashed #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 6px;
	padding-top: 23px;
	background-color: rgba(255, 255, 255, 0.3); /* 透明白，透明度30% */
	margin-bottom: 12px;
  }
  .add-bed-area {
	background: rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	padding: 12px 8px;
  }
  
  .add-bed-btn {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 16px;
	color: #FFFFFF;
	text-align: center;
	font-style: normal;
	text-transform: none;
  }
  
  .remove-icon {
	position: absolute;
	top: 5px;
	right: 5px;
	cursor: pointer;
	color: #ff4d4f;
  }
  
  .remove-icon:hover {
	color: #ff7875;
  }
  .sys-sub-title{
	margin: 10px 0px !important;
  }
  .tip{
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 16px;
	color: #8D99A5;
	text-align: left;
	font-style: normal;
	text-transform: none;
	margin-top: 8px;
  }
  .scroll-bar {
      display: none;
      color: #fff;
      position: absolute;
      top: 50%;
      width: 32px;
      height: 50px;
      transform: translateY(-50%);
      text-align: center;
      line-height: 50px;
      background: rgba(0, 0, 0, 0.4);
      cursor: pointer;
      z-index: 3;
    }
    .scroll-bar-left {
      left: 0;
      top: 50%;
      border-radius: 0px 4px 4px 0px;
    }
    .scroll-bar-right {
      right: 0;
      border-radius: 4px 0px 0px 4px;
    }
	.bedItem{
		// border: 1px solid #e4eaf0;
	}
	.bedItem:hover{
		// border: 1px solid #1890ff;
	}
	/* 确保Tooltip不会被隐藏 */
	/deep/.ivu-tooltip-popper {
		overflow: visible !important;
	}
	.tooltip-container {
		position: relative;
		z-index: 1;
		overflow: visible;
		width: 100%;
		height: 100%;
	}

	.room-background {
		position: relative;
		// margin-left: 50px;
		overflow: hidden;
		// width: 100%;
		height: 100%;
		overflow: auto;
	}
	.room-backgrounds{
		width: 100%;
		height: calc(~'100% - 51px');
		background: url('../../assets/images/roomManage/围墙.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		padding: 12px;
		.roomBed-content{
			width: 100%;
			height: 100%;
			padding: 13px 0px;
			overflow: auto;
			// background: #1890ff;
			.roomBed-grid{
				display: grid;
				gap: 25px;
				height: 100%;
			}
		}
	}
	.roomBed-contentPer{
		width: 100%;
		height: 100%;
		padding: 13px 0px;
		overflow: hidden;
		// background: #1890ff;
		.roomBed-gridPer{
			display: grid;
			gap: 10px;
			height: 100%;
		}
	}

	/* Tooltip样式（保留原样式基础上新增层级和定位） */
	/deep/.ivu-tooltip-popper {
		z-index: 999 !important;
		pointer-events: auto;
	}
	/deep/.ivu-modal-body{
		min-height: 230px !important;
		position: relative !important;
	}
	/deep/.select-use-modals {
		.ivu-modal-body{
			min-height: 670px !important;
		}
	}
	.orgBed-class{
		// width: 130px;
		// width: 15%;
		// min-width: 130px;
		// height: 120px;
		background-color: #dfdede;
		padding: 8px 12px;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 1px solid #C4CED8;
		cursor: pointer;
		margin-bottom: 10px;
		margin-right: 10px;
		img{
			width: 100%;
			height: 100px;
		}
		span{
			color: #00244A;
			font-size: 14px;
		}
	}
	.active-border{
		border: 1px solid #1890ff;
	}
	.bedSize{
		width: 70px;
		height: 36px;
		border-radius: 4px;
		border-color: #1890ff;
	}
	.bedSize:focus{
		outline: none;
	}
	.dw {
		position: relative;
		top: -6px;
		left: 0px;
		display: inline-block;
		width: 35px;
		text-align: center;
		// line-height: 36px;
		height: 36px;
		border: 2px solid #1890ff;
		border-left: none;
		border-radius: 0px 4px 4px 0px;
	}
	.zl:focus{
		outline: none;
	}
	.bed-config{
		padding-top: 20px;
	}
  </style>