<template>
  <div class="base-app" style="min-width: 1440px; height: 100%" v-if="!loadding && isPower">
    <Layout>
      <Header style="display: flex; justify-content: space-between; align-items: center">
        <div class="layout-logo">
          <img  style="width: 54px;margin-right: 6px;" src='@/assets/images/u5.png' alt="" />
          <span>{{ titleName }} </span>
        </div>

        <div class="layout-nav" >
          <orgSelect style="margin-right: 0px;" />

          <div  style="height: 60px;display:flex;" >
            <div class="mjInfo" style="height: 60px; margin-right: 17px;padding-left: 21px;">
              <img class="avater" :src="userImg" />
              <!-- <div class="dropTitle">
                <span class="no-wrap">{{ userName }}</span>
              </div> -->
            </div>
            <!-- <span style="color:#fff;left: -15px;position: relative;">|</span> -->
          </div>
          <!-- <div class="home-img" @click="$router.push('/protal')" v-if="honeShow">
            <span>返回首页</span>
          </div> -->
        </div>
      </Header>
      <!--  -->
      <top-menu   :menu-list="menus" :activeIndexTemp='activeIndexTemp'  @changeMenu='changeMenu' @on-select="turnToPage" v-if="showSlide && menusWidth == 240 && rank" />
      <Layout>
        <Sider v-show="showSlide" hide-trigger collapsible :width="menusWidth" :collapsed-width="64" v-model="collapsed" class="left-sider">
          <div class="bsp-main" style="height: 94vh">
            <!-- //menusArray[0].children[0].url || menusArray[0].children[0].url || -->
            <side-menu accordion :openedName='rank?(menusArray[0] && menusArray[0].children?[menusArray[0].children[0].id]:[ menusArray[0].id]):[]' :activeNameTemp='rank?(menusArray[0] && menusArray[0].children?menusArray[0].children[0].url:menusArray[0].url):""' ref="sideMenu" :multilevel="menusWidth == 240" :rootIconSize="menusWidth == 240 ? 20 : 32" :collapsed="collapsed" @on-select="turnToPage" :menu-list="menusWidth == 240 && rank?menusArray:menus">
            </side-menu>
          </div>
        </Sider>
        <!-- <Sider hide-trigger :width="104" :collapsed-width="64" :style="{ height: 'calc(100vh - 60px)', overflow: 'auto', background: '#233356 !important' }" v-if="showSideMenu">
          <side-menu accordion ref="sideMenu" :active-name="$route.path === '/app/system/frame' ? 'isTurnFrame_' + routerQuery.menuId : $route.path" :collapsed="collapsed" :menu-list="menus">
          </side-menu>
        </Sider> -->
        <Layout>
          <!-- <BreadCrumb v-if="showBreadCrumb" :moduleName="moduleName" /> -->
          <Content :style="{ overflow: 'auto hidden' }" class="icp-scroll">
            <router-view :key="key" class="icp-scroll" :class="[showBreadCrumb ? 'layout-ht-bread' : 'layout-ht']" v-show="doalogType === '0'" />
          </Content>
        </Layout>
      </Layout>
    </Layout>
  </div>
  <div v-else-if="!loadding && !isPower">
    <!-- <error :errMsg="errMsg" /> -->
  </div>
</template>

<script>
import orgSelect from "@/components/orgSelect/index.vue"
import SideMenu from './components/side-menu-main'
import topMenu from './components/top-menu/index.vue'
// import SideMenu from '@/components/zt/components/side-menu'
import { mapActions } from 'vuex'
// import error from '@/view/error-page/noPermError.vue'
// import BreadCrumb from '@/components/BreadCrumb/index.vue'
import User from '@/components/main/components/user/user.vue'
import { getUserCache } from '@/libs/util'
export default {
  name: 'AlertMain',
  components: {
    // error,
    SideMenu,
    // BreadCrumb,
    User,
    topMenu,
    orgSelect
  },
  data() {
    return {
      openedName:[],
      activeName:'',
      rank:false,
      userName: getUserCache.getUserName(),
      userImg: '',//require("@/assets/images/header/defaultPhoto.png"),
      appId: serverConfig.APP_ID, //应用id,
      moduleId:this.$route.query.moduleId,
      moduleName: '',
      collapsed: false,
      scrollTop: 0,
      menusWidth: 104,
      menus: [],
      menusArray:[],
      activeIndexTemp:'',
      tabs: [],
      tabMap: {},
      tabsName: '',
      subtitle: '',
      loadding: false,
      isPower: true,
      errMsg: '抱歉,您还没有权限访问该页面',
      app: {},
      doalogType: '0',
      iframeUrl: '',
      userName: getUserCache.getUserName(),
      routerQuery: {
        ztbs: '',
        menuId: '',
        ywbh: '',
        ajlx: '',
        ajbh: ''
      },
      showMenu: true,
      appCode:serverConfig.APP_CODE
    }
  },
  computed: {
    key() {
      return this.$route.path + Math.random()
    },
    honeShow() {
      switch (this.$route.path) {
        case '/index':
          return false
          break
        case '/xxzx':
          return false
          break
        case '/jlzx':
          return false
          break
        default:
          return true
          break
      }
    },
    titleShow() {
      switch (this.$route.path) {
        case '/index':
          return true
          break
        case '/xxzx':
          return true
          break
        case '/jlzx':
          return true
          break
        default:
          return false
          break
      }
    },
    showSideMenu() {
      return !!this.$route.meta.menu
    },
    showBreadCrumb() {
      return !!this.$route.meta.bread
    },
    showNav() {
      return !!this.$route.meta.nav
    },
    // moduleName() {
    //   let name = ''
    //   console.log(this.routerQuery,'this.routerQuery')
    //   if (this.routerQuery.ztbs) {
    //     name = this.routerQuery.ztbs === 'jqzt' ? '警情总台' : this.routerQuery.ztbs === 'ajzt' ? '案件总台' : '人员总台'
    //   } else {
    //      console.log(this.$route,this.routerQuery.title,'this.routerQuery.title')
    //     //  name=this.setTitle()
    //     // if (this.routerQuery.title) {
    //     //   name = this.routerQuery.title
    //     // } else {
    //     //   // name = '警情处置'
    //     // }
    //   }
    //   return name
    // },
    titleName() {
      let name = ''
      if (this.routerQuery.ztbs) {
        name =
          this.routerQuery.ztbs === 'jqzt'
            ? this.routerQuery.ywbh
            : this.routerQuery.ztbs === 'ajzt'
            ? (this.routerQuery.ajlx === '01' ? '行政-' : '刑事-') + this.routerQuery.ajbh + '案件'
            : '超级管理员(身份无法确查)'
      } else {
        name = serverConfig.APP_NAME
      }
      return name
    },
    userAvatar() {
      return this.$store.state.common.avatarImgPath
    },
    showSlide() {
      // 是否显示左边菜单
      return !!this.$route.meta.menu && this.showMenu
    }
  },
  created() {
    if (this.moduleId){
      this.getData()
    }else{
      // this.getDataMenu()
    }
    if (this.$route.query.params) {
      this.routerQuery = this.$Base64.decode(this.$route.query.params)
    } else {
      this.routerQuery.ztbs = this.$route.query.ztbs
      this.routerQuery.ywbh = this.$route.query.ywbh
      this.routerQuery.ajlx = this.$route.query.ajlx
      this.routerQuery.ajbh = this.$route.query.ajbh
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    // 获取用户头像信息
    getUserPhoto() {
      this.postRequest({
        url: this.$path.get_user_photo,
        params: { userId: getUserCache.getUserId() }
      }).then(resp => {
        if (resp.success && resp.data && resp.data.photo ) {
          // this.userImg = 'data:image/png;base64,' + resp.data.photo
          this.userImg = resp.data.photo
        }
      })
    },
    changeMenu(data){
      // console.log(data,data[0].url,this.rank,'2112')
     this.menusArray=JSON.parse(JSON.stringify(data))
     let url = data[0].children && data[0].children.length>0?data[0].children[0].url:data[0].url
     this.turnToPage(url)

    },
    getDataMenu() {
      this.authGetRequest({
        url: '/bsp-uac/uac/menu/getUserMenu',
        params: { appCode: this.appCode}
      }).then(res => {
        if (res.success) {
          // if (this.routerQuery.ztbs) {
          //   this.moduleName = this.routerQuery.ztbs === 'jqzt' ? '警情总台' : this.routerQuery.ztbs === 'ajzt' ? '案件总台' : '人员总台'
          // } else {
          //  this.moduleName = res.moduleName
          // }
          // this.moduleName = res.moduleName
          // this.rank=res.data.some(item => {
          //   return item.rank>2
          // });
          // console.log(this.rank,'this.rank')
          this.menus = res.data[0].children//res.data  //this.createTreeData(res.data)
          this.menus.forEach(item => {
            if (item.children && item.children.length > 0) {
              this.$set(item, 'className', 'activeRow')
              this.menusWidth = 240
            }
          })
          this.menusArray=this.menus.length>0 && this.menus[0] && this.menus[0].children?JSON.parse(JSON.stringify(this.menus[0].children)):[]
          // console.log(this.menusArray,'this.menusArray')
          this.$forceUpdate()
        } else {
          // this.errorModal({ content: res.msg })
          this.$Modal.error({
              title: "错误提示",
              content: res.msg
            });
        }
      })
    },
    // 根据appId，moduleId查询
    getData() {
      this.postRequest({
        url: '/bsp-uac/uac/menu/getMenuByModuleId',
        params: { appId: this.appId, moduleId: this.moduleId }
      }).then(res => {
        if (res.success) {
          // if (this.routerQuery.ztbs) {
          //   this.moduleName = this.routerQuery.ztbs === 'jqzt' ? '警情总台' : this.routerQuery.ztbs === 'ajzt' ? '案件总台' : '人员总台'
          // } else {
          //  this.moduleName = res.moduleName
          // }
          this.moduleName = res.moduleName
          this.rank=res.data.some(item => {
            return item.rank>2
          });
          // console.log(this.rank,'this.rank')
          this.menus = this.createTreeData(res.data)
          this.menus.forEach(item => {
            if (item.children && item.children.length > 0) {
              this.$set(item, 'className', 'activeRow')
            }
          })
          this.menusArray=this.menus.length>0 && this.menus[0] && this.menus[0].children?JSON.parse(JSON.stringify(this.menus[0].children)):[]
          // console.log(this.menusArray,'this.menusArray')
          this.$forceUpdate()
        } else {
          // this.errorModal({ content: res.msg })
          this.$Modal.error({
              title: "错误提示",
              content: res.msg
            });
        }
      })
    },
    // 构建树形数据
    createTreeData(arr) {
      let cloneData = JSON.parse(JSON.stringify(arr)) // 对源数据深度克隆
      return cloneData.filter(father => {
        let branchArr = cloneData.filter(child => father.id == child.parentId) //返回每一项的子级数组
        branchArr.length > 0 ? ((father.children = branchArr), (this.menusWidth = 240)) : '' //如果存在子级，则给父级添加一个children属性，并赋值
        return !father.parentId //返回第一层
      })
    },
    // 导航栏点击事件
    turnToPage(route, reset = false) {
      // console.log(route, reset,'route, reset')
      if (reset) return
      let { path, params, query } = {}
      if (typeof route === 'string') {
        path = route
        let moduleId = this.$route.query.moduleId || ''
        if (moduleId) query = { moduleId: moduleId }
      } else {
        path = route.path
        params = route.params
        query = route.query
      }
      this.$router.push({
        path,
        params,
        query
      })
    },
    // 监听滚动条
    handleScroll() {
      // document.body.scrollTop || document.documentElement.scrollTop
      let scrollTop =
        (document.querySelector('.layout-ht') ? document.querySelector('.layout-ht').scrollTop : 0) ||
        (document.querySelector('.background') ? document.querySelector('.background').scrollTop : 0) ||
        (document.querySelector('.icp-common-content') ? document.querySelector('.icp-common-content').scrollTop : 0)
      this.scrollTop = scrollTop
    },
  },
  watch: {
    '$route': {
      handler(val) {
         if(val.path !='/browserUpload' && val.path !='/gjb'){
          this.getUserPhoto()
         }
      },
      deep: true,
      immediate: true
    },
   'rank':{
    handler(n,o){
      if(n){
        this.menus.forEach(item=>{
          if(this.$route.path.indexOf(item.url)>-1){
             this.menusArray=item.children && item.children.length>0 ?JSON.parse(JSON.stringify(item.children)):JSON.parse(JSON.stringify(this.menus[0].children))
             this.activeIndexTemp=item && item.id?item.id:this.menus[0].id
          }
        })
      }
    },
    deep: true,
    immediate: true
   }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll, true)
    if (document.getElementById('watermark-box')) {
      document.getElementById('watermark-box').style.top = '75px'
    }
  },
  destroyed() {
    if (document.getElementById('watermark-box')) {
      document.getElementById('watermark-box').style.top = '20px'
    }
  }
}
</script>

<style lang="less" scoped>
.avater {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: #ddd;
  margin-right: 8px;
}
.dropTitle {
  font-size: 16px;
  color: #fff;
  cursor: pointer;
}
.mjInfo {
  display: flex;
  align-items: center;
  // margin-right: 30px;
  // padding-right: 30px;
  // background: url('~@/assets/images/pgys.png') no-repeat;
  background-size: 100% 100%;
  min-width: 80px;
}
.userInforbg {
  min-width: 200px;
  max-width: 280px;
  margin-left: 30px;
  &:hover {
    background: rgb(69, 117, 230);
  }
}
/deep/ .ivu-layout {
  // background: #EBEEF5;
  background: transparent !important;
  height: 99.9%;
}
/deep/ .tags-nav {
  box-shadow: 0px 3px 3px 0px #ced5e5;
  z-index: 2;
  position: relative;
  overflow: unset;
  .ivu-tabs-bar {
    margin-bottom: 0;
    .ivu-tabs-nav-container {
      height: 48px !important;
      background: #f0f5fa;
      padding: 0 12px;
      .ivu-tabs-nav-prev,
      .ivu-tabs-nav-next {
        z-index: 10;
        top: 9px;
        .ivu-icon {
          font-size: 30px;
          background: #fff;
          border-radius: 5px;
        }
      }
      .ivu-tabs-tab {
        width: 160px;
        height: 48px !important;
        padding: 0;
        line-height: 48px;
        background: #f0f5fa !important;
        border-radius: 4px 4px 0px 0px;
        text-align: center;
        font-size: 18px;
        font-weight: 400;
        color: #142966;
        margin: 0 8px !important;
        border: none !important;
        &:hover{
          color:#2B5FD9;
        }
      }
      .ivu-tabs-tab-active {
        background: #ffffff !important;
        box-shadow: 1px 0px 6px 0px #dcdee2;
        z-index: 2;
        font-size: 18px;
        font-weight: 700;
        color: #2b5fda;
      }
    }
  }
  .ivu-tabs,
  .ivu-tabs-nav-container,
  .ivu-tabs-nav-wrap,
  .ivu-tabs-nav-scroll {
    overflow: initial;
  }
  .ivu-tabs-ink-bar {
    bottom: -7px;
    height: 7px;
    visibility: inherit;
    background: #fff;
    z-index: 3;
    left: 8px;
    visibility: inherit !important;
    margin-left: 8px;
    opacity: 0;
    // border-bottom-left-radius: 10px;
    // border-bottom-right-radius: 10px;
  }
}
/deep/ .scrollTop {
  .ivu-tabs-ink-bar {
    opacity: 1;
  }
}

.layout-nav {
  min-width: 190px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  // position: relative;
  .ivu-avatar {
    background-color: #dee8ff;
    border: 1px solid #fff;
  }
  /deep/ .ivu-avatar .ivu-icon {
    color: #8fa1cc;
  }
  .user-name {
    padding-right: 10px;
    color: #fff;
    font-size: 16px;
  }
  .title-box {
    font-size: 22px;
    font-weight: 400;
    margin: 0 10px;
    padding: 0 30px;
    position: relative;
    > span {
      // color: #fff;
      color: #d2e3ff;
      cursor: pointer;
    }
    .tipIcon {
      position: absolute;
      bottom: -12px;
      left: 33%;
      visibility: hidden;
    }
    &.active {
      // background: #fff;
      font-weight: bold;
      > span {
        // color: #2b5fda;
        color: #fff;
      }
      .tipIcon {
        visibility: inherit !important;
      }
    }
  }
  .home-img {
    width: 128px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 16px;
    text-align: center;
    display: flex;
    justify-content: center;
    > span {
      padding-left: 8px;
    }
    &:hover {
      background: #4575e6;
    }
    &::before {
      // content: '';
      position: absolute;
      width: 2px;
      height: 28px;
      left: -10px;
      top: 27%;
      background: #fff;
      border-radius: 1px;
    }
  }
}
</style>

<style scoped>
.layout-logo {
  font-size: 30px;
  color: #fff;
  background-size: 42px 42px;
  background-repeat: no-repeat;
  /* padding-left: 58px; */
  display: flex;
  align-items: center;
}
.layout-logo > span:first-child {
  font-size: 30px;
  font-weight: 700;
  color: #f4f8ff;
  position: relative;
  top: 2px;
}
.layout-logo .subtitle {
  font-size: 22px;
  height: 40px;
  padding: 5.5px 10px;
  background: #4575e6;
  margin: auto 30px;
  border-radius: 4px;
  letter-spacing: 1px;
}
.layout-ht-bread {
  /* height: calc(100vh - 110px); */
  height: calc(100vh - 90px);
  overflow: hidden auto;
  position: relative;
  background: #EBEEF5;
}
.layout-ht {
  height: calc(100vh - 60px);
  overflow: hidden auto;
  position: relative;
}
@media screen and (max-width: 1440px) {
  .layout-ht-bread {
    height: calc(100vh - 120px);
  }
  .layout-ht {
    height: calc(100vh - 70px);
  }
}
/* .icp-scroll .layout-ht.hmPageManage {
  overflow: hidden !important;
  height: calc(100vh - 30px) !important;
} */
.base-app /deep/ .ivu-layout-header {
  background: #2b5fda;
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
}
</style>
