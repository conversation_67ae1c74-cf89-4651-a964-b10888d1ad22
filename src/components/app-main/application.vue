/** 应用程序-中转页 */
<template>
  <div></div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  name: 'application',
  data() {
    return {
      appId: serverConfig.APP_ID, //应用id,
      moduleId: this.$route.query.moduleId,
      userId: this.$store.state.common.userId,
      appCode:serverConfig.APP_CODE
    }
  },
  created() {
    // this.getData()
    // this.getDataMenu()
  },
  methods: {
    ...mapActions(['authGetRequest','postRequest']),
    // 根据appId，moduleId查询
    getData() {
      console.log(this.moduleId,'this.moduleId')

      this.postRequest({
        url: '/bsp-uac/uac/menu/getMenuByModuleId',
        params: { appId: this.appId, moduleId: this.moduleId }
      }).then(res => {
        if (res.success) {
          if (res.data && res.data[0]) {
            this.$router.push({
              path: res.data[0].url,
              query: {
                moduleId: this.moduleId
              },
              replace: true
            })
          }
        } else {
          // this.errorModal({ content: res.msg })
              this.$Modal.error({
              title: "错误提示",
              content: res.msg
            });
        }
      })
    },
    getDataMenu() {
      console.log(this.moduleId,'this.moduleId')

      this.authGetRequest({
        url: '/bsp-uac/uac/menu/getUserMenu',
        params: { appCode: this.appCode}
      }).then(res => {
        if (res.success) {
          if (res.data && res.data[0]) {
            this.$router.push({
              path: res.data[0].url,
              query: {
                // moduleId: this.moduleId
              },
              replace: true
            })
          }
        } else {
          // this.errorModal({ content: res.msg })
              this.$Modal.error({
              title: "错误提示",
              content: res.msg
            });
        }
      })
    }
  }
}
</script>