<template>
  <Submenu :name="`${parentName}`" :class="parentItem.className">
    <template slot="title">
      <div class="flex-left-title"><common-icon :type="parentItem.imgPath || ''" :size="rootIconSize" />
      <span>{{ showTitle(parentItem) }}</span></div>
    </template>
    <template v-for="item in children">
      <template v-if="item.children && item.children.length === 1">
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.children[0].name}`">
          <common-icon :type="item.children[0].icon || ''" :size="rootIconSize" /><span>{{ showTitle(item.children[0]) }}</span>
        </menu-item>
      </template>
      <template v-else>
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.name}`"  :class="[$route && $route.path == getNameOrHref(item)?'activeItem':'']"
          ><p>
            <common-icon :type="item.imgPath || ''" :size="rootIconSize" v-if="!multilevel" />
            <span v-if="multilevel && showTitle(item).length > 20">
              <Tooltip :content="showTitle(item)" placement="top-start">
                {{ showTitle(item).substr(0, 20) + '…' }}
              </Tooltip>
            </span>
            <span v-else>{{ showTitle(item) }}</span>
          </p>
        </menu-item>
      </template>
    </template>
  </Submenu>
</template>
<script>
import mixin from './mixin'
import itemMixin from './item-mixin'
export default {
  name: 'SideMenuItem',
  mixins: [mixin, itemMixin],
  props: {
    multilevel: {
      type: Boolean,
      default: false
    },
    rootIconSize: {
      type: Number,
      default: 20
    }
  }
}
</script>
<style scoped lang="less">
/deep/ .activeItem{
  background: rgba(49, 127, 245, 0.2) !important;
}
</style>

