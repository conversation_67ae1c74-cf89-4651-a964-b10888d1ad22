<template>
  <div class="side-menu-wrapper" :class="multilevel ? 'multilevel' : ''">
    <!-- <slot></slot> -->
    <Menu ref="menu" v-show="!collapsed" style="overflow-y: auto;overflow-x: hidden;" :active-name="active_name" :open-names="openedNames" :accordion="accordion" :theme="theme" width="auto" height="100%" @on-select="handleSelect">
      <template v-for="item in menuList">
        <template v-if="item.children && item.children.length === 1">
          <side-menu-item :multilevel="multilevel" :class="item.className" v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
          <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.children[0].name}`">
            <common-icon :size="rootIconSize" :type="item.children && item.children.length>0?item.children[0].imgPath :''" />
              <span>{{ showTitle(item.children[0]) }}</span>
          </menu-item>
        </template>
        <template v-else>
          <side-menu-item :multilevel="multilevel" v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
          <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.name}`" :class="[$route && $route.path == getNameOrHref(item)?'activeItem':'']">
            <common-icon :size="rootIconSize" :type="item.imgPath || ''" />
            <span v-if='!multilevel && showTitle(item).length > 8'>
              <Tooltip :content="showTitle(item)" placement="top-start">
                  {{ showTitle(item).substr(0,8) + '…' }}
              </Tooltip>
            </span>
            <span v-else>{{ showTitle(item) }}</span>
          </menu-item>
        </template>
      </template>
    </Menu>
    <div class="menu-collapsed" v-show="collapsed" :list="menuList">
      <template v-for="item in menuList">
        <collapsed-menu
          v-if="item.children && item.children.length > 1"
          @on-click="handleSelect"
          hide-title
          :root-icon-size="rootIconSize"
          :icon-size="iconSize"
          :theme="theme"
          :parent-item="item"
          :key="`drop-menu-${item.name}`"
        ></collapsed-menu>
        <Tooltip transfer v-else :content="showTitle(item.children && item.children[0] ? item.children[0] : item)" placement="right" :key="`drop-menu-${item.name}`">
          <a @click="handleSelect(getNameOrHref(item, true))" class="drop-menu-a" :style="{ textAlign: 'center' }">
            <!-- <common-icon :size="rootIconSize" :color="textColor" :type="item.imgPath || (item.children && item.children[0].imgPath)" /> -->
          </a>
        </Tooltip>
      </template>
    </div>
  </div>
</template>
<script>
import SideMenuItem from './side-menu-item.vue'
import CollapsedMenu from './collapsed-menu.vue'
import { getUnion } from '@/libs/tools'
import mixin from './mixin'

export default {
  name: 'SideMenu',
  mixins: [mixin],
  components: {
    SideMenuItem,
    CollapsedMenu
  },
  props: {
    menuList: {
      type: Array,
      default() {
        return []
      }
    },
    collapsed: {
      type: Boolean
    },
    theme: {
      type: String,
      default: 'dark'
    },
    rootIconSize: {
      type: Number,
      default: 20
    },
    iconSize: {
      type: Number,
      default: 16
    },
    accordion: Boolean,
    openNames: {
      type: Array,
      default: () => []
    },
    multilevel: {
      type: Boolean,
      default: false
    },
    activeNameTemp:{
      type: String,
      default: ''
    },
    openedName: {
      type: Array,
      default() {
        return []
      }
    },
  },
  data() {
    return {
      openedNames: this.openedName,
      active_name: this.activeNameTemp,
      curentMenu:[]
    }
  },
  computed: {
    textColor() {
      return this.theme === 'dark' ? '#fff' : '#495060'
    }
  },
  methods: {
    handleSelect(name) {
      // console.log(name,'name')
      let reset = false
      if (this.$route.path === name) {
        reset = true
      }
      this.$emit('on-select', name, reset)
    },
    // 菜单定位
    activeName(oname) {
      let name = oname
      // if (name.indexOf('/infoManage') > -1) {
      //   name = '/infoManage'
      // }
      // if(name.indexOf('/dms/bmzj') > -1 || name.indexOf('/dms/znyj') > -1){
      //   name = '/dms/jzgl'
      // }
      // if(name.indexOf('/prisons/files') > -1){
      //   name = '/prisons/files'
      // }
      // if(name.indexOf('/jzgl/wdjy') > -1){
      //   name = '/jzgl/wdjy/index'
      // }
      // if(name.indexOf('/jzgl/jysp') > -1){
      //   name = '/jzgl/jysp/index'
      // }
      // if(name.indexOf('/jzgl/jytz') > -1){
      //   name = '/jzgl/jytz/index'
      // }
      
      if (this.accordion) {
        this.openedNames = this.getOpenedNamesByActiveName(name)
      } else {
        this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
      }
      this.active_name =this.curentMenu[0].url?this.curentMenu[0].url:this.curentMenu[0].code  //'informationManage'//
      // this.openedNames=["1923579985495461888"]
      console.log( this.openedNames,oname,' this.openedNames2121',this.accordion,this.active_name,this.curentMenu )
    },
    getOpenedNamesByActiveName(name) {
      console.log(name,'getOpenedNamesByActiveName',this.menuList)
      let arr = []
      this.curentMenu=[]
      let tag=null
      this.menuList.every(ele => {
        //  console.log(ele.children, name,'ele.children, name')
        if (ele.children && ele.children.length > 0) {  // 多级情况
          let curentMenu = this.comparePath(ele.children, name)
          curentMenu?this.curentMenu.push(curentMenu):[]
          if (curentMenu) {
            arr.push(curentMenu.parentId)
            this.$nextTick(() => {
              this.active_name = curentMenu.url?curentMenu.url:name
            })
            tag=true
          }
        } else if (!this.multilevel) { // 一级情况
          if (ele.url && ele.url.indexOf(name) > -1) {
            this.$nextTick(() => {
              this.active_name = ele.url?ele.url:name
            })
            tag=true
          }
        }else{
           this.$nextTick(() => {
             this.active_name=this.activeNameTemp
              })
           tag=true

        }
        return tag
      })

      return arr
    },
    comparePath(menuList, path) {
      console.log(menuList, path,'menuList, path')
      let target = null
      for (let i = 0, len = menuList.length; i < len; i++) {
        let item = menuList[i]
        if (item.children && item.children.length > 0) {
          target = this.comparePath(item.children, path)
          if (target) {
            target = item
            break
          }
        } else {
          let tempUrl = item.url.split('?')[0]
          if (tempUrl === path) {
            target = item
            break
          }
        }
      }
      return target
    }
  },
  watch: {
    menuList() {
      this.menuList.forEach(item=>{
        
      })
      this.activeName(this.$route.path)
    },
    '$route':{
        handler(to, from){
          if(to){
            console.log(to,'$router')
            this.activeName(to.path)
            // this.openedNames=n
          }
        },
        deep:true,
        immediate:true
     },
    // 'openedName':{
    //     handler(n,o){
    //       if(n){
    //         console.log(n,'openedName')
    //         // this.openedNames=n
    //       }
    //     },
    //     deep:true,
    //     immediate:true
    //  },
     'activeNameTemp':{
        handler(n,o){
                      console.log(n,'activeNameTempactiveNameTempactiveNameTemp',this.active_name)

          if(n){
            console.log(n,'activeNameTemp')

            // this.active_name=n
          }
        },
        deep:true,
        immediate:true
     },
    openedNames() {
      this.$nextTick(() => {
        this.$refs.menu.updateOpened()
      })
    },
    
  }
}
</script>
<style lang="less">
@import './side-menu.less';
</style>
<style scoped lang="less">
/deep/ .activeItem{
  background: rgba(49, 127, 245, 0.2) !important;
}
</style>
