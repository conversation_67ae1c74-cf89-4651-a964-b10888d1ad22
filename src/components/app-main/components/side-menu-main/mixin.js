import CommonIcon from '@/components/common-icon/common-icon.vue'
import { showTitle } from '@/libs/util'
export default {
  components: {
    CommonIcon
  },
  methods: {
    showTitle (item) {
      return item.name
    },
    showChildren (item) {
      return item.children && item.children.length > 0
    },
    getNameOrHref (item, children0) {
      // console.log(item, children0,'item, children0')
      if(item.url.indexOf('/help/component') > -1) {
        return '/help/component'
      } else {
        let url = item.href ? `isTurnByHref_${item.href}` : (children0 ? item.children[0].url : item.url)
        // console.log(url,'1212')
        return url
      }
    }
  }
}
