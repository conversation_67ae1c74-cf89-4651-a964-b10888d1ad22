<template>
  <!-- 顶部导航菜单 -->
  <div class="top-menu">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
      <el-menu-item :index="item.id" v-for="(item) in menuList" :key="item.id">
        <common-icon :size="rootIconSize" :color="textColor" :type="item.imgPath || (item.children && item.children[0].imgPath)" />{{item.name}}</el-menu-item>
    </el-menu>
  </div>
</template>

<script>
import CommonIcon from '@/components/common-icon/common-icon.vue'
export default {
  components:{CommonIcon},
  props: {
    menuList: {
      type: Array,
      default() {
        return []
      }
    },
    activeIndexTemp:{
      type:String,
      default:''
    }
  },
  data(){
    return{
        activeIndex:this.activeIndexTemp?this.activeIndexTemp:this.menuList[0].id,
        textColor:'#2B5FD9',
        rootIconSize:20
    }
  },
  methods:{
    handleSelect(data){
      // console.log(data,this.menuList,'21212')
      this.menuList.forEach(item=>{
         if(item.id == data){
            this.$emit('changeMenu',item.children?item.children:[item])
         }
      })
    },
  }
}
</script>

<style lang="less" scoped>
.top-menu{
  height: 48px;
  background: #FFFFFF;
  padding-left: 20px;
}
/deep/ .el-menu--horizontal>.el-menu-item{
    height: 48px !important;
    line-height: 48px!important;
    font-size: 20px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #2B5FD9!important;
    display: flex;
    align-items: center;
    padding:0 40px;
}
/deep/ .el-menu--horizontal>.el-menu-item.is-active {
      border-bottom: none !important;
      // background: url('~@/assets/images/common/menuActive.png') no-repeat;
      background-size: 100% 100%;
      color: #fff !important;
      .common-icon-box{
          .ivu-icon::before{
            color: #fff !important;
          }
      }
    }
    
</style>