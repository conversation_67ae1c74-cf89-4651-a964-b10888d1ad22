<template>
    <div>
        <div style="display:flex;align-items: center;" v-if="!readonly">
            <Upload v-if="isUpload" ref="upload" :show-upload-list="false" :action="importUrl" style="width: 120px;"
                :before-upload="handleBeforeUpload" :on-success="handleSuccess" :format="format" :on-format-error="handleFormatError">
                <Button icon="ios-cloud-upload-outline">上传附件</Button>
            </Upload>
            <Button v-if="photo" style="margin-top: 2px;" @click="takePhoto">
                <Icon type="ios-cash-outline" size="24" />拍摄
            </Button>
        </div>
        <div class="upload-list">
            <div class="upload-list-item" v-for="(item, index) in fileList" :key="index">
                <div class="upload-icon">
                    <img :src="item.url" class="images" v-if="imgTypeArr.includes(item.ext)" v-viewer></img>
                    <div v-else class="file"></div>
                </div>
                <div class="upload-survey">
                    <p class="upload-name" :title="item.filename">{{ item.filename }}</p>
                    <div class="upload-tool">
                        <Icon type="md-eye" title="预览" v-if="imgTypeArr.includes(item.ext)" :src="item.url"
                            @click="openPreview(item.url)" style="margin-right: 5px;" class="iconfont" />
                        <Icon type="md-download" title="下载" @click="downLoad(item)" style="margin-right: 5px;"
                            class="iconfont" />
                        <Icon type="md-trash" title="删除" @click="removeItem(item, index)" class="iconfont"
                            v-if="isUpload" />
                    </div>
                </div>
            </div>

        </div>
        <!-- 相机模态框 -->
        <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
    </div>
</template>
<script>
import cameraModal from '@/components/camera/camera-modal.vue';
import { readonly } from 'vue';
export default {
    components: { cameraModal },
    props: {
        fileList: Array,
        importUrl: String,
        isUpload: {
            type: Boolean,
            default: true
        },
        photo: Boolean,
        readonly: Boolean,
        maxFiles: {
            type: Number,
            default: 10
        },
        format: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            imgTypeArr: ['jpg', 'png', 'peg', 'psd', 'png', 'tiff', 'ai', 'eps', 'svg', 'bmp', 'jpeg'],
        }
    },
    mounted() {
    },
    computed: {
    },
    watch: {
        fileList: {
            handler(n, o) {
                console.log(n, '121221')
            }, deep: true, immediate: true
        }
    },

    methods: {
        handleBeforeUpload() {
            console.log(this.fileList, 'this.fileList')
            const check = this.fileList && this.fileList.length < this.maxFiles;
            if (!check) {
                this.$Notice.warning({
                    title: `最多上传${this.maxFiles}个文件`
                });
            }
            return check;
        },
        // 回调方法
        handleSuccess(res) {
            this.$emit('handleSuccess', res)
        },
        removeItem(item, index) {
            let data = {
                item, index
            }
            this.$emit('removeItem', data)
        },
        downLoad(item) {
            if (item.url) {
                fetch(item.url)
                    .then(res => {
                        res.blob().then(res => {
                            const link = document.createElement('a');
                            link.href = window.URL.createObjectURL(res);
                            document.body.appendChild(link)
                            link.download = item.filename;
                            link.click();
                            window.URL.revokeObjectURL(link.href);
                            document.body.removeChild(link)
                        })
                    })
            }
        },
        openPreview(url) {
            this.$viewerApi({ images: [url] });//要预览的图片 URL 列表
        },

        /**
         * 拍照
         */
        takePhoto(row, index) {
            //   if (this.readonly) return

            try {
                this.$refs.cameraModal.open({})
            } catch (error) {
                console.error('打开相机失败:', error)
                this.$Message.error('打开相机失败，请检查相机权限')
            }
        },

        /**
         * 拍照完成回调
         */
        takePhotoItem(imgUrl) {
            console.log(imgUrl, 'imgUrl')
            //   if (this.currentMarkerIndex >= 0 && this.currentMarkerIndex < this.markerData.length) {
            //     this.$set(this.markerData[this.currentMarkerIndex], 'zp', imgUrl)

            //     if (!this.markerData[this.currentMarkerIndex].zpList) {
            //       this.$set(this.markerData[this.currentMarkerIndex], 'zpList', [])
            //     }
            //     this.markerData[this.currentMarkerIndex].zpList.push(imgUrl)
            //   }
        },
        handleFormatError(file) {
                this.$Notice.warning({
                    title: `${file.name}格式文件不支持上传`,
                });
            },
    }
}
</script>
<style scoped lang="less">
.upload-list {
    margin-top: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fill, 200px);
    grid-auto-rows: 66px;
    grid-gap: 10px;

    .upload-list-item {
        width: 200px;
        height: 66px;
        background: #f5f7fa;
        border-radius: 6px;
        display: flex;
        align-items: center;
        padding: 8px;

        .upload-icon {
            width: 50px !important;
            height: 50px !important;
            margin-right: 8px;

            .images {
                width: 100%;
                height: 100%;
                background-position: 50%;
                background-size: cover;
            }

            .file {
                width: 100%;
                height: 100%;
                background: url('~@/assets/icon/file.png') no-repeat;
            }
        }

        .upload-survey {
            height: 100%;
            flex: 1;
            overflow: hidden;

            .upload-name {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #415060;
                font-size: 16px;
                height: 21px;
                line-height: 21px;
            }

            .upload-tool {
                margin-top: 8px;
                display: flex;

                .iconfont {
                    cursor: pointer;
                    font-size: 24px;
                    color: #415060;

                    &:hover {
                        color: #2b5fda;
                    }
                }
            }
        }
    }
}
</style>
