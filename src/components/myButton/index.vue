<template>
    <div class="my-btn-container" @click="handleBtnClick">
        <div :class="['my-btn', btnClass]">
            <div v-if="hasIcon" class="my-btn-icon">
                <slot name="icon"></slot>
            </div>
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MyBtn',
    props: {
        // 类型: primary complex(复合型输入框中的按钮)
        type: {
            type: String,
            default: 'primary'
        },
         // 尺寸: small normal large
        size: {
            type: String,
            default: 'normal'
        },
        // 是否是朴素按钮
        plain: {
            type: Boolean,
            default: false
        },
        // 是否带有图案
        hasIcon: {
            type: Boolean,
            default: false
        },
        // 是否是加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 是否不可点击
        disabled: {
            type: Boolean,
            default: false
        },
        // 路由跳转地址
        link: {
            type: String,
            default: ''
        }
    },
    data() {
        return {

        }
    },
    computed: {
        btnClass() {
            let className = `my-btn__${this.type} my-btn__${this.size}`
            if(this.plain) {
                className += ' my-btn__plain'
            }
            if(this.hasIcon) {
                className += ' my-btn__has-icon'
            }
            if(this.disabled) {
                className += ' my-btn__disabled'
            }
            return  className
        }
    },
    methods: {
        handleBtnClick() {
            if(this.disabled) {
                return
            }
            if(this.link) {
                this.$router.push(this.link)
                return
            }
            this.$emit('click')
        }
    }
}
</script>

<style lang="less">
.my-btn-container {
    display: inline-block;
    .my-btn {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 0.16rem;
        padding: 0 0.1rem;
        border-radius: 0.044rem;
        cursor: pointer;
        &.my-btn__disabled {
            cursor: no-drop;
        }
        &.my-btn__message {
            min-width: 0.48rem;
            height: 0.24rem;
            font-size: 0.14rem;
        }
        &.my-btn__small {
            min-width: 0.6rem;
            height: 0.3rem;
        }
        &.my-btn__medium {
            min-width: 0.6rem;
            height: 0.32rem;
        }
        &.my-btn__normal {
            min-width: 0.96rem;
            height: 0.32rem;
        }
        &.my-btn__large {
            min-width: 0.96rem;
            height: 0.4rem;
        }
        &.my-btn__primary {
            background-color: #087EFF;
            &:hover {
                background-color: #0062D9;
            }
            &:active {
                background-color: #2A8AF3;
            }
            &.my-btn__disabled {
                background-color: #82BDFF;
            }
        }
        &.my-btn__complex {
            background-color: #63BCFF;
            &:hover {
                background-color: #44AFFF;
            }
            &:active {
                background-color: #2A8AF3;
            }
        }
        &.my-btn__plain {
            color: #087EFF;
            border: solid 0.01rem #087EFF;
            background-color: #fff;
            &:hover {
                background-color: #F5F9FF;
            }
            &:active {
                background-color: #DAECFF;
            }
            &.my-btn__disabled {
                color: #82BDFF;
                border-color: #82BDFF;
                background-color: #ECF5FF;;
            }
        }
        &.my-btn__has-icon {
            width: auto;
            padding: 0 0.1rem;
        }
        .my-btn-icon {
            display: flex;
            align-items: center;
            margin-right: 0.05rem;
        }
    }
}
</style>