<template>
	<div id="messageBox" class="main-content">
		<div>
			<div class="mbxdh">
				<Breadcrumb>
					<BreadcrumbItem to="/jzgl/home">首页</BreadcrumbItem>
					<BreadcrumbItem>消息中心</BreadcrumbItem>
				</Breadcrumb>
			</div>
			<div class="polive_message_main">
				<div class="polive_message_main_head">
					<ul class="top_tab_box filter-tab">
						<li class="tab_item" :class="{ tab_item_active: selectIndex == 0 }" @click="dbsxAction">
							<div class="tab-item-content">
								待办事项
								<i class="badge-box" v-show="messageCount.dbCount > 0">{{ messageCount.dbCount >= 100 ? '99+' :  messageCount.dbCount }}</i>
							</div>
						</li>
						<li class="tab_item" :class="{ tab_item_active: selectIndex == 1 }" @click="tzxxAction">
							<div class="tab-item-content">
								通知消息
								<i class="badge-box" v-show="messageCount.xxCount > 0">{{ messageCount.xxCount >= 100 ? '99+' : messageCount.xxCount }}</i>
							</div>
						</li>
						<li class="tab_item" :class="{ tab_item_active: selectIndex == 2 }" @click="mineAction">
							<div class="tab-item-content">我发起的</div>
						</li>
					</ul>
					<div class="right">
						<div class="content_title_text_right" v-show="selectIndex == 0 || selectIndex == 3">
							<div class="searchBox2">
								<Input search v-model="searchValue" placeholder="请输入消息标题的关键内容进行查询" @on-search="searchBtn" />
							</div>
							<RadioGroup v-model="processingStatus" @on-change="processingStatusAction">
								<Radio label="">全部</Radio>
								<Radio label="1">已处理</Radio>
								<Radio label="0">未处理</Radio>
							</RadioGroup>
						</div>
						<div class="content_title_text_right" v-show="selectIndex == 1">
							<div class="searchBox2">
								<Input search v-model="searchValue" placeholder="请输入消息标题的关键内容进行查询" @on-search="searchBtn" />
							</div>
							<RadioGroup v-model="readingStatus" @on-change="readingStatusAction">
								<Radio label="">全部</Radio>
								<Radio label="1">已读</Radio>
								<Radio label="0">未读</Radio>
							</RadioGroup>
						</div>
					</div>
				</div>
				<div style="text-align: right; padding: 10px">
					<Button v-if="selectIndex == 1 && readingStatus === '0' && jaxx && jaxx.length != 0" type="primary" @click="showConfirmModal">全部已读</Button>
				</div>
				<div class="messageContent">
					<div class="commonList_item" v-for="(item, index) in jaxx" :key="index">
						<div class="title">
							<div style="display: flex; align-items: center">
								<div v-if="selectIndex == 3" :class="'tag yqrksp'">{{ item.alarmTypeName ? item.alarmTypeName : '未知' }}</div>
								<div v-else-if="item.url == 'dmsAjjyApprove'" :class="'tag yqrksp'">电子卷宗案卷借阅</div>
								<div v-else-if="item.url == 'dmsAjdcApprove'" :class="'tag yqrksp'">电子卷宗案卷导出</div>
								<div v-else :class="'tag yqrksp'">{{ messageType }}</div>
								<div class="content">
									<a v-if="selectIndex == 3 && item.clzt == '00'" class="new_ajdj_ajbh" @click="dealAction(index, item)">
										{{ item.title ? item.title : '' }}
									</a>
									<a v-else-if="selectIndex == 3 && item.clzt == '01'" class="new_ajdj_ajbh" style="cursor: auto">
										{{ item.title ? item.title : '' }}
									</a>
									<div v-else-if="item.url == 'imsQjtzTemp'" class="new_ajdj_ajbh" @click="spAction(item)">{{ item.title ? item.title : '' }}</div>
									<a v-else class="new_ajdj_ajbh" @click="spAction(item)">
										{{ item.title ? item.title : '' }}
									</a>
								</div>
							</div>
							<div v-if="selectIndex == 0 && item.isProc == '0'" class="status status_wcl">
								<i></i>
								未处理
							</div>
							<div v-if="selectIndex == 0 && item.isProc == '1'" class="status status_ycl">
								<i></i>
								已处理
							</div>
							<div v-if="selectIndex == 1 && item.isRead == '0'" class="status status_wcl">
								<i></i>
								未读
							</div>
							<div v-if="selectIndex == 1 && item.isRead == '1'" class="status status_ycl">
								<i></i>
								已读
							</div>
							<div v-if="selectIndex == 3 && item.clzt == '00'" class="status status_wcl">
								<i></i>
								未处理
							</div>
							<div v-if="selectIndex == 3 && item.clzt == '01'" class="status status_ycl">
								<i></i>
								已处理
							</div>
						</div>
						<div class="from">
							<div v-if="selectIndex == 0 || selectIndex == 1">
								<p class="userName">
									<label>来源用户姓名：</label>
									<span>{{ item.fUserName }}</span>
								</p>
								<p style="margin-right: 0.65rem" v-if="item.url != 'imsQjtzTemp'">
									<label>来源机构：</label>
									<span>{{ item.fOrgName }}</span>
								</p>
								<p style="margin-right: 0.65rem" v-if="item.url == 'imsQjtzTemp'">
									<label>通知时间：</label>
									<span>{{ item.sTime }}</span>
								</p>
								<p style="margin-right: 0.65rem" v-else>
									<label>申请时间：</label>
									<span>{{ item.sTime }}</span>
								</p>
							</div>
							<div v-else-if="selectIndex == 2">
								<p class="userName">
									<label>申请单位：</label>
									<span>{{ item.fOrgName }}</span>
								</p>
								<p style="margin-right: 0.65rem">
									<label>申请时间：</label>
									<span>{{ item.sTime }}</span>
								</p>
								<p style="margin-right: 0.65rem">
									<label>审批状态：</label>
									<span :class="item.spzt == '2' ? 'spzt_spbtg' : item.spzt == '1' ? 'spzt_sptg' : item.spzt == '3' ? 'spzt_qxsq' : item.spzt == '4' ? 'spzt_spth' : 'spzt_wsp'">{{ item.spzt == '2' ? '审批不通过' : item.spzt == '1' ? '审批通过' : item.spzt == '3' ? '取消申请' : item.spzt == '4' ? '审批退回' : '审批中' }}</span>
								</p>
							</div>
							<div v-else>
								<p class="userName" v-if="item.bamj">
									<label>来源用户姓名：</label>
									<span>{{ item.bamj }}</span>
								</p>
								<p style="margin-right: 0.65rem" v-if="item.cbdwMc">
									<label>来源机构：</label>
									<span>{{ item.cbdwMc }}</span>
								</p>
								<p style="margin-right: 0.65rem">
									<label>预警时间：</label>
									<span>{{ item.yjsj }}</span>
								</p>
							</div>
						</div>
					</div>
				</div>
				<div style="position: relative; right: 20px" v-if="jaxx && jaxx.length != 0">
					<PageFooter :curPage="currentPage" :totalRowNumber="dataTotal" @editPageNum="changePage" @editPageSize="changePageSize"></PageFooter>
				</div>
				<div class="noYjxxMsgBox" v-else>
					<div>
						<ReplaceIcon :text="emptyDataTip" type="no_warning"></ReplaceIcon>
					</div>
				</div>
			</div>
			<!-- 处理 alarmType != 06 时弹窗-->
			<Modal v-model="modal2" :closable="false" width="280" height="116" class-name="vertical-center-modal" footer-hide>
				<div>
					<div class="alert-box-header">
						<!-- <Icon size="20" color="#F8C026" type="ios-alert" /> -->
						<i class="alert-box-header-icon"></i>
						<!-- <span style="color: #2B3646;font-size: 0.14rem;margin-left: 0.1rem;font-weight: bold;">信息提示</span> -->
						<div class="alert-box-title">信息提示</div>
					</div>
					<p style="font-size: 0.14rem; color: #2b3646; margin: 0.12rem 0 0.17rem 0.28rem">{{ modal2Title }}</p>
				</div>
				<div class="alert-box-btn">
					<my-btn size="message" @click="confirmBtnAlarm">确定</my-btn>
					<my-btn size="message" plain style="margin-right: 0.12rem" @click="cancelBtnAlarm">取消</my-btn>
				</div>
			</Modal>
		</div>
	</div>
</template>
<script>
// import {  getCenter } from '@/libs/util'
export default {
	data() {
		return {
			loading: true,
			allReadModal: false,
			modal2: false,
			modal3: false,
			modal2Title: '',
			yjxxDetail: {},
			formData2: {
				czsm: '',
				fkzt: '',
				ghsj: '',
				id: '',
				jzid: ''
			},
			ruleValidate: {
				fkzt: [{ required: true, message: '请选择处理原因', trigger: 'change' }],
				ghsj: [{ required: true, type: 'date', message: '请选择归还时间', trigger: 'change' }]
			},
			messageCount: {
				dbCount: 0,
				xxCount: 0,
				yjCount: 0
			},
			messageType: '消息',
			selectIndex: 0,
			readingStatus: '0',
			processingStatus: '0',
			searchValue: '',
			dataTotal: 0,
			jaxx: [],
			currentPage: 1,
			currPageSize: 10,
			// centerId: getCenter(),
			user: JSON.parse(localStorage.getItem('usreInfo')),
			roleSql: '',
			fromUrl: '',
			onlyShowCzsm: false,   // 异常出库只展示操作说明
			videoList: [],    // 视频列表
			videoSelectIndex: 0    // 视频选中序号
		}
	},
	created() {
		this.fromUrl = this.$route.query.from
		if (this.$route.query.selectIndex) {
			this.selectIndex = this.$route.query.selectIndex
			switch (this.selectIndex) {
				case '0':
					this.dbsxAction()
					break
				case '1':
					this.tzxxAction()
					break
				case '2':
					this.mineAction()
					break
			}
		}
	},
	mounted() {
		if (!this.$route.query.selectIndex) {
			this.dbsxAction()
		}
	},
	computed: {
		emptyDataTip() {
			let text = '暂无'
			switch (this.selectIndex) {
				case 0:
					text += '待办事项'
					break
				case 1:
					text += '通知消息'
					break
				case 2:
					text += '发起消息'
					break
				case 3:
					text += '预警消息'
					break
				default:
					break
			}
			return text
		}
	},
	methods: {
		showConfirmModal() {
			if (this.jaxx.length === 0) {
				this.$Message.warning('没有未读的消息。')
				return
			}
			this.$alert({
				type: 'confirm',
				title: '全部已读',
				content: '<p>将全部消息标记为已读?</p>',
				onOk: () => {
					this.allRead()
				},
				onCancel: () => {
					// this.$Message.info('取消成功');
				}
			})
		},
		getUserRoleSql() {
			var param = {}
			param.mark = serverConfig.dmsFxyjRule
			param.roleIds = this.user.roleIds
			this.$Post(this.API.BspApi.GET_USER_ROLE, param)
				.then((res) => {
					if (res.code && res.code == '200') {
						this.roleSql = res.data
					}
					this.countAllMessage()
				})
				.catch({})
		},
		confirmBtn() {
			this.$refs.formData2.validate((valid) => {
				if (valid) {
					this.formData2.id = this.yjxxDetail.id
					this.formData2.jzid = this.yjxxDetail.jzid
					this.formData2.ghsj = this.formData2.ghsj == '' ? '' : this.utils.dateFormatDay(this.formData2.ghsj)
					this.updateClqk(this.formData2)
					this.$refs.formData2.resetFields()
					this.modal3 = false
				} else {
					setTimeout(() => {
						this.loading = false
						this.$nextTick(() => {
							this.loading = true
						})
					}, 100)
				}
			})
		},
		// 获取轨迹视频
        getVideoList(row) {
            this.$Post(this.API.DmsApi.GET_TRAIL_CHILDS_BY_SPDS, { spbs: row.trailid }).then((res) => {
				if (res.success) {
                    res.data ? this.videoList = res.data : this.videoList = []
				} else {
                    this.$Message.warning('请求失败:' + res.msg  )   
                }
			})
        },
		cancelBtn() {
			this.modal3 = false
			this.$refs.formData2.resetFields()
		},

		confirmBtnAlarm() {
			let params = {
				fkzt: '02',
				id: this.yjxxDetail.id
			}
			this.updateClqk(params)
			this.modal2 = false
		},
		cancelBtnAlarm() {
			this.modal2 = false
		},
		dealAction(index, row) {
			this.yjxxDetail = row
			if (row.alarmType == '06' || row.alarmType == '13') {
				this.$Modal.confirm({
					title: '提示',
                    content: '确定处理该预警吗?',
					onOk: () => {
						if (row.alarmType == '13') {
							this.getVideoList(row)   // 异常出库需要查询轨迹视频
                            this.onlyShowCzsm = true
						} else {
							this.onlyShowCzsm = false
						}
                        this.modal3 = true
					}
				})
			} else {
				this.modal2Type = 1
				// this.modal2 = true
				// this.modal2Title = '确定处理该预警吗?'
				this.$Modal.confirm({
					title: '提示',
					content: '确定处理该预警吗？',
					onOk: () => {
						this.confirmBtnAlarm()
					}
				})
			}
		},

		//处理预警消息 单条
		updateClqk(params) {
			this.$Post(this.API.DmsApi.ALARM_UPDATE_CLQK, { formData: JSON.stringify(params) }).then((res) => {
				if (res.success) {
					//this.$router.go(0)
					this.countAllMessage()
					this.resetPages()
					var params = {
						page: 1,
						limit: this.currPageSize
					}
					this.getYjxxData(params)
				}
			})
		},
		//待办事项
		dbsxAction() {
			this.getUserRoleSql()
			this.selectIndex = 0
			this.resetPages()
			var params = {
				pageNo: 1,
				pageSize: this.currPageSize,
				isProc: this.processingStatus,
				type: '01',
				title: this.searchValue
			}
			this.getRvcMessagePageData(params)
		},
		//通知消息
		tzxxAction() {
			this.getUserRoleSql()
			this.selectIndex = 1
			this.resetPages()
			var params = {
				pageNo: 1,
				pageSize: this.currPageSize,
				isRead: this.readingStatus,
				type: '02',
				title: this.searchValue
			}
			this.getRvcMessagePageData(params)
		},
		//预警消息
		yjxxAction() {
			this.getUserRoleSql()
			this.selectIndex = 3
			this.resetPages()
			var params = {
				page: 1,
				limit: this.currPageSize
			}
			this.getYjxxData(params)
		},
		//我发起的
		mineAction() {
			this.getUserRoleSql()
			this.resetPages()
			this.selectIndex = 2
			var params = {
				pageNo: 1,
				pageSize: this.currPageSize
			}
			this.getMyApplyPageData(params)
		},

		spAction(item) {
			var actInstId = item.actInstId
			var url = item.url
			var id = item.id ? item.id : item._id
			var isRead = item.isRead
			if (this.selectIndex == 1 && isRead == 0 && url != 'imsQjtzTemp') {
				let requestParams = {
					msgIds: id,
					appCode: serverConfig.dmsCode,
					msgType: '02',
					procIdCard: this.user.idCard
				}
				this.$Post(this.API.BspApi.TZXX_READ, requestParams).then((res) => {
					if (res.code && res.code == 'OK') {
					}
				})
			}

			if (url == 'imsQjtz' || url == 'imsQctz' || url == 'imsCrtz') {
				url = 'ajda'
				this.$router.push({ name: url, query: { jzid: item.ywbh, from: this.fromUrl, selectIndex: this.selectIndex } })
				return
			} else if (url == 'imsQjtzTemp') {
				if (isRead == 0) {
					this.$alert({
						type: 'confirm',
						title: '温馨提示',
						content: '确认标记为已读吗？',
						onOk: () => {
							let requestParams = {
								msgIds: id,
								appCode: serverConfig.dmsCode,
								msgType: '02',
								procIdCard: this.user.idCard
							}
							this.$Post(this.API.BspApi.TZXX_READ, requestParams).then((res) => {
								if (res.code && res.code == 'OK') {
								}
							})
							//临时取卷的消息通知,不进行跳转
							this.tzxxAction()
							this.countAllMessage()
							this.$Message.info('消息已读')
						}
					})
				}
				this.$Message.info('该消息无详情')
				return
			}

			this.$router.push({ name: url, query: { actInstId: actInstId, sqdId: id, from: this.fromUrl, selectIndex: this.selectIndex } })
		},

		searchBtn() {
			if (this.selectIndex == 0) {
				this.dbsxAction()
			} else if (this.selectIndex == 1) {
				this.tzxxAction()
			} else if (this.selectIndex == 3) {
				var params = {
					page: 1,
					limit: this.currPageSize
				}
				this.getYjxxData(params)
			}
		},

		processingStatusAction(val) {
			this.resetPages()
			this.processingStatus = val
			if (this.selectIndex == '0') {
				var params = {
					pageNo: 1,
					pageSize: this.currPageSize,
					isProc: val,
					type: '01',
					title: this.searchValue
				}
				this.getRvcMessagePageData(params)
			} else {
				var params = {
					page: 1,
					limit: this.currPageSize
				}
				this.getYjxxData(params)
			}
		},

		readingStatusAction(val) {
			this.resetPages()
			this.readingStatus = val
			var params = {
				pageNo: 1,
				pageSize: this.currPageSize,
				isRead: val,
				type: '02',
				title: this.searchValue
			}
			this.getRvcMessagePageData(params)
		},

		changePage(pageObj) {
			this.currentPage = pageObj.pageNo
			if (this.selectIndex == 0) {
				var params = {
					pageNo: pageObj.pageNo,
					pageSize: this.currPageSize,
					isProc: this.processingStatus,
					type: '01',
					title: this.searchValue
				}
				this.getRvcMessagePageData(params)
			} else if (this.selectIndex == 1) {
				var params = {
					pageNo: pageObj.pageNo,
					pageSize: this.currPageSize,
					isRead: this.readingStatus,
					type: '02',
					title: this.searchValue
				}
				this.getRvcMessagePageData(params)
			} else if (this.selectIndex == 2) {
				var params = {
					pageNo: pageObj.pageNo,
					pageSize: this.currPageSize
				}
				this.getMyApplyPageData(params)
			} else if (this.selectIndex == 3) {
				var params = {
					page: pageObj.pageNo,
					limit: this.currPageSize
				}
				this.getYjxxData(params)
			}
		},

		changePageSize(pageObj) {
			this.currPageSize = pageObj.pageSize
			if (this.selectIndex == 0) {
				var params = {
					pageNo: 1,
					pageSize: pageObj.pageSize,
					isProc: this.processingStatus,
					type: '01',
					title: this.searchValue
				}
				this.getRvcMessagePageData(params)
			} else if (this.selectIndex == 1) {
				var params = {
					pageNo: 1,
					pageSize: pageObj.pageSize,
					isRead: this.readingStatus,
					type: '02',
					title: this.searchValue
				}
				this.getRvcMessagePageData(params)
			} else if (this.selectIndex == 2) {
				var params = {
					pageNo: 1,
					pageSize: pageObj.pageSize
				}
				this.getMyApplyPageData(params)
			} else if (this.selectIndex == 3) {
				var params = {
					page: 1,
					limit: pageObj.pageSize
				}
				this.getYjxxData(params)
			}
		},

		//全部消息设置为已读
		allRead() {
			var requestParams = {
				appCode: serverConfig.dmsCode,
				procIdCard: this.user.idCard,
				msgType: '02'
			}
			this.$Post(this.API.BspApi.TZXX_READ, requestParams).then((res) => {
				if (res.success) {
					this.countAllMessage()
					this.resetPages()
					// var params = {
					//     pageNo : 1,
					//     pageSize : this.currPageSize,
					//     isRead : this.readingStatus,
					//     type : '02',
					//     title : this.searchValue
					// };
					// 在未读消息里点击了全部已读后跳到全部消息页面
					this.readingStatus = ''
					var params = {
						pageNo: 1,
						pageSize: this.currPageSize,
						isRead: '',
						type: '02',
						title: ''
					}
					this.getRvcMessagePageData(params)
				}
			})
		},

		//获取待办事项/通知消息数据
		getRvcMessagePageData(params) {
			params.appCode = serverConfig.dmsCode
			this.$Post(this.API.DmsApi.RVC_MESSAGE_DATA_BSP, params).then((res) => {
				if (res.success) {
					this.jaxx = res.rows
					this.dataTotal = res.count
				}
			})
		},
		//获取预警消息数据
		getYjxxData(params) {
			// params.centerId = this.centerId
			// params.imsModel = this.imsModel
			params.content = this.searchValue
			// params.dmsFxyjRule = serverConfig.dmsFxyjRule
			params.roleSql = this.roleSql
			if (this.processingStatus == '1') {
				params.clzt = '01'
			} else if (this.processingStatus == '0') {
				params.clzt = '00'
			}

			this.$Post(this.API.DmsApi.ALARM_YJXX_DATA, params).then((res) => {
				if (res.success) {
					var da = res.data
					this.dataTotal = res.count
					if (da) {
						for (var j = 0; j < da.length; j++) {
							da[j].bamj = da[j].info ? da[j].bamj : '未知'
							da[j].cbdwMc = da[j].info ? da[j].cbdwMc : '未知'
							da[j].yjsj = da[j].info ? da[j].yjsj : '未知'
							da[j].jzmc = da[j].info ? da[j].jzmc : '未知'
							if (da[j].alarmType == '07') {
								da[j].title = (da[j].info ? da[j].info : '') + ',请及时处理！'
							} else if (da[j].alarmType == '08' && da[j].jqbh) {
								da[j].title = (da[j].jzbh ? '【' + da[j].jzbh + '】 ' : '') + (da[j].info ? da[j].info : '') + ',请及时处理！'
							} else {
								if (da[j].jzmc) {
									da[j].title = ((da[j].jzmc && da[j].jzbh) ? '【' + da[j].jzmc + '-' + da[j].jzbh + '】' : '') + (da[j].info ? da[j].info : '') + ',请及时处理！'
								} else {
									da[j].title = ((da[j].jzbh && da[j].jzmc) ?  '【' + da[j].jzbh + '】' : '') + (da[j].info ? da[j].info : '') + ',请及时处理！'
								}
							}
						}
					}
					this.jaxx = da
				}
			})
		},

		//获取顶部消息数量
		countAllMessage() {
			var request_params = {
				// centerId: getCenter(),
				// imsModel: getImsModel(),
				// dmsFxyjRule: serverConfig.dmsFxyjRule,
				roleSql: this.roleSql,
				appCode: serverConfig.dmsCode
			}

			this.$Post(this.API.DmsApi.COUNT_ALL_MESSAGE, request_params)
				.then((res) => {
					if (res.success) {
						this.messageCount.dbCount = res.dbCount
						this.messageCount.xxCount = res.xxCount
						this.messageCount.yjCount = res.yjCount
					}
				})
				.catch({})
		},

		//我发起的
		getMyApplyPageData(params) {
			this.$Post(this.API.DmsApi.MY_APPLY_MESSAGE_DATA, params).then((res) => {
				if (res.success) {
					this.jaxx = res.rows
					this.dataTotal = res.count
				}
			})
		},

		resetPages() {
			this.currentPage = 1
			this.currPageSize = 10
		}
	}
}
</script>
<style lang="less" scoped>

.video-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0.1rem;
    .video-item {
		cursor: pointer;
        min-width: 0.9rem;
        padding: 0 0.1rem;
		margin-right: 0.1rem;
        text-align: center;
        background-color: #ffffff;
        border: 1px solid #087EFF;
        color: #087EFF;
        border-radius: 4px;
    }
    .video-item-active {
        color: #ffffff;
        background-color: #087EFF;
    }
}
.polive_message_main {
	flex: 1;
	padding-bottom: 0.2rem;
	background-color: #fff;
	.tab-item-content {
		display: inline-block;
		position: relative;
		line-height: 1;
	}
}
.polive_message_main_head {
	width: 100%;
	display: flex;
	justify-content: space-between;
	border-bottom: solid 1px #ebf0f5;
	padding: 0rem 0.2rem 0rem 0;
	background: #fff;
}
.polive_message_main_head .left {
	display: flex;
	align-items: center;
}
.polive_message_main_head .left li {
	font-size: 0.16rem;
	padding: 0 0.2rem;
	box-sizing: border-box;
	cursor: pointer;
}
.polive_message_main_head .left li span {
	display: inline-block;
	line-height: 0.3rem;
}
.polive_message_main_head .left li i {
	display: inline-block;
	padding: 0 0.1rem;
	height: 0.18rem;
	line-height: 0.18rem;
	text-align: center;
	background: #e60012;
	color: #fff;
	border-radius: 0.09rem;
	margin-left: 0.1rem;
}
.polive_message_main_head .left li span.active {
	color: #3179f5;
	border-bottom: solid 2px #3179f5;
}
.polive_message_main_head .right {
	display: flex;
	align-items: center;
}
.polive_message_main_head .right .searchBox2 {
	position: relative;
	width: 3.5rem;
	margin-right: 0.5rem;
}
/* .polive_message_main_head .right .searchBox2 input{
        width: 3.5rem;
        height: 0.35rem;
        font-size: 0.14rem;
        padding-left: 0.1rem;
        box-sizing: border-box;
        border: solid 1px #e5e5e5;
    }
    .polive_message_main_head .right .searchBox2 .layui-icon-search{
        position: absolute;
        right: 0.1rem;
        top: 0.06rem;
        font-size: 0.2rem;
        cursor: pointer;
    } */
.layui-form-item {
	margin-bottom: 0;
}
.layui-input-block {
	margin-left: 0.4rem;
}
.layui-form-radio > i {
	font-size: 0.2rem;
}
.layui-form-radio > i:hover,
.layui-form-radioed > i {
	color: #3179f5;
}
.messageList {
	width: 100%;
	height: 100%;
}
.ivu-icon-ios-search-outline {
	font-size: 0.28rem;
	position: absolute;
	right: 0.08rem;
	top: 0.04rem;
	cursor: pointer;
}
.commonListBox {
	width: 100%;
	height: 85vh;
	background: #fff;
	overflow: auto;
	padding: 0.1rem;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}
.commonListBox .common_value {
	flex: 1;
	overflow: auto;
}
.commonList_item {
	padding: 0.2rem;
	box-sizing: border-box;
	border-bottom: solid 1px #e5e5e5;
}
.commonList_item .title {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.commonList_item .tag {
	padding: 0.05rem 0.1rem;
	min-width: 0.8rem;
	text-align: center;
	font-size: 0.14rem;
	margin-right: 0.4rem;
	letter-spacing: initial;
}
.commonList_item .content {
	max-width: 13rem;
	font-size: 0.16rem;
	font-weight: bold;
	color: #333;
	a {
		color: #087eff;
	}
}
.commonList_item .content span {
	font-weight: bold;
}
.tag_xs {
	color: #e60012;
}
.tag_xz {
	color: #32b16c;
}
.tag_aq {
	color: #3179f5;
}
.yqrksp {
	background-color: #eee;
	color: #3179f5;
}
.jzdcsp {
	background-color: #eee;
	color: #32b16c;
}
.jzjysp {
	background-color: #eee;
	color: #abb619;
}
.stjzjysp {
	background-color: #eee;
	color: #19b1b6;
}
.bccl {
	background-color: #eee;
	color: #32b16c;
}
.status {
	color: #e60012;
	display: flex;
	align-items: center;
	letter-spacing: initial;
}
.status i {
	display: inline-block;
	width: 0.12rem;
	height: 0.12rem;
	border-radius: 50%;
	margin-right: 0.1rem;
}
.status_wcl {
	color: #e60012;
}
.status_wcl i {
	background: #e60012;
}
.status_ycl {
	color: #3179f5;
}
.status_ycl i {
	background: #3179f5;
}
.commonList_item .from {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 0.32rem;
}
.commonList_item .from > div {
	display: flex;
	align-items: center;
}
.commonList_item .from label {
	color: #000;
}
.userName {
	margin-right: 1.6rem;
}
.spzt_wsp {
	color: #3179f5;
}
.spzt_spth {
	color: #f83;
}
.spzt_qxsq {
	color: #61959c;
}
.spzt_spbtg {
	color: #e60012;
}
.spzt_sptg {
	color: #32b16c;
}
.alert-box-header {
	display: flex;
	align-items: center;
	width: 280px;
	font-size: 14px;
	.alert-box-title {
		font-weight: bold;
		margin-left: 10px;
	}
	.alert-box-header-icon {
		display: block;
		width: 18px;
		height: 18px;
		background: url('../../assets/images/components/alert-confirm.png');
	}
}
.alert-box-btn {
	display: flex;
	flex-direction: row-reverse;
}

/deep/ .ivu-modal-footer {
	.ivu-btn {
		color: #087eff;
		width: 0.6rem;
		margin-right: 0;
	}
	.ivu-btn-primary {
		background: #087eff;
		color: #ffffff;
	}
}
</style>
