<template>
	<div class="main-content">
		<div class="jqxq_content_box">
			<div class="mbxdh">
				<Breadcrumb>
					<BreadcrumbItem :to="'/dms/message/message?from=' + messageFromUrl">消息中心</BreadcrumbItem>
					<BreadcrumbItem :to="'/dms/message/message?from=' + messageFromUrl">{{ $route.query.selectIndex == '2' ? '我发起的' : $route.query.selectIndex == '1' ? '通知消息' : '待办事项' }}</BreadcrumbItem>
					<BreadcrumbItem class="content_title_text">电子卷宗案卷导出</BreadcrumbItem>
				</Breadcrumb>
			</div>
			<div class="ajxxBox">
				<div class="sys-sub-title">案件信息</div>
				<div style="padding: 0.2rem; background: #fff">
					<Table :columns="tableColumns" :data="tableData"></Table>
				</div>
				<div style="padding: 0.2rem 0">
					<div class="sys-sub-title">申请消息</div>
					<Form class="form-col-3" :label-width="150" style="background: #fff; padding-top: 10px">
						<FormItem label="是否添加水印" class="form-item">
							<span>
								{{ isWatermark }}
							</span>
						</FormItem>
						<FormItem label="是否带标注" class="form-item">
							<span>
								{{ labelType }}
							</span>
						</FormItem>
						<FormItem label="申请理由" class="form-item">
							<span>
								{{ sqly }}
							</span>
						</FormItem>
						<FormItem label="申请说明">
							<span>
								{{ sqsm }}
							</span>
						</FormItem>
						<FormItem label="附件">
							<div v-show="uploadList.length > 0" style="max-height: 300px; overflow: auto; margin-top: 20px">
								<DownloadFiles :uploadFiles="uploadList"></DownloadFiles>
							</div>
						</FormItem>
					</Form>
					<!-- <ul  style="padding-left: 110px;background: #fff;display:flex;flex-wrap: wrap;padding-bottom: 10px;" v-if="uploadList.length > 0">
                    <li  class="ivu-upload-list-file ivu-upload-list-file-finish " v-for="(item, index) in uploadList" :key="index" @click.stop="downLoad(item.url)">
                        <span>
                            <i class="ivu-icon ivu-icon-ios-image"></i>
                            {{ item.fileName }}
                        </span>
                    </li>
                </ul> -->
				</div>
				<div class="ajxxBox">
					<div class="sys-sub-title">导出材料</div>
					<div class="jzjbxxBox" style="padding: 0.2rem 0.65rem; box-sizing: border-box; background: #fff">
						<div class="allJzcl_tree_box">
							<div class="tree_box">
								<VueZtree :nodes="treeData" :setting="setting" @onCreated="handleCreated" />
							</div>
						</div>
					</div>
				</div>
				<div class="ajxxBox">
					<div class="sys-sub-title">审批历史</div>
					<div style="background: #fff">
						<s-general-history :actInstId="actInstId" :showRevokeBtn="showRevokeBtn" :showModifyBtn="showModifyBtn"></s-general-history>
					</div>
				</div>
				<div class="bsp-base-fotter">
					<s-general-audit ref="approval" @audit-close="audit_close" :showFileUpload="showFileUpload" :beforeOpen="beforeOpen" :beforeAudit="beforeAudit" :auditComplete="auditComplete" :actInstId="actInstId" :showcc="showcc" :msgUrl="msgUrl" :msgTit="msgTit" :variables="variables" :businessId="businessId" :module="appCode" :modalWidth="modalWidth" :modalHeight="modalHeight">
						      
						<my-btn slot="func">审批</my-btn>
						 
					</s-general-audit>
					<div class="text"></div>
					<my-btn plain @click="cancelBtn()">返回</my-btn>
					<div class="text"></div>
					<my-btn class="ajgl_search_qd_btn" v-if="exportShow" @click="exportAction">导出</my-btn>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import config from '@/config'
import { sGeneralAudit } from 'sd-general-audit'
import { sGeneralHistory } from 'sd-general-history'
import VueZtree from 'vue-giant-tree'
import DownloadFiles from '_c/downloadFiles'
import FileSaver from 'file-saver'

export default {
	components: {
		VueZtree,
		sGeneralAudit,
		DownloadFiles,
		sGeneralHistory
	},
	data() {
		return {
			treeData: [],
			appCode: serverConfig.dmsCode,
			showFileUpload: false,
			showcc: false,
			actInstId: '0',
			businessId: '0',
			modalWidth: '800',
			modalHeight: '500',
			showModifyBtn: false,
			showRevokeBtn: false,
			msgUrl: 'dmsAjdcApprove',
			msgTit: '【通知】电子卷宗案卷导出审批通过',
			variables: {},
			tableData: [],
			tableColumns: [
				{
					title: '案件编号',
					key: 'ajbh',
					align: 'center',
					tooltip: true
				},
				{
					title: '案件名称',
					key: 'ajmc',
					align: 'center',
					tooltip: true
				},
				{
					title: '案件类型',
					key: 'ajlxName',
					align: 'center'
				},
				{
					title: '案件状态',
					key: 'ajztName',
					align: 'center'
				},

				{
					title: '承办单位',
					key: 'cbdwMc',
					align: 'center'
				},
				{
					title: '承办人',
					key: 'cbrXm',
					align: 'center'
				},
				{
					title: '卷宗状态',
					key: 'jzztName',
					align: 'center'
				}
			],
			setting: {
				check: {
					enable: false
				},
				data: {
					key: {
						title: 'name'
					},
					simpleData: {
						enable: true,
						idKey: 'id',
						pIdKey: 'parentId',
						rootPId: '0'
					}
				},
				view: {
					// addDiyDom: '',
					showLine: false,
					showIcon: true
				}
			},
			ajbh: '',
			ajmc: '',
			ajlx: '',
			ajzt: '',
			cbdw: '',
			cbr: '',
			isWatermark: '',
			labelType: '',
			jzzt: '',
			sqly: '',
			sqsm: '',
			sqrXm: '',
			uploadList: [],
			exportShow: false,
			sqdId: '',
			messageFromUrl: '',
			isBack: false
		}
	},
	created() {
		if (this.$route.query.actInstId) {
			this.actInstId = this.$route.query.actInstId
		}
	},
	mounted() {
		this.initData()
		this.messageFromUrl = this.$route.query.from
	},
	methods: {
		initData() {
			if (this.$route.query.actInstId) {
				this.actInstId = this.$route.query.actInstId
			}
			var params = {}
			params.actInstId = this.actInstId
			params.sqdId = this.$route.query.sqdId
			this.$Post(this.API.DmsApi.DMS_EXPORT_DATA, params)
				.then((res) => {
					if (res.success) {
						var data = res.data

						if (data.fileList) {
							this.uploadList = data.fileList
						}

						var ajxx = data.ajxx
						this.tableData.push(ajxx)
						this.businessId = ajxx.ajbh
						this.ajbh = ajxx.ajbh
						this.ajmc = ajxx.ajmc
						this.ajlx = ajxx.ajlxName
						this.cbdw = ajxx.cbdwMc
						this.cbr = ajxx.cbrXm
						this.ajzt = ajxx.ajztName
						this.jzzt = ajxx.jzztName

						var sqd = data.sqd
						this.sqly = sqd.sqly
						this.sqsm = sqd.sqsm
						this.sqrXm = sqd.sqrXm
						this.isWatermark = sqd.isWaterMark == '0' ? '否' : '是'
						this.labelType = sqd.labelType == '0' ? '否' : '是'
						this.sqdId = sqd.id
						if (data.isSqr == '1' && sqd.spzt == '1') {
							this.exportShow = true
						}
						this.treeData = JSON.parse(sqd.exportCatalogData)

						this.msgTit = data.title
						//'已审批${approvalStatusStr_node_sp}!'
						this.variables.titleStart = this.msgTit
					}
				})
				.catch({})
		},
		handleCreated(ztreeObj) {
			// 拿到ztree实例
			this.ztreeObj = ztreeObj

			function filter(node) {
				return !node.isParent
			}
			var nodes = ztreeObj.getNodesByFilter(filter) // 查找节点集合

			if (nodes.length > 0) {
				nodes.forEach((item) => {
					var html = '<span class="bookMark" id="' + item.id + '"></span>'
					$('#' + item.tId + '_a').append(html)
					if (item.hasLabel) {
						$('#' + item.id).show()
					} else {
						$('#' + item.id).hide()
					}
				})
			}
			this.ztreeObj.expandAll(false)
			// this.ztreeObj.checkAllNodes(true);
		},
		exportAction() {
			var params = {}
			params.sqdId = this.sqdId
			this.$Post(this.API.DmsApi.GET_PDF_EXPORT_URL, params).then((res) => {
				if (res.success) {
					var data = res.data
					var urlArr = data.exportUrl.split(',')
					this.bulkDownload(urlArr)
				} else {
					this.$alert({
						type: 'error',
						title: '温馨提示',
						content: res.msg
					})
				}
			})
		},
		//  批量下载
		downloadFile(url) {
			let fileName = url.split('/')
			fileName = fileName[fileName.length - 1]
			FileSaver.saveAs(url, fileName + '.pdf')
			// const a = document.createElement('a');
			// let blob = new Blob([url]);
			// a.href = URL.createObjectURL(blob);
			// a.href = url;
			// let fileName  = url.split('/');
			// fileName = fileName[fileName.length - 1]
			// a.download = fileName
			// a.click();
			// URL.revokeObjectURL(blob);

			// window.open("http://192.168.37.26:8080/default/20220805/16/29/1/C276A02A26254F57B70249D7F6CB6B7D.pdf")
			// const iframe = document.createElement("iframe");
			// iframe.style.display = "none"; // 防止影响页面
			// iframe.style.height = 0; // 防止影响页面
			// iframe.src = "http://192.168.37.26:8080/default/20220805/16/29/1/C276A02A26254F57B70249D7F6CB6B7D.pdf";
			// document.body.appendChild(iframe); // 这一行必须，iframe挂在到dom树上才会发请求
			// // 5分钟之后删除
			// setTimeout(() => {
			//     iframe.remove();
			// }, 5 * 60 * 1000);
		},

		bulkDownload(jsonUrlList) {
			for (let i = 0; i < jsonUrlList.length; i++) {
				//循环遍历调用downloadFile方法
				const url = jsonUrlList[i]
				this.downloadFile(url)
			}
		},
		downLoad(item) {
			let a = document.createElement('a')
			a.href = item.replace(serverConfig.uploadUrl, serverConfig.downloadUrl)
			a.click()
		},
		beforeOpen() {
			return new Promise((resolve, reject) => {
				resolve(true)
			})
		},
		beforeAudit(data) {
			return new Promise((resolve, reject) => {
				// 模拟异步方法
				if (data.formData.isApprove == '5') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已审批同意！'
				} else if (data.formData.isBack && data.formData.backNodeId == 'node_fqsq') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已被退回！'
					this.isBack = true
				} else if (data.formData.isApprove == '6') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已取消申请！'
				} else {
					this.msgTit = this.msgTit + '请及时审批！'
				}
				resolve(true)
			})
		},
		auditComplete(data) {
			return new Promise((resolve, reject) => {
				let that = this
				let da = data.data
				//模拟异步方法
				setTimeout(function () {
					//审批成功业务处理逻辑
					var spzt = da.bpmTrail.isApprove
					var taskKey = da.bpmTrail.taskKey

					var spyj = da.bpmTrail.approvalContent
					var params = {}
					params.actInstId = that.actInstId
					params.spyj = da.bpmTrail.approvalContent
					if (spzt == '5') {
						params.spzt = 1
					} else if (spzt == '6') {
						if (taskKey == 'node_fqsq') {
							params.spzt = 3
						} else {
							params.spzt = 2
						}
					} else if (that.isBack) {
						params.spzt = 4
					} else {
						params.spzt = 0
					}
					that
						.$Post(that.API.DmsApi.DMS_EXPORT_APPROVE, params)
						.then((res) => {
							if (res.success) {
								that.$alert({
									type: 'warning',
									title: '温馨提示',
									content: '流程审批成功',
									onOk: function () {
										that.$router.push({ path: that.$store.state.user.goBackUrl, query: { from: that.$route.query.from, selectIndex: that.$route.query.selectIndex } })
									}
								})
								resolve(true)
							} else {
								that.$alert({
									type: 'error',
									content: '流程审批失败',
									onOk: function () {
										that.$router.push({ path: that.$store.state.user.goBackUrl, query: { from: that.$route.query.from, selectIndex: that.$route.query.selectIndex } })
									}
								})
								resolve(false)
							}
						})
						.catch({})
				}, 500)
			})
		},
		testErr(data) {
			this.$alert({
				type: 'error',
				content: data.msg
			})
		},
		audit_close() {
			return new Promise((resolve, reject) => {
				resolve(true)
			})
		},
		cancelBtn() {
			this.$router.push({ path: this.$store.state.user.goBackUrl, query: { from: this.$route.query.from, selectIndex: this.$route.query.selectIndex } })
		}
	}
}
</script>
<style lang="less" scoped>
.history-audit /deep/ .ivu-timeline-item-tail {
	top: 15px !important;
}
.ivu-upload-list-file:hover {
	background: none !important;
}
.ivu-upload-list-file {
	margin-left: 15px !important;
}
.history-audit /deep/ .task-title {
	font-size: 17px !important;
}
.history-audit /deep/ .task-container {
	margin-left: 10px !important;
}
.history-audit /deep/ .content {
	background: none;
}
.history-audit /deep/ .ivu-timeline-item-head {
	background: none;
}
.content_title {
	margin-top: 10px;
}
</style>
<style lang="less" scoped>
.ivu-form .ivu-form-item {
	width: 100%;
	margin-bottom: 0;
}

.tableDiv {
	min-height: 1rem;
	width: 100%;
	padding-left: 40px;
	padding-right: 40px;
	padding-top: 10px;
}

.title {
	width: 100%;
	height: 0.4rem;
	// background: #F7FAFC;
	padding-left: 0.2rem;
	box-sizing: border-box;
	font-size: 0.18rem;
	color: #3179f5;
	font-weight: bold;
	letter-spacing: 0;
	line-height: 0.4rem;
}

.text {
	padding-left: 0.12rem;
}

.ajxxBox {
	background: #fff;
	padding-top: 0.1rem;
	padding-bottom: 0.2rem;
}

.sys-sub-title {
	padding: 0.2rem 0;
	margin: 0;
}

.form-col-3 {
	.ivu-form-item:nth-child(-n + 3) {
		width: 33.33%;
	}
	.ivu-form-item {
		padding: 8px;
	}
	/deep/ .ivu-form-item-content {
		margin-left: 0 !important;
	}
	.ivu-form-item-content {
		margin-left: 0 !important;
	}
}

.btn_box {
	margin-right: 0.13rem;
}
</style>
