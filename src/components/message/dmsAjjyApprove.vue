<template>
	<div class="main-content">
		<div class="jqxq_content_box">
			<div class="mbxdh">
				<Breadcrumb>
					<BreadcrumbItem :to="'/dms/message/message?from=' + messageFromUrl">消息中心</BreadcrumbItem>
					<BreadcrumbItem :to="'/dms/message/message?from=' + messageFromUrl">{{ $route.query.selectIndex == '2' ? '我发起的' : $route.query.selectIndex == '1' ? '通知消息' : '待办事项' }}</BreadcrumbItem>
					<BreadcrumbItem>电子卷宗案卷借阅</BreadcrumbItem>
				</Breadcrumb>
			</div>
			<div class="ajxxBox">
				<div class="sys-sub-title">案件信息</div>
				<div class="ajDetail">
					<div class="text">案件编号：{{ ajbh }}</div>
					<div class="text">案件类型：{{ ajlx }}</div>
					<div class="text">案件名称：{{ ajmc }}</div>
					<div class="text">承办单位：{{ cbdw }}</div>
					<div class="text">承办人：{{ cbr }}</div>
					<div class="text">嫌疑人姓名：{{ xyrxm }}</div>
				</div>
			</div>
			<div class="ajxxBox">
				<div class="sys-sub-title">申请信息</div>
				<div class="ajDetail">
					<div class="text">借阅分卷：{{ fjmc }}</div>
					<div class="text">借阅截止时间：{{ jyjssj }}</div>
					<div class="text">申请理由：{{ sqly }}</div>
					<div class="text">申请说明：{{ sqsm }}</div>
					<div class="text">附件：</div>
				</div>

				<div v-show="uploadList.length > 0" style="max-height: 300px; overflow: auto">
					<DownloadFiles :uploadFiles="uploadList" style="position: relative; top: -0.25rem"></DownloadFiles>
				</div>

				<!-- <ul class="ivu-upload-list" v-if="uploadList.length > 0" >
			<li
			class="ivu-upload-list-file ivu-upload-list-file-finish"
			v-for="(item, index) in uploadList"
			:key="index"
			@click.stop="downLoad(item)"
			>
				<span>
				<i class="ivu-icon ivu-icon-ios-image"></i>
				{{ item.fileName }}
				</span>
			</li>
		</ul> -->
			</div>
			<div class="ajxxBox">
				<div class="sys-sub-title">审批历史</div>
				<div style="background: #fff">
					<s-general-history :actInstId="actInstId" :showRevokeBtn="showRevokeBtn" :showModifyBtn="showModifyBtn"></s-general-history>
				</div>
			</div>
			<div class="bsp-base-fotter">
				<s-general-audit ref="approval" @audit-close="audit_close" :showFileUpload="showFileUpload" :beforeOpen="beforeOpen" :beforeAudit="beforeAudit" :auditComplete="auditComplete" :actInstId="actInstId" :showcc="showcc" :msgUrl="msgUrl" :msgTit="msgTit" :variables="variables" :businessId="businessId" :module="appCode" :modalWidth="modalWidth" :modalHeight="modalHeight">
					<my-btn slot="func" class="btn_box">审批</my-btn>
				</s-general-audit>
				<my-btn plain @click="cancelBtn()">返回</my-btn>
			</div>
		</div>
	</div>
</template>
<script>
import config from '@/config'
import { sGeneralAudit } from 'sd-general-audit'
import { sGeneralHistory } from 'sd-general-history'
import DownloadFiles from '_c/downloadFiles'
export default {
	components: {
		sGeneralAudit,
		sGeneralHistory,
		DownloadFiles
	},
	data() {
		return {
			appCode: serverConfig.dmsCode,
			showFileUpload: false,
			showcc: false,
			actInstId: '0',
			businessId: '0',
			modalWidth: '800',
			modalHeight: '500',
			showModifyBtn: false,
			showRevokeBtn: false,
			msgUrl: 'dmsAjjyApprove',
			msgTit: '【通知】电子卷宗案卷借阅审批通过',
			variables: {},
			ajbh: '',
			ajmc: '',
			ajlx: '',
			cbdw: '',
			cbr: '',
			xyrxm: '',
			fjmc: '',
			jyjssj: '',
			sqly: '',
			sqsm: '',
			sqrXm: '',
			uploadList: [],
			messageFromUrl: '',
			isBack: false
		}
	},
	created() {
		this.initData()
		this.messageFromUrl = this.$route.query.from
	},
	methods: {
		cancelBtn() {
			//this.$router.push(this.$store.state.user.goBackUrl);
			this.$router.push({ path: this.$store.state.user.goBackUrl, query: { from: this.$route.query.from, selectIndex: this.$route.query.selectIndex } })
		},
		initData() {
			if (this.$route.query.actInstId) {
				this.actInstId = this.$route.query.actInstId
			}
			var params = {}
			params.actInstId = this.actInstId
			params.sqdId = this.$route.query.sqdId
			this.$Post(this.API.DmsApi.DMS_BORROW_DATA, params)
				.then((res) => {
					if (res.success) {
						var data = res.data

						if (data.fileList) {
							this.uploadList = data.fileList
						}

						var ajxx = data.ajxx
						this.businessId = ajxx.ajbh
						this.ajbh = ajxx.ajbh
						this.ajmc = ajxx.ajmc
						this.ajlx = ajxx.ajlxName
						this.cbdw = ajxx.cbdwMc
						this.cbr = ajxx.cbrXm
						this.xyrxm = ajxx.xyrxm

						var sqd = data.sqd
						this.sqly = sqd.sqly
						this.sqsm = sqd.sqsm
						this.fjmc = sqd.fjmc
						this.sqrXm = sqd.sqrXm
						this.jyjssj = sqd.jyjssj ? sqd.jyjssj.substring(0, 10) : ''

						this.msgTit = data.title
						//'已审批${approvalStatusStr_node_sp}!'
						this.variables.titleStart = this.msgTit
					}
				})
				.catch({})
		},
		downLoad(item) {
			let a = document.createElement('a')
			a.href = item.url.replace(serverConfig.uploadUrl, serverConfig.downloadUrl)
			a.click()
		},
		beforeOpen() {
			return new Promise((resolve, reject) => {
				resolve(true)
			})
		},
		beforeAudit(data) {
			return new Promise((resolve, reject) => {
				// 模拟异步方法
				if (data.formData.isApprove == '5') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已审批同意！'
				} else if (data.formData.isBack && data.formData.backNodeId == 'node_fqsq') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已被退回！'
					this.isBack = true
				} else if (data.formData.isApprove == '6') {
					this.msgTit = this.msgTit.replace(this.sqrXm, '您') + '已取消申请！'
				} else {
					this.msgTit = this.msgTit + '请及时审批！'
				}
				resolve(true)
			})
		},
		auditComplete(data) {
			return new Promise((resolve, reject) => {
				let that = this
				let da = data.data
				//模拟异步方法
				setTimeout(function () {
					//审批成功业务处理逻辑
					var spzt = da.bpmTrail.isApprove
					var taskKey = da.bpmTrail.taskKey

					var spyj = da.bpmTrail.approvalContent
					var params = {}
					params.actInstId = that.actInstId
					params.spyj = da.bpmTrail.approvalContent
					if (spzt == '5') {
						params.spzt = 1
					} else if (spzt == '6') {
						if (taskKey == 'node_fqsq') {
							params.spzt = 3
						} else {
							params.spzt = 2
						}
					} else if (that.isBack) {
						params.spzt = 4
					} else {
						params.spzt = 0
					}
					that
						.$Post(that.API.DmsApi.DMS_BORROW_APPROVE, params)
						.then((res) => {
							if (res.success) {
								that.$alert({
									type: 'success',
									title: '温馨提示',
									content: '流程审批成功',
									onOk: function () {
										//that.$router.push(that.$store.state.user.goBackUrl);
										that.$router.push({ path: that.$store.state.user.goBackUrl, query: { from: that.$route.query.from, selectIndex: that.$route.query.selectIndex } })
										//that.$router.push({name:'message'});
									}
								})
								resolve(true)
							} else {
								that.$alert({
									type: 'error',
									title: '温馨提示',
									content: '流程审批失败',
									onOk: function () {
										//that.$router.push(that.$store.state.user.goBackUrl);
										that.$router.push({ path: that.$store.state.user.goBackUrl, query: { from: that.$route.query.from, selectIndex: that.$route.query.selectIndex } })
									}
								})
								resolve(false)
							}
						})
						.catch({})
				}, 500)
			})
		},
		testErr(data) {
			this.$alert({
				type: 'error',
				content: data.msg
			})
		},
		audit_close() {
			return new Promise((resolve, reject) => {
				resolve(true)
			})
		}
	}
}
</script>
<style lang="less" scoped>
.history-audit /deep/ .ivu-timeline-item-tail {
	top: 15px !important;
}
.ajDetail {
	background: #fff;
	display: flex;
	flex-wrap: wrap;
	padding: 8px 15px;
}
.ajDetail .text {
	width: 50%;
	padding: 8px;
}
.ivu-upload-list {
	margin-top: 0 !important;
	background: #fff;
	display: flex;
	flex-wrap: wrap;
	padding-left: 30px;
}
.ivu-upload-list-file:hover {
	background: none !important;
}
.ivu-upload-list-file {
	margin-left: 15px !important;
}
.history-audit /deep/ .task-title {
	font-size: 17px !important;
}
.history-audit /deep/ .task-container {
	margin-left: 10px !important;
}
.history-audit /deep/ .content {
	background: none;
}
.history-audit /deep/ .ivu-timeline-item-head {
	background: none;
}
.content_title {
	margin-top: 10px;
}
</style>
<style lang="less" scoped>
.tableDiv {
	min-height: 1rem;
	width: 100%;
	padding-left: 40px;
	padding-right: 40px;
	padding-top: 10px;
}
.title {
	width: 100%;
	height: 0.4rem;
	// background: #F7FAFC;
	padding-left: 0.2rem;
	box-sizing: border-box;
	font-size: 0.18rem;
	color: #3179f5;
	font-weight: bold;
	letter-spacing: 0;
	line-height: 0.4rem;
}
.text {
	padding-left: 40px;
}
.ajxxBox {
	background: #fff;
	padding-top: 0.1rem;
	padding-bottom: 0.2rem;
}

.sys-sub-title {
	padding: 0.2rem 0;
	margin: 0;
}

.form-col-3 {
	.ivu-form-item:nth-child(-n + 3) {
		width: 33.33%;
	}
	.ivu-form-item {
		padding: 8px;
	}
}

.btn_box {
	margin-right: 0.13rem;
}
</style>
