<template>
    <div>
      <p class="sm-title">药品库存预警</p>
      <div class="bq-Flex" style="padding-top: 0px;">
         <div v-for="(item,index) in dataArr" :key="index" class="children">
            <p :title="item.name" class="text-ellipsis">{{ item.name }}</p>
            <p>({{ item.value }})</p>
         </div>
      </div>
      <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
  </template>

  <script>
  export default {
     data(){
      return{
        spinShow:false,
        dataArr:[
          {name:'吡嗪酰胺片',kc:10},
          {name:'地高辛片',kc:10},
          {name:'跌打万花油',kc:10},
          {name:'恩替卡韦片',kc:10},
          {name:'缬沙坦胶囊',kc:10},
          {name:'硫软膏',kc:10},
          {name:'利福平胶囊',kc:10},
          {name:'螺内酯片',kc:10},
          {name:'保济丸',kc:10},
          {name:'三七化痔丸',kc:10},
          {name:'肾衰宁片',kc:10},
          {name:'秋水仙碱片',kc:10},
          {name:'缬沙坦胶囊',kc:10},
          {name:'硫软膏',kc:10},
          {name:'利福平胶囊',kc:10},
          {name:'螺内酯片',kc:10},
        ]
      }
     },
     mounted(){
      this.getData('syypkcyj','dataArr')
    },
   methods:{
    getData(mark,dataTip){
        this.spinShow=true
        this.$store.dispatch("authGetRequest", {
          url:this.$path.get_executeMultiQuery,// '/bsp-com/com/form/handle/executeMultiQuery',
          params: { mark: mark },
        }).then(res => {
          if (res.success) {
            this.spinShow=false
            this.$set(this,dataTip,res.data)
          }else{
            this.spinShow=false
          }
        })
    },
   }
  }
  </script>

  <style scoped lang="less">
  .children{
       width: 22%;
       text-align: center;
       border:1px solid #c8e1f4;
       line-height: 17px;
       padding: 3px 6px;
       color: #ff7640;
       font-size: 14px;
  }
  .children:nth-of-type(2n+1),.children:nth-of-type(3n+1){
      //  background: red;
  }
  .text-ellipsis {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
  </style>
