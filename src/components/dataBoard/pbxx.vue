<template>
  <div class="pbxx">
    <p class="title">医务人员排班信息</p>
    <div class="box">
      <div class="box_top">
        <div>医生：{{doctorObj.directorname}}</div>
        <div>护士长：{{doctorObj.headnursename}}</div>
      </div>
      <div class="box_bottom">
        <div v-for="item in dataList" class="item">
          <img :src="item.url" alt="">
          <div class="item_num">{{ item.num }}</div>
          <div class="item_name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import manImg from "@/assets/images/man.png"
import womanImg from "@/assets/images/woman.png"
export default {
  data() {
    return {
      spinShow:false,
      doctorObj:{
        directorname:'',
        headnursename:''
      },
      dataList: [
        { name: '医生', num: 0, url: manImg },
        { name: '护士', num: 0, url: womanImg },
        { name: '药师', num: 0, url: manImg },
      ]
    }
  },
  mounted(){
      this.getData('sypbxx')
    },
   methods:{
    getData(mark,dataTip){
        this.spinShow=true
        this.$store.dispatch("authGetRequest", {
          url:this.$path.get_executeMultiQuery,// '/bsp-com/com/form/handle/executeMultiQuery',
          params: { mark: mark },
        }).then(res => {
          if (res.success) {
            this.spinShow=false
            this.$set(this.doctorObj,'directorname',res.data[0].directorname)
            this.$set(this.doctorObj,'headnursename',res.data[0].headnursename)
            this.$set(this.dataList[0],'num',res.data[0].doctornum)
            this.$set(this.dataList[1],'num',res.data[0].nursenum)
            this.$set(this.dataList[2],'num',res.data[0].pharmacistsnum)
          }else{
            this.spinShow=false
          }
        })
    },
   }
}
</script>

<style lang="less" scoped>
.pbxx {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 2% 5%;
  background: linear-gradient(to bottom, #599afa, #FFFFFF,#599afa);
  color: #fff;

  .title {
    text-align: center;
    font-weight: bold;
    font-size: 18px;
  }

  .box {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .box_top {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      font-size: 15px;
      padding: 0 36px;

    }

    .box_bottom {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item {
        width: 33.3%;
        height: 100%;
        padding-top: 5%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;

        img {
          width: 40%;
          height: 30%;
        }

        .item_num {
          color: rgb(85, 85, 231);
          font-weight: bold;
          font-size: 18px;
        }

        .item_name {
          // color: rgb(85, 85, 231);
          font-weight: bold;
          font-size: 15px;
        }

      }

    }
  }

}
</style>
