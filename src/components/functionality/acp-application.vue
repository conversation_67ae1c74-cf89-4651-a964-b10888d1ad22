<template>
  <div class="appMain">
    <Tabs value="0" class="tab" @on-click="changeTab">
      <TabPane :label="item.name" :name="String(i)" v-for="(item, i) in appList" :key="i">
        <!-- 全部应用 -->
        <ul class="appUl" v-if="curName != 0">
          <template v-for="(item, i) in appTypeList">
            <li :key="i" class="appLi" v-if="item.showType">
              <div class="leftTitle">
                <span>{{ item.typeName }}</span
                >(<span class="numApp">{{ item.typeList.length || 0 }}</span
                >)
              </div>
              <div class="rightTitle">
                <div v-for="(val, index) in item.typeList" :key="index + 'A'"  :class="[val.noDisabled?'':'noDisabled','iconDiv']" @click="iconDrup(val)">
                  <p :class="['iconImg', 'animationIcon']">
                    <img :src="val.imgAppUrl" class="tbIcon" alt="" />
                  </p>
                  <p class="appName">{{ val.yymc }}</p>
                </div>
              </div>
            </li>
          </template>
        </ul>
        <ul class="appUl" v-else>
          <li class="appLi">
            <div class="rightTitle">
              <div v-for="(val, index) in myApp" :key="index + 'A'" :class="[val.noDisabled?'':'noDisabled','iconDiv']" @click="iconDrup(val)">
                <p :class="['iconImg', 'animationIcon']">
                  <img :src="val.imgAppUrl" class="tbIcon" alt="" />
                </p>
                <p class="appName">{{ val.yymc }}</p>
              </div>
              <div class="iconDiv" @click="addApp">
                <p class="iconImg addApp"><Icon type="md-add" :color="'#4C91FF'" :size="32" class="addIconA" /></p>
                <p class="appName">添加</p>
              </div>
            </div>
          </li>
        </ul>
      </TabPane>
    </Tabs>

    <!-- 我的应用 添加弹窗 -->
    <Modal v-model="modalShow" :mask-closable="false" :closable="false" class-name="bsp-role-assign" :width="1042" @on-ok="ok" @on-cancel="cancel">
      <div class="flow-modal-title" slot="header">
        <span style="font-size: 16px; font-weight: 400">自定义应用功能</span>
        <span @click="cancel" style="position: absolute; right: 10px; font-size: 36px; cursor: pointer">
          <i class="ivu-icon ivu-icon-ios-close" style="position: relative; top: -3px"></i>
        </span>
      </div>
      <addApply ref="addApply" :myApp="myApp" :appTypeListModel="appTypeList" v-if="myAppTag" />
    </Modal>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import addApply from './addApply.vue'
import tem from 'template_js'
import { getToken, parseQuery, getUserCache } from '@/libs/util'
import { SM4 } from 'gm-crypto' //SM4国产化加密身份证
import CryptoJS from 'crypto-js'
import cryptoObj from '@/libs/crypto.js'
export default {
  components: {
    addApply
  },
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    },
    itemOrder: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      zjmxRedirect: serverConfig.zjmxRedirect,
      operateList: [
        // { id: 1, title: '配置', className: 'ios-paper-outline' },
        { id: 2, title: '移除', className: 'ios-trash-outline' }
      ],
      modalShow: false,
      curName: 0,
      myAppTag: false,
      appList: [
        { id: 1, name: '常用应用' },
        { id: 2, name: '全部应用' }
      ],
      myApp: [],
      appTypeList: []
    }
  },
  methods: {
    ...mapActions(['authPostRequest', 'postRequest', 'authGetRequest']),
    changeTab(name) {
      this.curName = name
    },
    // 操作组件
    operate(val, item, index) {
      // this.layoutData.forEach(ele => {
      this.$set(item, 'editSetag', false)
      // })
      if (val.title == '移除') {
        this.confirmModal({ content: '是否确认移除？' }).then(async () => {
          this.$emit('submitDel', item, index)
        })
      } else {
        this.confirmModal({ content: '是否对该组件进行配置？' }).then(async () => {})
      }
    },
    // 配置
    edit(item, index) {
      this.$set(item, 'editSetag', !item.editSetag)
    },

    // 点击图标跳转页面
    iconDrup(val) {
      if (val.ljdz) {
        const key = '0123456789abcdeffedcba9876543210'
        let idCard
        let req = Object.assign({}, this.$route.query)
        if (parseQuery(val.ljdz) && parseQuery(val.ljdz).method == 'sm4') {
          idCard = SM4.encrypt(this.$store.getters.sessionUser.idCard, key, {
            inputEncoding: 'utf8',
            outputEncoding: 'base64'
          })
        } else if (parseQuery(val.ljdz) && parseQuery(val.ljdz).method == 'base64') {
          idCard = this.$Base64.encode(this.$store.getters.sessionUser.idCard)
        } else if (parseQuery(val.ljdz) && parseQuery(val.ljdz).method == 'zjmx') {
          idCard = cryptoObj.encrypt(getUserCache.getIdCard())
        } else {
          // idCard = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(this.$store.getters.sessionUser.idCard))
        }
        let zjmxRedirect = cryptoObj.encrypt(this.zjmxRedirect)
        let temValue = {
          // user: this.$store.getters.sessionUser,
          redirect: zjmxRedirect,
          req: req,
          token: getToken(),
          idCard: idCard,
          // userId: this.$store.getters.sessionUser.userId,
          // userJh: this.$store.state.common.loginId
        }
        let openUrl = this.template(val.ljdz, temValue)
        window.open(openUrl,'_self') //打开新窗口
      } else {

      }
    },
    getData() {
      let params={
          systemId:serverConfig.APP_ID,
      }
      this.authGetRequest({ url: `${this.$path.acp_wdyy_getAllApply}`, params: params }).then(res => {
        if (res.success) {
          // let markArr = localStorage.getItem('appComp') ? JSON.parse(localStorage.getItem('appComp')) : []
          // let appTemp= res.data &&  res.data.length>0?JSON.parse(JSON.stringify(res.data)):[]
          let appTemp = []
          // markArr.forEach(mark => {
            res.data.forEach(ele => {
              if (ele.typeList && ele.typeList.length > 0 ) {
                ele.typeList.forEach((item, i) => {
                  if (item.yylx != 1) {
                    item.imgAppUrl =item.yytb// require(`../../../public/image/${item.yytb}`)
                  }
                  if(item.ljdz && item.ljdz!='/'){
                    item.noDisabled=true
                  }else{
                    item.noDisabled=false
                  }
                })
                this.$set(
                  ele,
                  'typeList',
                  ele.typeList.filter(eleApp => eleApp.yylx != 1)
                )
                if (ele.typeList && ele.typeList.length > 0) {
                  ele.showType = true
                }
                appTemp.push(ele)
              }
            })
          // })
          this.appTypeList = appTemp
        }
      }),
        this.authGetRequest({ url: `${this.$path.acp_wdyy_getWdyyList}`, params: {} }).then(res => {
          if (res.success) {
            if (res.data && res.data.length > 0) {
              res.data.forEach(item => {
                if (item.yylx != 1) {
                  item.imgAppUrl =item.yytb// require(`../../../public/image/${item.yytb}`)
                  this.myApp.push(item)
                }
                 if(item.ljdz && item.ljdz!='/'){
                    item.noDisabled=true
                  }else{
                    item.noDisabled=false
                  }
              })
            }
            this.myAppTag = true
          }
        })
    },
    ok() {
      let selectId = []
      if (this.$refs.addApply.appTypeList && this.$refs.addApply.appTypeList.length > 0) {
        this.$refs.addApply.appTypeList.forEach((item, i) => {
          item.typeList.forEach(val => {
            if (val.check) {
              selectId.push(val.id)
            }
          })
        })
      }
      // let ids=this.$refs.addApply.xzApp.join(",")
      let ids = selectId.join(',')
      let params = {
        ids: ids
      }
      this.authGetRequest({ url: `${this.$path.acp_getWdyyList_save}`, params: params }).then(res => {
        if (res.success) {
          this.successModal({ content: res.msg })
          this.myApp = []
          this.getData()
          this.modalShow = false
        } else {
          this.errorModal({ content: res.msg })
        }
      })
    },
    cancel() {
      this.modalShow = false
    },
    addApp() {
      this.modalShow = true
      this.$refs.addApply.getData()
    },
    template(tpl, data) {
      tem.config({ sTag: '{{', eTag: '}}', escape: true })
      return tem(tpl, data)
    }
  },
  mounted() {
    this.getData()
  }
}
</script>
<style lang="less" scoped>
@import url('./addApply.less');
/deep/ .ivu-modal-body {
  padding: 16px !important;
  overflow-x: hidden !important;
}
</style>
