<template>
   <div class="" style="background: #fff;">
    <div class="bsp-base-tit">{{ title }}</div>
    <div class="bsp-base-content1">
      <fm
        @saveForm="saveForm"
        @cancal="cancal"
        :showSave="showSave"
        :showCancel="showCancel"
        :parameter="parameter"
      />
    </div>
  </div>
</template>

<script>
import fm from "@/components/fm/component/fm.vue";
export default {
  components: {
    fm,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    showSave: {
      type: Boolean,
      default: true,
    },
    showCancel:{
      type: Boolean,
      default: false,
    },
    parameter: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  methods: {
    saveForm(params, data) {
      this.$emit("saveForm", params, data);
    },
    // 关闭的回调   返回接口参数   formData数据
    cancal(params, data) {
      this.$emit("cancal", params, data);
    },
  },
};
</script>


