<template>
  <div class="fm-content" v-show="isDisplayForm">
    <div :id="fmMountElId"></div>
    <div class="bsp-base-fotter" v-show="showSave">
            <Button type="primary" :loading="saveLoading" @click="handleSubmit()" size="large"  class="ws-btn">{{saveBtnTxt}}</Button>
            <Button type="error" size="large"  class="ws-btn" @click="cancal">关&nbsp;&nbsp;闭</Button>
        </div>
        <div class="bsp-base-fotter" v-show="showCancel">
            <Button type="error" size="large"  class="ws-btn" @click="cancal">关&nbsp;&nbsp;闭</Button>
        </div>
  </div>
</template>
<script>
import fm from '../fm'
export default {
  mixins: [fm],
  data() {
    return {}
  }
}
</script>
<style>
@import '../styles/wenshu.css';
@import '../styles/formInfo.css';
@import '../styles/formForm.css';
.EvidenceObjTitle{
 max-width: 22%;
 white-space: nowrap;
 overflow: hidden;
 text-overflow: ellipsis;
}
.fm-content {
  padding: 10px 0 0;
  overflow: hidden;
  /* background: rgb(230, 230, 225); */
  background: #fff;
  position: relative;
  height:100%;
}
.fm-content>div:first-of-type {
  overflow-y: auto;
  height:calc(100% - 52px);
}
.fm-spin-icon-load {
  animation: ani-demo-spin 2s linear infinite;
}
.fm-spin-load-text {
  color: white;
}
.fm-footer-pos {
  position: absolute;
  bottom: 0px;
  width: 100%;
  text-align: center;
  background: #fff;
  height: 52px;
  border-top: 1px solid #f3f3f3;
}
.fm-footer-pos .ws-btn {
  margin: 6px 35px;
  padding: 0 25px;
  font-size: 17px;
}
.fm-table .del {
  color: red;
  cursor: pointer;
}
.fm-table .head {
  background-color: #f8f8f9;
}
.fm-list {
  width: 760px;
  margin: 0 auto 20px;
  background: #fff;
  padding: 40px;
  box-shadow: 0px 0px 10px 0px #ddd;
}

.wskj-tip-view {
  width: 98%;
  height: 34px;
  line-height: 34px;
  background: #fff9e6;
  border: 1px solid #ecddb9;
  margin: 0 10px 10px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #475066;
  display: flex;
  align-content: center;
}
.ios-alert{
  margin: auto 16px;
}
.orgColor{
  color:#FE8831;
  font-style: normal;
}
.blueColor{
  color:#2B5FD9;
  cursor: pointer;
}
</style>
<style lang="less">
 /deep/ .ivu-form-item-error-tip {
  padding-top: 0px !important;
}
</style>
