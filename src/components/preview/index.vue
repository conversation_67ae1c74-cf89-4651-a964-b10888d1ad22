<template>
  <div class="jgzsk-info">
    <div class="jgzsk-content">
        <div class="jgzsk-left">
            <div class="catalogue-header">
              <span>目录</span>
            </div>
            <Input v-model="keyword" placeholder="输入关键字过滤目录" class="catalogue-input"></Input>
            <div class="catalogue-content">
                <p
                v-for="(item, index) in filteredFileList"
                :key="index"
                @click="toPage(item)"
                :class="[{ active: curTab == getOriginalIndex(item) }]"
              >
                <span>{{ item.wsmc || item.name || item.fileName }}</span>
                <span>
                    <Icon type="md-download" @click.stop="downloadFile(item.cflj || item.url,item.wsmc || item.name || item.fileName)" class="f-20" style="margin-right: 5px;" />
                    <Icon type="ios-print-outline" @click.stop="printPage(item)" class="f-20" />
                </span>
              </p>
            </div>
        </div>
        <div class="jgzsk-right">
            <div class="jgzsk-right-content">
                <div class="pdf-container" v-if="pdfVisible">
                <!-- {{currentItem}} -->
                <vue-office-pdf 
                    :src="currentItem.cflj" 
                    style="width: 100%; height: 100%;"
                />
                <!-- <iframe
                    :src="'http://***********:9010/acp/询问笔录(李四).pdf'" 
                    id="iframePdf"
                    style="width: 100%;height: 100%;"></iframe> -->
                </div>
            </div>
        </div>
    </div>
  </div>
</template>

<script>
import VueOfficePdf from '@vue-office/pdf'
export default {
    components: {
        VueOfficePdf
    },
    props: {
        fileList:{
            type: Array,
            default: []
        }
    },
    watch: {
        fileList(newVal) {
        if (newVal.length > 0 && (this.curTab === null || this.curTab >= newVal.length)) {
            this.curTab = 0;
            this.toPage(newVal[0]);
        }
        }
    },
    data() {
        return {
            keyword: '',
            curTab: null,
            fileDownloading: false,
            pdfVisible: false
        }
    },
    created() {
        if (this.fileList.length > 0) {
            this.curTab = 0;
            this.toPage(this.fileList[0]);
        }
    },
    computed: {
        currentItem() {
            return this.fileList[this.curTab] || {};
        },
        filteredFileList() {
            if (!this.keyword) return this.fileList;
            return this.fileList.filter(item => {
            const fileName = item.wsmc || item.name || item.fileName || '';
            return fileName.toLowerCase().includes(this.keyword.toLowerCase());
            });
        }
    },
    mounted() {
        this.pdfVisible = true
    },
    methods: {
        getOriginalIndex(item) {
            return this.fileList.findIndex(file => file === item);
        },
        printPage(item) {
            // item.cflj = 'http://***********:9010/acp/询问笔录(李四).pdf'
            if (!item || (!item.cflj && !item.url)) {
                this.$Message.error('该文档没有可打印的文件地址');
                return;
            }

            const loadingMsg = this.$Message.loading({
                content: `正在准备打印 ${item.wsmc || '未命名文档'}...`,
                duration: 0
            });

            printJS({
                printable: item.cflj || item.url,
                type: 'pdf',
                header: `<h4>${item.wsmc || item.name || item.fileName || '未命名文档'}</h4>`,
                headerStyle: 'text-align: center; color: #333;',
                documentTitle: item.wsmc || item.name || item.fileName || '文档打印',
                onLoadingEnd: () => {
                    loadingMsg();
                    // this.$Message.success('PDF已加载，请使用浏览器打印功能');
                },
                onError: (err) => {
                    loadingMsg();
                    console.error('打印失败:', err);
                    // this.$Message.error(`打印失败: ${err.message || '未知错误'}`);
                }
            });
        },
        toPage(item){
            const index = this.getOriginalIndex(item);
            if (index !== -1) {
                this.curTab = index;
            }
        },
        // 文件下载方法（优化版）
        downloadFile(url, filename = '') {
            // console.log(url,filename);
            
            this.fileDownloading = true;
            this.$Message.info('开始下载文件...');
            
            // 处理文件名
            if (!filename) {
                const urlParts = url.split('/');
                filename = urlParts[urlParts.length - 1];
                
                // 如果没有文件扩展名，根据类型添加
                if (!filename.includes('.')) {
                if (this.currentItem.type.includes('word')) {
                    filename += '.docx';
                } else if (this.currentItem.type.includes('excel')) {
                    filename += '.xlsx';
                }
                }
            }
            
            // 创建XMLHttpRequest对象
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                const blob = xhr.response;
                const downloadUrl = window.URL.createObjectURL(blob);
                
                // 处理不同浏览器的下载方式
                if (window.navigator.msSaveOrOpenBlob) {
                    // IE专用方法
                    window.navigator.msSaveOrOpenBlob(blob, filename);
                } else {
                    // 标准浏览器方法
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
                
                // 释放内存
                window.URL.revokeObjectURL(downloadUrl);
                this.fileDownloading = false;
                this.$Message.success('文件下载成功');
                } else {
                this.fileDownloading = false;
                this.$Message.error('文件下载失败: 服务器响应异常');
                }
            };
            
            xhr.onerror = () => {
                this.fileDownloading = false;
                this.$Message.error('文件下载失败: 网络错误');
            };
            
            xhr.send();
        },
        toback(){
            this.$emit('toback')
        },
    }
}
</script>

<style lang="less" scoped>
.jgzsk-info{
    width: 100%;
    height: calc(~'100vh - 285px');
    min-height: 600px;
    .jgzsk-content{
        width: 100%;
        // height: calc(~'100% - 136px');
        height: 100%;
        display: flex;
        background: #eee;
        padding: 10px;
        .jgzsk-left{
            flex: none;
            width: 350px;
            height: 100%;
            background: #fff;
            padding: 10px;
            margin-right: 10px;
            .catalogue-header {
                font-weight: bold;
                font-size: 18px;
                color: #415060;
                line-height: 40px;
                margin-bottom: 10px;
                padding-left: 6px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .catalogue-input {
                margin-bottom: 10px;
            }
            .catalogue-content {
                height: ~"calc(100% - 128px)";
                overflow: auto !important;
                p {
                    width: 100%;
                    padding: 0 16px;
                    line-height: 40px;
                    border-radius: 4px;
                    color: #272c2c;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    &.active {
                        color:#2b5fda;// #00afb8;
                        background:#cfe2fa;// #e5f7f7;
                    }
                    &.no-click {
                        cursor: not-allowed;
                    }
                    &:not(.no-click):hover {
                        // background: #e5f7f7;
                        background:#cfe2fa;
                    }
                    span {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: inline-block; /* 或者 block，确保生效 */
                        max-width: 100%;
                        font-size: 14px;
                    }
                }
            }
            .catalogue-close {
                display: flex;
                justify-content: center;
            }
            .archive-content {
                flex: 1;
                position: relative;
                background: #f5f7fa;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 16px;
                border-radius: 4px;
                overflow: auto;
                .archive-paper-wrapper {
                    margin-bottom: 16px;
                    // padding: 40px;
                    background: #ffffff;
                    border-radius: 4px;
                    position: relative;
                }
            }
        }
        .jgzsk-right{
            flex: 1;
            height: 100%;
            background: #fff;
            padding: 10px;
            .jgzsk-right-content{
                margin-top: 5px;
                width: 100%;
                height: 100%;
                overflow: auto;
                .pdf-container{
                    width: 100%;
                    height: 100%;
                    background: gray;
                }
            }
        }
    }
}

/deep/.file-upload .ivu-upload{
    display: none !important;
}
.right-header{
    width: 100%; 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    border-bottom: 1px solid #dcdee2;
}
.f-20{
    cursor: pointer;
}
/deep/.vue-office-pdf-wrapper{
    height: 100% !important;
}
</style>