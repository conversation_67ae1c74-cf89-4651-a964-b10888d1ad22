<template>
  <div class="scroll-wrapper">
    <div
      class="scroll-container"
      :style="scrollContainerStyle()"
      @scroll="onScroll"
      @wheel="onWheel"
      ref="scrollContainer"
    >
      <div class="scroll-loader" :style="scrollTopLoaderStyle()" >
        <div class="loader-box top-loader" ref="topLoader">
          <i class="loader-roll" ></i>
          <div class="loader-text" v-if="loadingText">{{loadingText}}</div>
        </div>
      </div>
      <div :class="['scroll-content', {'scroll-content-loading': isRolling}]">
        <slot></slot>
      </div>
      <div class="scroll-loader" :style="scrollBottomLoaderStyle()" >
        <div class="loader-box bottom-loader" ref="bottomLoader">
          <i class="loader-roll"></i>
          <div class="loader-text" v-if="loadingText">{{loadingText}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
let throttle = function (func, wait, options = {}) {
  let timeout;
  let context;
  let args;
  let previous = 0;

  let later = function () {
    previous = options.leading === false ? 0 : new Date().getTime();
    timeout = null;
    func.apply(context, args);
    if (!timeout) {
      context = null;
      args = null;
    }
  };

  let throttled = function () {
    let now = new Date().getTime();
    if (!previous && options.leading === false) previous = now;
    let remaining = wait - (now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      func.apply(context, args);
      if (!timeout) {
        context = null;
        args = null;
      }
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
  };
  return throttled;
};
export default {
  name: "scroll",
  props: {
    height: {
      type: [Number, String],
      default: "100%",
    },
    onReachTop: {
      type: Function
    },
    onReachBottom: {
      type: Function
    },
    onReachEdge: {
      type: Function
    },
    loadingText: {
      type: String,
      default: "加载中"
    },
    isLoading: {
      type: Boolean,
    },
    listen: {
      type: Boolean,
      default: true,
    },
    distanceToEdge: [Number, Array],
  },
  data() {
    return {
      observer: null,
      isRolling: false,
      topObj: {
        loaderHeight: 0,
        distance: 0,
        rubberDistance: 0,
        reachLimit: false,
        reachRequest: "",
      },
      bottomObj: {
        loaderHeight: 0,
        distance: 0,
        rubberDistance: 0,
        reachLimit: false,
        reachRequest: "",
      },
      lastScroll: 0,
      onScroll: null,
      timer1: null,
      timer2: null,
      scrollDom: null,
    };
  },
  computed: {
    scrollPermission() {
      if (this.onReachEdge) return [true, true];
      return [!!this.onReachTop, !!this.onReachBottom];
    },

  },
  watch: {
    listen(listen) {
      if (listen) {
        this.addObserver();
      } else {
        this.removeObserver();
      }
    },
  },
  methods: {
    scrollContainerStyle() {
      return {
        height: typeof this.height === "string" ? this.height : `${this.height}px`,
      };
    },
    scrollTopLoaderStyle() {
      let { rubberDistance, loaderHeight } = this.topObj;
      let scale = rubberDistance / loaderHeight;
      return {
        height: `${rubberDistance}px`,
        opacity: `${scale > 1 ? 1 : scale}`,
      };
    },
    scrollBottomLoaderStyle() {
      let { rubberDistance, loaderHeight } = this.bottomObj;
      let scale = rubberDistance / loaderHeight;
      return {
        height: `${rubberDistance}px`,
        opacity: `${scale > 1 ? 1 : scale}`,
      };
    },
    waitOneSecond() {
      return new Promise(resolve => {
        setTimeout(resolve, 1000);
      });
    },
    startCountDown(timer, immediate, time) {
      this[timer] = setTimeout(() => {
        if (immediate || !this.isRolling) this.onReset();
      }, time);
    },
    stopCountDown(timer) {
      this[timer] && clearTimeout(this[timer]);
    },
    onReset() {
      this.topObj.rubberDistance =  0;
      this.topObj.reachLimit =  false;
      this.bottomObj.rubberDistance = 0;
      this.bottomObj.reachLimit = false;
      this.isRolling = false;
      this.lastScroll = 0;
      this.stopCountDown("timer1");
      this.stopCountDown("timer2");
    },
    onWheel(e) {
      if (this.isRolling || !this.isLoading) return;
      const direction = e.wheelDelta ? e.wheelDelta : -e.deltaY;
      if (direction > 0 && !this.scrollPermission[0]) return;
      if (direction < 0 && !this.scrollPermission[1]) return;
      if (this.scrollDom.clientHeight === this.scrollDom.scrollHeight) return;
      this.stretchEdge(direction);
    },
    stretchEdge(direction) {
      this.stopCountDown("timer1");
      // if the scroll is not strong enough, lets reset it
      this.startCountDown("timer1", false, 250);
      // to give the feeling its ruberish and can be puled more to start loading
      if (direction > 0 && this.topObj.reachLimit) {
        this.topObj.rubberDistance += 15 - this.topObj.rubberDistance / 6;
        if (this.topObj.rubberDistance > this.topObj.loaderHeight) this.onCallback(1);
      } else if (direction < 0 && this.bottomObj.reachLimit) {
        this.bottomObj.rubberDistance += 15 - this.bottomObj.rubberDistance  / 6;
        if (this.bottomObj.rubberDistance  > this.bottomObj.loaderHeight) this.onCallback(-1);
      } else {
        this.handleScroll();
      }
    },
    onCallback(dir) {
      this.stopCountDown("timer1");
      this.isRolling = true;
      if (dir > 0) {
        this.scrollDom.scrollTop = 0;
        this.topObj.rubberDistance = this.topObj.loaderHeight;
      } else {
        this.scrollDom.scrollTop =  this.scrollDom.scrollHeight -  this.scrollDom.clientHeight;
        this.bottomObj.rubberDistance = this.bottomObj.loaderHeight;
      }
      const callbacks = [this.waitOneSecond(), dir > 0 ? this.topObj.reachRequest() : this.bottomObj.reachRequest()];
      this.startCountDown("timer2", true, 5000);
      Promise.all(callbacks).then(() => {
        this.stopCountDown("timer2");
        this.onReset();
      });
    },
    handleScroll() {
      if (this.isRolling || !this.isLoading) return;
      const scrollDirection = this.lastScroll >= this.scrollDom.scrollTop;
      let bottomLimit = this.scrollDom.scrollHeight  -  this.scrollDom.clientHeight - this.scrollDom.scrollTop - this.bottomObj.distance;
      let topLimit = this.scrollDom.scrollTop - this.topObj.distance;
      if (scrollDirection && topLimit <= 0) {
        this.topObj.reachLimit = true;
      } else if (!scrollDirection &&  bottomLimit <= 0) {
        this.bottomObj.reachLimit = true;
      } else {
        this.topObj.reachLimit = false;
        this.bottomObj.reachLimit = false;
        this.lastScroll = this.scrollDom.scrollTop;
      }
    },
    onObserver() {
      let isOver = this.scrollDom.scrollHeight - this.scrollDom.clientHeight - this.topObj.rubberDistance - this.bottomObj.rubberDistance;
      if (isOver <= 0) {
        if (this.scrollPermission[1]) {
          this.bottomObj.reachRequest();
        } else {
          this.topObj.reachRequest();
        }
      } else {
        this.removeObserver();
      }
    },
    addObserver() {
      this.observer && this.observer.observe(this.scrollDom, { childList: true, subtree: true });
    },
    removeObserver() {
      this.observer && this.observer.disconnect();
      this.$emit("update:listen", false);
    },
    getDistance() {
      let distance = this.distanceToEdge || 20;
      let distanceArr =  Array.isArray(distance)  ? distance : [distance, distance];
      return distanceArr.map(item => (item < 20 ? 20 : item));
    },
    initObj() {
      const noop = () => Promise.resolve();
      const distanceArr = this.getDistance();
      this.topObj.reachRequest = this.onReachEdge || this.onReachTop || noop;
      this.topObj.distance = distanceArr[0];
      this.topObj.loaderHeight = this.$refs.topLoader.clientHeight || 54;
      this.bottomObj.reachRequest = this.onReachEdge || this.onReachBottom || noop();
      this.bottomObj.distance = distanceArr[1];
      this.bottomObj.loaderHeight = this.$refs.bottomLoader.clientHeight || 54;
    },
  },
  created() {
    this.onScroll = throttle(this.handleScroll, 200, {leading: false, trailing: true});
  },
  mounted() {
    this.initObj();
    this.scrollDom = this.$refs.scrollContainer;
    if (this.scrollPermission[0] ||  this.scrollPermission[1]) {
      this.observer = new MutationObserver(this.onObserver);
      this.listen && this.addObserver();
    }
  },
  destroyed() {
    this.removeObserver();
  }
};

</script>
<style scoped lang="less" >
  .scroll-wrapper{
    width: 100%;
    height: 100%;
    .scroll-container{
      overflow-y: auto;
      width: 100%;
      height: 100%;
    }
    .scroll-content-loading{
      opacity: .5;
    }
    .scroll-loader{
      overflow: hidden;
      position: relative;
      width: 100%;
      .loader-box {
        position: absolute;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 6px 0;
      }
      .top-loader {
        bottom: 0;
      }
      .bottom-loader {
        top: 0;
      }
      .loader-roll{
        width: 16px;
        height: 16px;
        min-height: 16px;
        min-width: 16px;
        margin-bottom: 4px;
        border: 2px solid #3465FF;
        border-top-color: transparent;
        border-radius: 50%;
        animation: rotateCircle 1s linear infinite;
        box-sizing: content-box;
      }
      .loader-text {
        color: #555555;
        font-size: 16px;
        line-height: 18px;
      }
    }
  }
  @keyframes rotateCircle {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>