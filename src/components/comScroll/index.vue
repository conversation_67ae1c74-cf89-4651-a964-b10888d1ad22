<template>
  <scroll
    ref="scroll"
    class="ui-scroll"
    :distance-to-edge="distanceToEdge"
    :loading-text="displayText"
    :is-loading="!!optionLists.length"
    :height="height"
    :on-reach-bottom="reachBottom"
    :listen.sync="listen"
  >
    <slot :rows="optionLists" />
  </scroll>
</template>
<script>
import scroll from "./scroll";
export default {
  name: "comScroll",
  components: { scroll },
  props: {
    api: {
      type: Function,
      require: false
    },
    param: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: [Number, String],
      default: "100%",
    },
    distanceToEdge: {
      type: Number,
      default: 20,
    },
    loadingText: {
      type: String,
    },
    beforeUpdate: {
      type: Function
    },
    maxSize: {
      type: Number,
      required: false,
    },
    isInitLoad: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      curPage: 0,
      optionLists: [],
      timestamp: "",
      paramObj: {
        curPage: 1,
        pageSize: 10,
      },
      status: "load",
      defaultText: "加载中",
      listen: false,
    };
  },
  computed: {
    displayText() {
      let statusText = {
        load: "加载中",
        end: "已经到底了",
        over: `滚动加载数据最大到${this.maxSize}条！`
      };
      return this.loadingText || statusText[this.status];
    }
  },
  methods: {
    reachBottom() {
      return new Promise((resolve) => {
        this.paramObj["curPage"] = this.curPage + 1;
        this.handleQuery(true).then(() => {
          resolve();
        });
      });
    },
    handleQuery(isScroll) {
      return new Promise((resolve) => {
        if (!isScroll) {
          this.status = "load";
          this.optionLists = [];
          this.paramObj["curPage"] = 1;
          this.curPage = 0;
        }
        if (this.status === "over" || this.status === "end") {
          return resolve();
        }
        this.timestamp = new Date().getTime();
        this.getOptionLists(this.timestamp).then((originData) => {
          if (!originData) return resolve();
          let lists = originData.rows || [];
          if (this.maxSize !== undefined) {
            let renderLen = this.optionLists.length;
            lists = renderLen >= this.maxSize ? [] : lists.slice(0, this.maxSize - renderLen);
          }
          if (this.beforeUpdate) {
            let rows = [...lists];
            let renderRows = [...this.optionLists];
            this.beforeUpdate(rows, renderRows).then(newLists => {
              newLists = newLists && Array.isArray(newLists) ? newLists :  lists;
              this.handleResult(newLists, originData);
              resolve();
            }).catch(() => {
              this.curPage = originData.curPage - 1;
              resolve();
            });
          } else {
            this.handleResult(lists, originData);
            resolve();
          }
        });
      });
    },
    handleResult(lists, originData) {
      if (lists) {
        let isOver = this.maxSize !== undefined && (this.optionLists.length + lists.length) >= this.maxSize;
        let isEnd = (this.optionLists.length + lists.length) === originData.total || lists.length === 0;
        this.status = isOver ? "over" : isEnd  ? "end" : "load";
        this.listen = originData.curPage === 1 && this.status === "load";
        this.$nextTick(() => {
          this.optionLists.push(...lists);
          const resData = {
            ...originData,
            rows: [...lists],
            renderRows: [...this.optionLists],
            curPage: originData.curPage,
          };
          this.$emit("on-update", resData);
        });
      }
    },
    getOptionLists(timestamp) {
      return new Promise((resolve) => {
        let paramObj = Object.assign(this.paramObj, this.param);
        this.api(paramObj).then((res) => {
          const { data = {}, returnCode } = res;
          if (returnCode === 0) {
            if (timestamp === this.timestamp && paramObj.curPage === this.curPage + 1) {
              this.curPage = paramObj.curPage;
              data.curPage = paramObj.curPage;
              resolve(data);
            } else {
              resolve(null);
            }
          } else {
            this.$emit("on-error");
            resolve(null);
          }
        }).catch(() => {
          this.$emit("on-error");
          resolve(null);
        });
      });
    },
    reRenderData() {
      this.$refs.scroll.$refs.scrollContainer.scrollTo(0, 0);
      this.handleQuery();
    }
  },
  created() {
    if (this.isInitLoad) {
      this.handleQuery();
    }
  },
};
</script>
