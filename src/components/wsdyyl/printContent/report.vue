<template>
  <!-- <print-content ref="printContent" style="height: 100%;" class="printContent">
    <div class="archive-paper" >
       <pdfCom   ref="fm" :formId="parameter.formId"  :businessId="parameter.businessId"
       :templateId="parameter.templateId" v-if="isPreview"  />
    </div>
  </print-content> -->
  <div class="archive-paper">
       <pdfCom   ref="fm" :formId="parameter.formId"  :businessId="parameter.businessId"
       :templateId="parameter.templateId" v-if="isPreview"  />
    </div>
</template>

<script>
// import printContent from "./printContent.vue";
// import reportBYBGWPDJBPNG from "../images/reportBYBGWPDJB.png";
import pdfCom from "./pdf-com.vue"
export default {
  name: "report",
  components: {
    // printContent,
    pdfCom
  },
  props: {
      isPreview: {
        type: <PERSON>olean,
        default: true
      },
      parameter:Object
    },
  data() {
    return {
      // reportBYBGWPDJBPNG,
    };
  },
  methods: {},
  mounted() {
    this.$emit("on-load");
  },
};
</script>
<style scoped>
.printContent>div{
  height: 100% !important;
  overflow: hidden !important;
}
</style>
<style lang="less" scoped>
.archive-paper {
  text-align: center;
  margin: auto;
  height: calc(~'100vh - 120px');
  overflow: hidden;
  width: 100%;
  .img {
    height: 950px;
  }
}
.min-height {
  height: 150px;
}
.tip {
  font-size: 13px;
  margin-top: 10px;
}

</style>
