<template>
  <div :class="getClasses()" @click="handleClickCard">
    <div class="card-head head-line">
      <div :class="['head-type', getSexClasses()]"></div>
      <div class="head-box">
        <div class="box1" :title="content.roomName || content.room_name">{{content.roomName  || content.room_name}}</div>
        <div class="box2">
          <i :class="['count', type]">{{content.count || 0}}</i>
          <span class="type" :title="content.roomType || content.room_typeName" v-if="content.roomType || content.room_typeName">{{content.roomType || content.room_typeName}}</span>
          <slot name="type"></slot>
        </div>
      </div>
    </div>
    <div class="card-content" v-if="size === 'large'">
      <slot name="content">
        <div class="content-box">
          <div class="content-item">
            <span class="label">主管管教：</span>
            <span class="value"  :title="content.policeName || content.w_police">{{ content.policeName || content.w_police || "-" }}</span>
          </div>
          <div class="content-item">
            <span class="label">协管管教：</span>
            <span class="value" :title="content.assistPoliceName || content.a_police">{{ content.assistPoliceName || content.a_police ||  "-"}}</span>
          </div>
          <slot name="extra">
            <div class="content-item" v-for="(item, idx) in extraList" :key="idx">
              <span class="label">{{item.label}}：</span>
              <span class="value" :title="item.value">{{ item.value || "-" }}</span>
            </div>
          </slot>
        </div>
      </slot>
    </div>
    <div class="card-abnormal" v-if="abnormalType">{{abnormalType}}</div>
     <ui-checkbox
       class="card-checkbox"
       v-if="showCheckbox && !disabled"
       :value="active"
       @change="handleClickCheckbox"
     ></ui-checkbox>
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: "comRoomCard",
  props: {
    // 卡片内容
    content: {
      type: Object,
      default: () => ({}),
    },
    // 选中
    active: {
      type: Boolean,
      default: false,
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 大小
    size: {
      type: String,
      default: "medium",
      validator: val => ["medium", "large"].includes(val),
    },
    // 显示勾选
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    // 补充列表
    extraList: {
      type: Array,
      default: () => [],
    },
    // 选择交互方式
    selectType: {
      type: String,
      default: "checkbox",
      validator: val => ["checkbox", "card"].includes(val),
    },
    // 监室类型
    type: {
      type: String,
      default: "normal",
      validator: val => ["normal", "ear"].includes(val),
    },
    // 异常类型
    abnormalType: {
      type: String,
      default: "",
    },
  },
  computed: {
    roomSex() {
      return `${this.content.roomSex || this.content.room_sex}`;
    }
  },
  methods: {
    getClasses() {
      return [
        "com-common-card",
        "com-room-card",
        `com-room-card-${this.size}`,
        {
          "card-active": this.active && this.showCheckbox && !this.disabled,
          "card-disabled": this.disabled
        },
      ];
    },
    getSexClasses() {
      return   this.roomSex === "1" ? "man"  : this.roomSex === "2" ? "woman" : "unknow";
    },
    handleClickCheckbox() {
      this.$emit("update:active", !this.active);
      this.$emit("on-select");
    },
    handleClickCard() {
      this.$emit('toPageDetail',this.content)
      if (this.selectType === "card" && !this.disabled) {
        this.$emit("update:active", !this.active);
        this.$emit("on-select");
      }
      this.$emit("on-change");
    },
  }
};
</script>

<style scoped lang="less">
  @import url("~@/assets/component/commonCard");
  .com-room-card{
    .card-head{
      padding-left: 62px;
      .head-type{
        width: 50px;
        height: 50px;
        background-size:  50px 50px;
        background-position: center center;
        top: 5px;
        left: 0;
        position: absolute;
        &.woman{
          background-image: url("./images/comRoomCard/woman.png") ;
        }
        &.man{
          background-image: url("./images/comRoomCard/man.png")  ;
        }
        &.unknow{
          background-image: url("./images/comRoomCard/unknow.png")  ;
        }
      }
      .head-box{
        width: 100%;
        > div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 24px;
        }
      }
      .box1{
        font-size: 18px;
        margin-top: 5px;
        margin-bottom:5px;
        color: #00244A;
        line-height: 24px;
      }
      .box2 {
        display: flex;
        align-items: center;
        height: 22px;
        line-height: 22px;
        font-size: 14px;
      }
      i{
        font-style: normal;
        position: relative;
        min-width:46px;
        color: #415060;
        padding-left: 20px;
        display: inline-block;
        &:before{
          position: absolute;
          content: "";
          display: block;
          background-image: url("./images/comRoomCard/icon_people.png");
          background-size: 16px 16px;
          width: 16px;
          height: 16px;
          left: 0px;
          top: 3px;
        }
        &.ear{
          &:before{
            background-image: url("./images/comRoomCard/icon_ear.png");
          }
        }
      }
      span {
        border-radius: 4px;
        max-width: 68px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 6px;
        background: #E9F4FF;
        color: #2390FF;
        + span {
          margin-left: 8px;
        }
      }
    }
    .card-content{
      width: 100%;
      .content-box{
        padding-top: 24px;
        width: 100%;
      }
      .content-item{
        width: 100%;
        padding-bottom: 12px;
        font-size: 16px;
        color: #8D99A5;
        line-height: 21px;
        display: flex;
        align-items: center;
        &:nth-last-child(1){
          padding-bottom: 0;
        }
        >span{
          white-space: nowrap;
          display: inline-block;
        }
        .label{
          min-width: 80px;
        }
        .value{
          width: calc(100% - 80px);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .card-abnormal{
      position: absolute;
      right: 0;
      top: 0;
      height: 24px;
      line-height: 24px;
      color: #FFFFFF;
      font-size: 14px;
      background: #FE564E;
      border-radius: 0 0 0 6px;
      padding: 0 6px;
    }
  }
  .com-room-card-medium{
    width: 204px;
    min-height: 82px;
    padding: 12px;
  }
  .com-room-card-large{
    width: 294px;
    min-height: 160px;
    padding: 11px 16px;
  }
</style>