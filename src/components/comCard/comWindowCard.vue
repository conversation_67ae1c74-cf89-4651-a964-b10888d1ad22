<template>
  <div :class="['com-window-card',getClasses()]" @click="handleClickCard">
    <div class="name">{{content.roomName}}</div>
    <div class="status">
      {{getStatus()}}
    </div>
  </div>
</template>
<script>
export default {
  name: "comWindowCard",
  props: {
    content: {
      type: Object,
      default: () => ({}),
    },
    // 选中
    active: {
      type: Boolean,
      default: false,
    },
    // 类型 0 超时 1 使用中 2 空闲 3 维修
    status: {
      type: Number,
      default: 2,
    },
  },

  methods: {
    handleClickCard() {
      if ([2, -1].includes(this.status)) {
        this.$emit("update:active", !this.active);
        this.$emit("on-select");
      }
      this.$emit("on-change");
    },
    getStatus() {
      if (this.active) return "";
      switch (this.status) {
        case 0:
          return `超时${this.content.overTime}分钟`;
        case 1:
          return "使用中";
        case 2:
          return `${this.active ? "" : "空闲中"}`;
        case 3:
          return "维护中";
        case -1:    // 编辑情况，不区分具体状态
          return "";
      }
    },
    getClasses() {
      switch (this.status) {
        case 0:
          return `timeout-card ${this.active ? "com-window-card-active" : ""}`;
        case 1:
          return `useing-card ${this.active ? "com-window-card-active" : ""}`;
        case 2:
          return `free-card ${this.active ? "com-window-card-active" : ""}`;
        case 3:
          return `maintaing-card ${this.active ? "com-window-card-active" : ""}`;
        case -1:
          return `free-card ${this.active ? "com-window-card-active" : ""}`;
      }
    },
  }
};
</script>

<style scoped lang="less">
.com-window-card{
  width: 160px;
  height: 100px;
  padding-left: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  .name{
    font-size: 20px;
    color: #00244A;
    line-height: 30px;
  }
  .status{
    font-size: 16px;
    color: #8D99A5;
    line-height: 30px;
  }
  &.timeout-card{
    background: linear-gradient(180deg, #FFF7F5 0%, #FFF3F2 100%);
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border-radius: 6px;
    border: 1px solid #FF251B;
    .name,.status{
      color: #FF251B;
    }
    &:not(.com-window-card-active):hover{
      background: linear-gradient(180deg, #FFF1EE 0%, #FED5D2 100%);
      box-shadow: 0px 2px 8px 0px rgba(213,23,7,0.16);
    }
  }
  &.free-card{
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border-radius: 6px;
    border: 1px solid #CADAEA;
     cursor: pointer;
    &:not(.com-window-card-active):hover{
      box-shadow: 0px 2px 8px 0px rgba(21,131,243,0.3);
      border: 1px solid #2390FF;
      .name,.status{
        color: #2390FF;
      }
    }
  }
  &.useing-card{
    background: linear-gradient(360deg, #F3F9FF 0%, #F4F9FF 100%);
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border-radius: 6px;
    border: 1px solid #2390FF;
    .name,.status{
      color: #2390FF;
    }
    &:not(.com-window-card-active):hover{
      background: linear-gradient(360deg, #BCDCFF 0%, #D8EBFF 100%);
      box-shadow: 0px 2px 8px 0px rgba(21,131,243,0.3);
    }
  }
  &.maintaing-card{
    background: linear-gradient(180deg, #F8F9FB 0%, #EFF1F5 100%);
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border-radius: 6px;
    border: 1px solid #8D99A5;
    .name,.status{
      color: #8D99A5;
    }
  }
  &.com-window-card-active{
    background: linear-gradient(360deg, #40A5FF 0%, #64C0FF 100%);
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border: none;
    .name{
      color: #FFFFFF;
    }
    .status{
      width: 24px;
      height: 30px;
      background: url("./images/comWindowCard/card_active.png") 100% center no-repeat;
    }
    &:hover{
      opacity: 0.8;
    }
  }
}
</style>