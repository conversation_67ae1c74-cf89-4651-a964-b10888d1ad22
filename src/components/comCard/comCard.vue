<template>
    <div class="com-card" @click="onSelect">
        <div class="com-card-content" @click="clickBoard">
            <div class="avator-photo">
                <img :src="img" alt="" />
            </div>
            <div class="info-box">
                <p class="name">{{name}}</p>
                <p v-for="(item, idx) in extraList" :key="idx"><span class="label">{{item.label}}：</span><span class="value">{{item.value||"-"}}</span></p>
            </div>
        </div>
        <div class="com-card-footer" v-if="edit||del">
            <div class="footer-btn" v-if="edit" @click="toEdit"><u class="edit"></u>编辑</div>
            <div class="footer-btn" v-if="del" @click="toDel"><u class="del"></u>删除</div>
        </div>
    </div>
</template>

<script>
export default {
  name: "comCard",
  props: {
    img: {
      type: String
    },
    name: {
      type: String
    },
    extraList: {
      type: Array,
      default: () => [],
    },
    edit: {
      type: Boolean,
      default: false
    },
    del: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    clickBoard() {
      this.$emit("on-click");
    },
    toEdit() {
      this.$emit("on-edit");
    },
    toDel() {
      this.$emit("on-del");
    },
    onSelect() {
      this.$emit("on-select");
    }
  }
};
</script>

<style lang="less" scoped>
.com-card {
    width: 412px;
    border-radius: 6px;
    box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
    border: 1px solid #E4EAF0;
}
.info-box {
    display:flex;
    justify-content: center;
    flex-direction: column;
    color: #8D99A5;
    font-size: 16px;
    p {
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5em;
        height: 30px;
        white-space: nowrap;
    }
}
.name {
    font-size: 18px;
    color: #00244A;
}
.avator-photo {
    width: 94px;
    height: 124px;
    position: absolute;
    left: 16px;
    top: 16px;
    background: #F5F7FA;
    border-radius: 4px;
    overflow: hidden;
}
.com-card-content {
    min-height: 156px;
    padding:16px;
    padding-left:126px;
    position: relative;
    background: #FFFFFF linear-gradient(360deg, #FFFFFF 0%, #F0F6FF 100%);
}
.com-card-footer {
    height:53px;
    border-top:1px solid #E4EAF0;
    display:flex;
    color: #415060;
    font-size:16px;
    >div {
        flex:1;
        display:flex;
        justify-content: center;
        align-items: center;
        position: relative;
        &+div {
            &::before {
                content: "";
                position: absolute;
                left:0;
                top:50%;
                width:1px;
                transform: translateY(-50%);
                height: 20px;
                background:#E4EAF0;
            }
        }
    }
    .footer-btn {
        cursor: pointer;
        u {
            display:inline-block;
            width:20px;
            height:20px;
            margin-right: 8px;
            &.edit {
                background:url(./images/common/icon_edit.png);
            }
            &.del {
                background:url(./images/common/icon_del.png);
            }
        }
    }
}
</style>