<template>
  <div class="tag">
    <Tooltip transfer :content="tag.content" v-for="(tag, index) in renderTag()" :key="index">
      <label>{{tag.alias}}</label>
    </Tooltip>
  </div>
</template>

<script>
export default {
  name: "comPrisonerTag",
  data() {
    return {
      tagList: []
    };
  },
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    count: {
      type: Number,
    }
  },
  methods: {
    getTagList() {
      let text  = [];
      let { urgentRiskType, riskLevel, sickType, riskType, importantType, deathTag, minorityTag, newInPrisonTag, outTreatmentTag} = this.info;
      if (urgentRiskType) {
        let content = `紧急风险人员${riskType && riskLevel ? `,${riskLevel}` : ""}`;
        text.push({alias: "紧", content});
      }
      if (riskType && !urgentRiskType) {
        text.push({alias: "险", content: riskLevel});
      }
      if (sickType) {
        text.push({alias: "病", content: "重病号人员"});
      }
      if (importantType) {
        text.push({alias: "注", content: "关注人员"});
      }
      if (deathTag) {
        text.push({alias: "死刑", content: "死刑人员"});
      }
      if (newInPrisonTag) {
        text.push({alias: "新", content: "新入所"});
      }
      if (minorityTag) {
        text.push({alias: "未", content: "未成年人"});
      }
      if (outTreatmentTag) {
        text.push({alias: "医", content: "出所就医"});
      }
      return text;
    },
    renderTag() {
      let text = this.getTagList();
      let num = this.count !== undefined ? this.count : text.length;
      if (text.length > num) {
        let overflowText = text.splice(num - 1, text.length);
        let content =  overflowText.map((item) =>  item.content);
        text.push({alias: "...", content: content.join("、")});
      }
      return text;
    }
  }
};
</script>

<style scoped lang="less">
  .tag{
    display: flex;
    align-items: center;
    margin-left: -6px;
    height: 22px;
    label{
      display: inline-block;
      height: 22px;
      background: #FFEEED;
      border-radius: 4px;
      margin-left: 6px;
      text-align: center;
      font-size: 14px;
      color: #FF251B;
      line-height: 22px;
      padding: 0 6px;
    }
  }
</style>