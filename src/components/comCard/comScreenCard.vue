<template>
  <div :class="getClasses()"  @click="handleClickCard">
    <div class="card-head">
      <div :class="['box-img', type === '1' ? 'inside' : 'outside']"></div>
      <div class="box-content">
        <div class="name" :title="deviceName">{{deviceName || "-"}}</div>
        <slot></slot>
      </div>
    </div>
    <ui-checkbox
      class="card-checkbox"
      v-if="showCheckbox && !disabled"
      :value="active"
      @change="handleClickCheckbox"
    ></ui-checkbox>
  </div>
</template>

<script>
export default {
  name: "comScreenCard",
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    // 默认1是仓内屏，2是仓外屏
    type: {
      type: String,
      default: "1"
    },
    // 设备名称
    deviceName: {
      type: String,
      default: ""
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    active: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getClasses() {
      return [
        "com-common-card",
        "com-screen-card",
        {
          "card-disabled": this.disabled
        },
      ];
    },
    handleClickCheckbox() {
      this.$emit("update:active", !this.active);
      this.$emit("on-select");
    },
    handleClickCard() {
      if (!this.disabled) {
        this.$emit("update:active", !this.active);
        this.$emit("on-select");
      }
      this.$emit("on-change");
    }
  }
};
</script>

<style lang="less" scoped>
  @import url("~@/assets/component/commonCard");
  .com-screen-card{
    width: 276px;
    height: 104px;
    padding: 13px;
    .card-head{
      display: flex;
      align-items: center;
      height: 100%;
      .box-img{
        margin-right: 12px;
        width: 80px;
        height: 57px;
        background-size: 80px  57px;
        &.inside {
          background-image: url("./images/comScreenCard/cnp.png")  ;
        }
        &.outside {
          background-image: url("./images/comScreenCard/cwp.png")   ;
        }
      }
      .name {
        font-size: 18px;
        color: #00244A;
        line-height: 24px;
      }
    }
  }
</style>

