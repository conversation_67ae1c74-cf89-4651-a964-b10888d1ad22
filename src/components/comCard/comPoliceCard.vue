<template>
  <div :class="getClasses()" @click="handleClickCard">
    <div class="card-head">
      <div class="head-img">
        <img class="photo" none-img-type="idCard" :src="policePhoto"></img>
      </div>
      <div class="head-box">
        <slot name="content">
          <div class="name" v-if="content.name" :title="content.name">{{content.name || "-"}}<i class="icon-sex" :class="getSex(content.sex)"/></div>
          <div class="code" v-if="content.policeCode" :title="content.policeCode">{{content.policeCode}}</div>
          <div class="title" v-if="content.policeRank2" :title="content.policeRank2">{{content.policeRank2}}</div>
          <slot name="extra"></slot>
        </slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "assistPoliceCard",
  props: {
    content: {
      type: Object,
      default: () => ({}),
    },
    // 选中
    active: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    policePhoto() {
      return  this.content && this.content.photo && this.content.photo[0] && this.content.photo[0].path || "";
    }
  },
  methods: {
    getClasses() {
      return [
        "com-common-card",
        "com-police-card",
        {
          "card-active": this.active,
        }
      ];
    },
    getSex(type) {
      let sexLists = {
        1: {
          className: "man"
        },
        2: {
          className: "woman"
        }
      };
      return (sexLists[type] && sexLists[type].className) || "";
    },
    handleClickCard() {
      this.$emit("on-select");
    },
  }
};
</script>

<style scoped lang="less">
  @import url("~@/assets/component/commonCard");
  .com-police-card{
    padding: 12px;
    width: 294px;
    min-height: 114px;
    .card-head{
      padding-left: 78px;
      .head-img{
        position: absolute;
        top: 0;
        left: 0;
        width: 66px;
        height: 90px;
        border-radius: 4px;
        background:#F5F7FA;
        overflow: hidden;
      }
      .head-box{
        width: 100%;
        > div {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 8px;
          &:nth-last-child(1){
            margin-bottom: 0;
          }
        }
      }
      .name{
        font-size: 18px;
        color: #00244A;
        line-height: 24px;
        height: 24px;
      }
      .code{
        font-size: 16px;
        color: #8D99A5;
        line-height: 21px;
        height: 21px;
      }
      .title {
        padding: 0 8px;
        height: 22px;
        background: fade(#2390ff, 15%);
        border-radius: 4px;
        line-height: 22px;
        font-size: 14px;
        color: #2390FF;
        display: inline-block;
      }
    }
    .icon-sex {
      width:18px;
      height:18px;
      display:inline-block;
      margin-left:6px;
      background-position: center center;
      background-size:100% 100%;
      background-image:url(./images/common/unknow.png);
      &.woman {
        background-image:url(./images/common/woman.png)
      }
      &.man {
        background-image:url(./images/common/man.png);
      }
    }
  }
</style>