<template>
  <div :class="[getClasses(), getRiskBg()]" @click="handleClickCard">
    <div class="card-head head-line">
      <div class="head-img">
        <img  :src="content.frontPhoto" none-img-type="idCard"></img>
      </div>
      <div class="head-box">
        <div class="name" :title="content.prisonerName">{{content.prisonerName || "-"}}<i class="sex" :class="getSex(content.sex)"></i></div>
        <div class="room" :title="content.roomName">{{content.roomName || "-"}}</div>
        <div class="extra" v-if="size === 'large'">
          <span class="extra-item" :title="content.suspectedCharges">{{content.suspectedCharges || "-"}}</span>
          <span class="extra-line"></span>
          <span class="extra-item" :title="content.litigationLink">{{content.litigationLink || "-"}}</span>
        </div>
        <com-prisoner-tag :info="content" :count="size === 'large' ? 5 : 3"></com-prisoner-tag>
      </div>
    </div>
    <div class="card-content">
      <slot name="content">
        <div class="content-box" v-if="extraList.length !== 0">
          <div class="content-item" v-for="(item, idx) in extraList" :key="idx">
            <div class="label">{{item.label}}：</div>
            <div class="value" :title="content[item.key] || item.value">{{ content[item.key] || item.value || "-" }}</div>
          </div>
        </div>
      </slot>
    </div>
    <ui-checkbox
      class="card-checkbox"
      v-if="showCheckbox && !disabled"
      :value="active"
      @change="handleClickCheckbox"
    ></ui-checkbox>
    <div class="card-closed" v-if="closed && !disabled" @click.stop="handleClose"></div>
    <com-prisoner-detail ref="comPrisonerDetail"></com-prisoner-detail>
  </div>
</template>
<script>
import comPrisonerDetail from "./comPrisonerDetail";
import comPrisonerTag from "./comPrisonerTag";
export default {
  name: "comRoomCard",
  components: {comPrisonerDetail, comPrisonerTag},
  props: {
    // 卡片内容
    content: {
      type: Object,
      default: () => ({}),
      required: true,
    },
    // 选中
    active: {
      type: Boolean,
      default: false,
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 关闭
    closed: {
      type: Boolean,
      default: false,
    },
    // 大小
    size: {
      type: String,
      default: "medium",
      validator: val => ["medium", "large"].includes(val),
    },
    // 显示勾选
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    // 补充列表
    extraList: {
      type: Array,
      default: () => [],
    },
    // 选择交互方式
    selectType: {
      type: String,
      default: "checkbox",
      validator: val => ["checkbox", "card"].includes(val),
    },
    // 显示详情
    showDetail: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    getRiskBg() {
      if (this.size === "large") {
        switch (this.content.riskLevelCode) {
          case "2":
            return "one-level-risk-bg";
          case "3":
            return "tow-level-risk-bg";
          case "4":
            return "three-level-risk-bg";
          default:
            return "";
        }
      }
    },
    getClasses() {
      return [
        "com-common-card",
        "com-prisoner-card",
        `com-prisoner-card-${this.size}`,
        {
          "card-active": this.active && this.showCheckbox && !this.disabled,
          "card-disabled": this.disabled
        },
      ];
    },
    handleClickCheckbox() {
      this.$emit("update:active", !this.active);
      this.$emit("on-select");
    },
    handleClickCard() {
      if (this.showDetail && this.content.prisonerId) {
        this.$refs.comPrisonerDetail.open(this.content.prisonerId);
      }
      if (this.selectType === "card" && !this.disabled) {
        this.$emit("update:active", !this.active);
        this.$emit("on-select");
      }
      this.$emit("on-change");
    },
    handleClose() {
      this.$emit("on-close");
    },
    getSex(type) {
      // TODO 这里需要纠正sex字段
      let sexLists = {
        1: {
          className: "man"
        },
        2: {
          className: "woman"
        }
      };
      return (sexLists[type] && sexLists[type].className) || "";
    }
  }
};
</script>

<style scoped lang="less">
  @import url("~@/assets/component/commonCard");
  .com-prisoner-card{
    padding: 12px;
    .card-head{
      display: flex;
      align-items: center;
      width: 100%;
      position: relative;
      padding-left: 102px;
      .head-img{
        position: absolute;
        left: 0;
        top: 0;
        width: 86px;
        height: 114px;
        border-radius: 4px;
        background:#F5F7FA;
        overflow: hidden;
      }
      .head-box{
        width: 100%;
        > div {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 8px;
          &:nth-last-child(1){
            margin-bottom: 0;
          }
        }
      }
      .sex {
        position: absolute;
        width:18px;
        height:18px;
        top: 3px;
        right: 0;
        background-position: center center;
        background-size:100% 100%;
        background-image:url(./images/common/unknow.png);
        &.woman {
          background-image:url(./images/common/woman.png)
        }
        &.man {
          background-image:url(./images/common/man.png);
        }
      }
      .name{
        font-size: 18px;
        color: #00244A;
        line-height: 24px;
        height: 24px;
        padding-right: 24px;
        position: relative;
        display: inline-block;
        max-width: 100%;
      }
      .room{
        font-size: 16px;
        color: #415060;
        line-height: 21px;
        height: 21px;
      }
      .extra{
        width: 100%;
        height: 21px;
        display: flex;
        align-items: center;
        .extra-item{
          display: inline-block;
          max-width: calc( (100% - 13px) / 2 );
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #8D99A5;
          font-size: 16px;
          line-height: 21px;
        }
        .extra-line{
          margin: 0 12px;
          display: inline-block;
          width: 1px;
          height: 14px;
          border: 1px solid #E4EAF0;
        }
      }
      .tag{
        display: flex;
        align-items: center;
        margin-left: -6px;
        height: 22px;
        label{
          display: inline-block;
          height: 22px;
          background: #FFEEED;
          border-radius: 4px;
          margin-left: 6px;
          text-align: center;
          font-size: 14px;
          color: #FF251B;
          line-height: 22px;
          padding: 0 6px;
        }
      }
    }
    .card-content{
      width: 100%;
      .content-box{
        padding-top: 24px;
        width: 100%;
      }
      .content-item{
        width: 100%;
        padding-bottom: 12px;
        font-size: 16px;
        color: #8D99A5;
        line-height: 21px;
        display: flex;
        align-items: center;
        &:nth-last-child(1){
          padding-bottom: 0;
        }
        > div{
          white-space: nowrap;
        }
        .value{
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .card-closed{
      cursor: pointer;
      position: absolute;
      width: 24px;
      height: 24px;
      background: url("./images/comPirsonerCard/icon_delete.png");
      top: 8px;
      right: 8px;
      z-index: 3;
      &:hover{
        background: url("./images/comPirsonerCard/icon_delete_hover.png");
      }
    }
  }
  .com-prisoner-card-medium{
    width: 204px;
    min-height: 108px;
    .card-head{
      padding-left: 76px;
      .head-img{
        width: 64px;
        height: 84px;
      }
    }
    .card-content{
      .content-item{
        display: block;
        .label{
          width: 100%;
        }
        .value{
          width: 100%;
        }
      }
    }
  }
  .com-prisoner-card-large{
    width: 294px;
    min-height: 138px;
    background-position: top right;
    background-repeat: no-repeat;
    &.one-level-risk-bg {
      background-image: url("./images/comPirsonerCard/bg_one_level_risk.png");
    }
    &.tow-level-risk-bg {
      background-image: url("./images/comPirsonerCard/bg_tow_level_risk.png");
    }
    &.three-level-risk-bg {
      background-image: url("./images/comPirsonerCard/bg_three_level_risk.png");
    }
  }

</style>