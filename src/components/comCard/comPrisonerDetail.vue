<template>
  <Modal  v-model="infoModal" title="人员基本信息" class="com-modal-cls" width="900px">
    <div class="com-modal-container">
      <div class="com-module-layout com-modal-height-nfooter">
        <div class="com-content-wrapper">
          <div class="prisoner-detail-head">
            <div class="head-img">
              <img :src="info.frontPhoto" none-img-type="idCard"></img>
            </div>
            <div class="head-box">
              <div class="name">{{ info.prisonerName }}</div>
              <div class="room">{{ info.roomName }}</div>
              <div class="extra">
                <span class="extra-item" :title="info.suspectedCharges">{{ info.suspectedCharges || "-" }}</span>
                <span class="extra-line"></span>
                <span class="extra-item" :title="info.litigationLink">{{ info.litigationLink || "-" }}</span>
              </div>
              <com-prisoner-tag :info="info"></com-prisoner-tag>
            </div>
          </div>
        </div>
        <com-hr />
        <p class="com-sub-title">人员基本信息</p>
        <div class="com-content-wrapper">
          <com-grid-table :model="info" type="fit" col="2" readonly>
            <com-grid-item label="证件类型">
              <span value="身份证"></span>
            </com-grid-item>
            <com-grid-item label="证件号码">
              <span :value="info.certificateNumber"></span>
            </com-grid-item>
            <com-grid-item label="性别">
              <span :value="info.sexDisplayName"></span>
            </com-grid-item>
            <com-grid-item label="核实情况">
              <span :value="info.checkIdentity"></span>
            </com-grid-item>
            <com-grid-item label="出生日期">
              <span :value="info.birth"></span>
            </com-grid-item>
            <com-grid-item label="国籍">
              <span :value="info.nationality"></span>
            </com-grid-item>
            <com-grid-item label="民族">
              <span :value="info.nation"></span>
            </com-grid-item>
            <com-grid-item label="文化程度">
              <span :value="info.degreeEducation"></span>
            </com-grid-item>
            <com-grid-item label="婚姻状况">
              <span :value="info.maritalStatus"></span>
            </com-grid-item>
            <com-grid-item label="职业">
              <span :value="info.occupation"></span>
            </com-grid-item>
            <com-grid-item label="籍贯">
              <span :value="info.domicile"></span>
            </com-grid-item>
            <com-grid-item label="政治面貌">
              <span :value="info.zzmm"></span>
            </com-grid-item>
            <com-grid-item label="办案单位类型">
              <span :value="info.badwlx"></span>
            </com-grid-item>
            <com-grid-item label="办案单位">
              <span :value="info.badw"></span>
            </com-grid-item>
            <com-grid-item label="办案人">
              <span :value="info.bar"></span>
            </com-grid-item>
            <com-grid-item label="办案人联系方式">
              <span :value="info.barlxff"></span>
            </com-grid-item>
            <com-grid-item label="关押期限">
              <span :value="info.gyqx"></span>
            </com-grid-item>
            <com-grid-item label="羁押日期">
              <span :value="info.jyrq"></span>
            </com-grid-item>
            <com-grid-item label="案件编号">
              <span :value="info.ajbh"></span>
            </com-grid-item>
            <com-grid-item label="同案编号">
              <span :value="info.tabh"></span>
            </com-grid-item>
            <com-grid-item label="备注" :col="3">
              <span :value="info.bz"></span>
            </com-grid-item>
          </com-grid-table>
        </div>
      </div>
    </div>
  </Modal >
</template>

<script>
// import { getPrisonerPageList } from "@/axios/zhjgBasicBusiness";
import comPrisonerTag from "./comPrisonerTag";
export default {
  name: "prisonerCardDetail",
  components: { comPrisonerTag },
  data() {
    return {
      info: {},
      infoModal: false,
    };
  },
  methods: {
    open(id) {
      if (!id) return;
      if (this.info && this.info.prisonerId === id) {
        this.infoModal = true;
      } else {
        // this.getInfo(id);
      }
    },
    getInfo(id) {
      getPrisonerPageList({ prisonerId: id })
        .then((res) => {
          let data = res.data.rows || [];
          this.info = data[0] ? data[0] : {};
          this.infoModal = true;
        })
        .catch((err) => {
          this.info = {};
          this.$messageError(err);
        });
    },
  },
};
</script>

<style scoped lang="less">
.prisoner-detail-head {
  display: flex;
  align-items: center;
  width: 100%;
  .head-img {
    width: 91px;
    height: 120px;
    border-radius: 4px;
    margin-right: 16px;
    background: #f5f7fa;
  }
  .head-box {
    flex: 1;
    width: calc(100% - 107px);
    > div {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }
  }
  .name {
    font-size: 24px;
    color: #00244a;
    line-height: 32px;
  }
  .room {
    font-size: 16px;
    color: #00244a;
    line-height: 21px;
  }
  .extra {
    width: 100%;
    display: flex;
    align-items: center;
    .extra-item {
      display: inline-block;
      max-width: calc((100% - 13px) / 2);
      min-width: 64px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #8d99a5;
      font-size: 16px;
      line-height: 21px;
    }
    .extra-line {
      margin: 0 12px;
      display: inline-block;
      width: 1px;
      height: 14px;
      border: 1px solid #e4eaf0;
    }
  }
}
</style>
