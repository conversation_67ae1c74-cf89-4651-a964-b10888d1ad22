import layoutMenu from '@/components/layoutMenu/index.vue'
import uacRouters from './uac-routers-new'
import conflictRouters from "./router-conflict"
import legbyRouters from './router-legby'
import protalSiteRouters from './router-protalSite'
import terminalRouters from './router-terminal'
import RouterSide from './routers-side'
import routerWindow from './router-window'
import routerTalkEducation from "./router-talkEducation";
import routerInquiry from './router-inquiry'
import routerInquiryJls from './router-inquiryJls'
import routerTerminalYw from './router-terminal-yw'

import systemRouters from './router-system'
// import routerManage from './router-manage'
import routerDiscipline from './router-discipline'
import routerDetentionEnter from './router-detentionEnter'
import routerDetainEnter from './router-detainEnter'
import routerOutForTreatment from './router-temporaryOuting'
import routerMainStation from './router-mainStation'
import routerComprehensive from './router-comprehensive'
import routerLeaderWork from './router-leaderWork'
import testRoutes from './test-routes'
/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [
  ...terminalRouters,
  ...uacRouters,
  ...conflictRouters,
  ...legbyRouters,
  ...protalSiteRouters,
  ...RouterSide,
  ...systemRouters,
  ...routerWindow,
  ...routerTalkEducation,
  ...routerInquiry,
  // 组件测试路由
  ...testRoutes,
  // ...routerManage,
  ...routerDiscipline,
  ...routerInquiryJls,
  ...routerTerminalYw,
  // 人员选择组件测试路由
  {
    path: '/personnel-selector-test',
    name: 'personnel-selector-test',
    component: () => import('@/components/personnel-selector/test.vue'),
    meta: {
      title: '人员选择组件测试',
      hideInMenu: false
    }
  },
  ...routerDetentionEnter,
  ...routerDetainEnter,
  ...routerOutForTreatment,
  ...routerMainStation,
  ...routerComprehensive,
  ...routerLeaderWork,
  {
    path: '/',
    name: 'homePage',
    redirect: '/homePage',
    component: layoutMenu, // menuMode == 'side' ? mainNew : main,
    meta: {
      hideInMenu: true,
      notCache: true,
      menu: true,
      bread: true
    },
    children: [{
      path: '/protal',
      name: 'protal',
      meta: {
        hideInMenu: false,
        title: '门户页面',
        notCache: false,
        icon: 'md-home'
      },
      component: () => import('@/view/protal-components/index.vue')
    }]
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '系统登录',
      hideInMenu: true
    },
    component: () => import('@/view/login/login.vue')
  },
  {
    path: '/lawyerMeeting',
    name: 'lawyerMeeting',
    meta: {
      title: '律师会见排号信息',
      hideInMenu: true
    },
    component: () => import('@/view/windowBusiness/meetingNumber/lawyer.vue')
  },
  {
    path: '/familyMeetingScreen',
    name: 'familyMeetingScreen',
    meta: {
      title: '家属会见排号信息',
      hideInMenu: true
    },
    component: () => import('@/view/windowBusiness/meetingNumber/family.vue')
  },
  {
    path: '/personnel',
    name: 'personnel',
    meta: {
      title: '被监管人员选择',
      hideInMenu: true
    },
    component: () => import('@/components/personnel-selection/index.vue')
  },
  {
    path: '/newlogin',
    name: 'login',
    meta: {
      title: '系统登录',
      hideInMenu: true
    },
    component: () => import('@/view/login/login.vue')
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/401.vue')
  },
  // {
  //   meta: {
  //     title: '首页',
  //     hideInMenu: true
  //   },
  //   path: '/portalSite',
  //   name: 'portalSite',
  //   sider: false,
  //   bread: true,
  //   component: () => import('@/components/portalSite/index.vue')
  // },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/500.vue')
  },
  {
    path: '*',
    name: 'error_404',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/404.vue')
  },
  {
    path: '/postScreen',
    name: 'postScreen',
    meta: {
      title: '提押岗大屏',
      hideInMenu: true

    },
    component: () => import('@/view/windowBusiness/leaderScreen/post/postScreen.vue')
  },
  {
    path: '/leaderScreen',
    name: 'leaderScreen',
    meta: {
      title: '大屏',
      hideInMenu: true
    },
    redirect: "/leaderScreen/prisonOverview",
    component: () => import('@/view/windowBusiness/leaderScreen/index.vue'),
    children: [{
        path: "prisonOverview",
        name: "prisonOverview",
        meta: {
          title: "监所概况",
          hideInMenu: true,
        },
        component: () => import("@/view/windowBusiness/leaderScreen/prisonOverview/index.vue"),
      },
      {
        path: "detentionDynamics",
        name: "detentionDynamics",
        meta: {
          title: "羁押动态",
          hideInMenu: true,
        },
        component: () => import("@/view/windowBusiness/leaderScreen/detentionDynamics/index.vue"),
      },
      {
        path: "securitySituation",
        name: "securitySituation",
        meta: {
          title: "安全态势",
          hideInMenu: true,
        },
        component: () => import("@/view/windowBusiness/leaderScreen/securitySituation/index.vue"),
      },
    ]
  }

]
