import bspUac from './bsp-uac'
import bspTest from './bsp-test'
import bspApp from './bsp-app'
import bspConflict from './bsp-conflict'
import bspSystem from './bsp-system'
import acpApplication from './acp-application'
import bspWindowBusiness from './bsp-window-business'
import bspRoomManage from './bsp-roomManage'
import bspDetentionEnter from './bsp-detentionEnter'
import bspDetainEnter from './bsp-detainEnter'
import bspOutForTreatment from "./bsp-outForTreatment"
import bspRehabilitation from "./bsp-rehabilitation"
import bspLeaderWork from './bsp-leaderWork'
import { bspUacRoot, bspComRoot } from './base'
import catering from './catering'
import securityCheck from "./securitycheck"
import inOutRecords from "./inOutRecords"
import familyContact from "./familyContact"
import fjcy from "./fjcy"
import psy from "./psy"
import oneDaylifeSystem from './oneDaylifeSystem'
import apiTerminal from './api-terminal'
import acpUndercove from './acp-undercove'
import bspInfoManage from './bsp-infoManage'
import faceToface from './faceToface'
import prohibitedManage from './prohibitedManage'
import detentionUniform from './detentionUniform'
import pamrollcall from './pam-rollcall'
import acpPunishment from './acp-punishment'
import equipmentRepair from './equipmentRepair'
import dockConfiguration from "./acp-dockConfiguration"
import libraryBorrowing from './libraryBorrowing';
import disciplineMedical from './disciplineMedical'
import acpComprehensive from './acp-comprehensive.js'
import violationAppeals from './violationAppeals.js'
import emergencyDeparture from './acp-emergency-departure'
import civilization from './civilization'
import educationalRehabilitation from './educationalRehabilitation'
import acpTerminal from './acp-terminal'
import acpPerformanceAppraisal from "./acp-performanceAppraisal"
import acpRiskConfiguration from './acp-riskConfiguration'
// IHC 模块导入
import { ihcRequestUrl } from './ihc/path.js'
import { temRequestUrl } from './tem/path.js'
import {ptmRequestUrl} from "./ptm/path.js";
import {damRequestUrl} from './dam/path.js'
const request_url = {
  ...bspUac,
  ...bspTest,
  ...bspApp,
  ...bspConflict,
  ...bspSystem,
  ...acpApplication,
  ...bspWindowBusiness,
  ...bspRoomManage,
  ...catering,
  ...securityCheck,
  ...inOutRecords,
  ...familyContact,
  ...fjcy,
  ...libraryBorrowing,
  ...psy,
  ...bspDetentionEnter,
  ...bspDetainEnter,
  ...bspOutForTreatment,
  ...bspRehabilitation,
  ...bspLeaderWork,
  ...oneDaylifeSystem,
  ...acpUndercove,
  ...bspInfoManage,
  ...faceToface,
  ...prohibitedManage,
  ...detentionUniform,
  ...acpPunishment,
  ...equipmentRepair,
  ...dockConfiguration,
  ...disciplineMedical,
  ...violationAppeals,
  ...civilization,
  ...educationalRehabilitation,
  ...acpRiskConfiguration,
  // IHC 模块路径配置
  ...ihcRequestUrl,
  // TEM 模块路径配置
  ...temRequestUrl,
  // PTM 模块路径配置
  ...ptmRequestUrl,
  // 基础配置（同名配置）
  // dam 模块路径配置
  ...damRequestUrl,
  // 基础配置（主项目优先，覆盖 IHC 中的同名配置）
  bspUacRoot,
  bspComRoot,
  apiTerminal,
  ...pamrollcall,
  ...acpComprehensive,
  ...emergencyDeparture,
  ...acpTerminal,
  ...acpPerformanceAppraisal
}
export default Vue => {
  Vue.path = Vue.prototype.$path = request_url
}
