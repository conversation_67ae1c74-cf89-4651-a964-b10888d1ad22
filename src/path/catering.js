import { pamCom } from './base'
// edas
export default {


  // ----------------------菜品分类 start -------------------------
  // 菜品分类列表
  catering_list: pamCom + '/pam/cook/cate/list',
  // 创建-菜品分类
  catering_create: pamCom + '/pam/cook/cate/create',
  // 删除-菜品分类
  catering_delete: pamCom + '/pam/cook/cate/delete',
  // 获得-菜品分类
  catering_get: pamCom + '/pam/cook/cate/get',
  // 更新-菜品分类
  catering_update: pamCom + '/pam/cook/cate/update',
  // 创建-菜品
  catering_create_dashes: pamCom + '/pam/cook/manage/create',
  // 获得-菜品
  catering_get_dashes: pamCom + '/pam/cook/manage/get',
  // 更新-菜品
  catering_update_dashes: pamCom + '/pam/cook/manage/update',
  // 删除-菜品
  catering_delete_dashes: pamCom + '/pam/cook/manage/delete',
  // 获得-菜品列表
  catering_dashes_list: pamCom + '/pam/cook/manage/list',
  // 创建-菜品模板
  catering_recipe_tem_create: pamCom +'/pam/cook/template/create',
  // 获得-菜品模板
  catering_recipe_tem_get: pamCom + '/pam/cook/template/get',
  // 更新-菜品模板
  catering_recipe_tem_update: pamCom + '/pam/cook/template/update',
  // 删除-菜品模板
  catering_recipe_tem_delete: pamCom + '/pam/cook/template/delete',
  // 监室配餐-一周食谱首页列表
  catering_recipe_tem_getIndexList: pamCom + '/pam/cook/template/getIndexList',
  // 获得-菜品模板列表
  catering_recipe_tem_list: pamCom + '/pam/cook/template/list',
  // 监室配餐-菜谱复用
  catering_recipe_tem_multiplexing: pamCom + '/pam/cook/template/multiplexing',
  // 监室配餐-特殊餐申请
  specialApply_create: pamCom + '/pam/cook/specialApply/create',
  // 监室配餐-特殊餐批量申请
  specialApply_createBatch: pamCom + '/pam/cook/specialApply/createBatchSpecialApply',
  // 监所事务管理-特殊餐申请人员查询
  specialApply_people_search: pamCom + '/pam/cook/specialApply/getSpecialApplyPrisonerByJgrybm',
  // 监室配餐-获取特殊餐申请详情
  specialApply_people_detail: pamCom + '/pam/cook/specialApply/get',
  // 监室配餐-医生审批
  specialApply_doctorApprove: pamCom + '/pam/cook/specialApply/doctorApprove',
  // 监室配餐-所领导审批
  specialApply_leaderApprove: pamCom + '/pam/cook/specialApply/leaderApprove',
  // 监室配餐-管教民警确认申请
  specialApply_confirm: pamCom + '/pam/cook/specialApply/confirmSpecialApply',

  cook_delivery_get: pamCom + '/pam/cook/delivery/get',
  // ----------------------菜品分类 start -------------------------


  // 数字伙房--伙房人员管理--新增
    management_kitchen_personnel_create: pamCom + '/pam/cook/persionInfo/create',
  // 数字伙房--伙房人员管理--编辑和详情   信息回显
    management_kitchen_personnel_detail: pamCom + '/pam/cook/persionInfo/get',
  // 数字伙房--伙房人员管理--编辑
  management_kitchen_personnel_edit: pamCom + '/pam/cook/persionInfo/update',
  // 数字伙房--伙房人员管理--删除
    management_kitchen_personnel_delete: pamCom + '/pam/cook/persionInfo/delete',


}
