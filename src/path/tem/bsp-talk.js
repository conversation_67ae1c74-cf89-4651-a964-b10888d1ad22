import { bspTalkRoot, acpCom } from "./base";
export default {
  // 测试
  bsp_test_page_url: bspTalkRoot + "/test",
  // 待办消息
  app_getDbMsgData: acpCom + "/acp/msg/getDbMsgData",
  systemTheme_get: acpCom + "/acp/pm/systemTheme/get",
  // 更新实战平台-主题-主题配置
  systemTheme_update: acpCom + "/acp/pm/systemTheme/update",

  // 谈话模板新增
  bsp_test_tem_url_add: bspTalkRoot + "/tem/template/talkTemplate/create",

  // 谈话模板删除
  bsp_test_tem_url_delete: bspTalkRoot + "/tem/template/talkTemplate/delete",

  // 谈话模板查看
  bsp_test_tem_url_get: bspTalkRoot + "/tem/template/talkTemplate/get",

  // 谈话模板编辑
  bsp_test_tem_url_update: bspTalkRoot + "/tem/template/talkTemplate/update",

  // 谈话提问助手新增
  bsp_test_tem_assistant_add: bspTalkRoot + "/tem/talk/talkHelper/create",

  // 谈话提问助手删除
  bsp_test_tem_assistant_delete: bspTalkRoot + "/tem/talk/talkHelper/delete",

  // 谈话提问助手查看
  bsp_test_tem_assistant_get: bspTalkRoot + "/tem/talk/talkHelper/get",

  // 谈话提问助手编辑
  bsp_test_tem_assistant_update: bspTalkRoot + "/tem/talk/talkHelper/update",

  // 谈话人员标签库新增
  bsp_test_tem_ryTag_add: bspTalkRoot + "/tem/tag/talkTag/create",

  // 谈话人员标签库删除
  bsp_test_tem_ryTag_delete: bspTalkRoot + "/tem/tag/talkTag/delete",

  // 谈话人员标签库查看
  bsp_test_tem_ryTag_get: bspTalkRoot + "/tem/tag/talkTag/get",

  // 谈话人员标签库编辑
  bsp_test_tem_ryTag_update: bspTalkRoot + "/tem/tag/talkTag/update",

  // 集体教育新增
  bsp_test_tem_jtjy_add: bspTalkRoot + "/tem/talk/collectiveTalkRecord/create",

  // 集体教育删除
  bsp_test_tem_jtjy_delete:
    bspTalkRoot + "/tem/talk/collectiveTalkRecord/delete",

  // 集体教育查看
  bsp_test_tem_jtjy_get: bspTalkRoot + "/tem/talk/collectiveTalkRecord/get",

  // 集体教育编辑
  bsp_test_tem_jtjy_update:
    bspTalkRoot + "/tem/talk/collectiveTalkRecord/update",

  //集体教育-获取监室
  bsp_test_tem_jtjy_getJail:
    acpCom + "/base/area/getAreaListByOrgCodeAndAreaType",

  // 个人谈话-谈话原因
  bsp_test_tem_thyyList: bspTalkRoot + "/ops/dic/code/getDicCodeTreeData",

  // 个人谈话-创建
  bsp_test_tem_grTalk_submit: bspTalkRoot + "/tem/talk/talkRecord/create",

  // 个人谈话-创建
  bsp_test_tem_grTalk_add: bspTalkRoot + "/tem/talk/talkTask/create",

  // 个人谈话-删除
  bsp_test_tem_grTalk_delete: bspTalkRoot + "/tem/talk/talkRecord/delete",

  // 个人谈话-查看
  bsp_test_tem_grTalk_view: bspTalkRoot + "/tem/talk/talkRecord/get",

  // 个人谈话-查看
  bsp_test_tem_grTalk_get: bspTalkRoot + "/tem/talk/talkTask/get",

  // 个人谈话-编辑
  bsp_test_tem_grTalk_update: bspTalkRoot + "/tem/talk/talkRecord/update",

  // 个人谈话-获取当前时间
  bsp_talk_tem_grTalk_time: bspTalkRoot + "/tem/talk/talkTask/getCurrentTime",

  // 个人谈话-获取谈话室
  bsp_talk_getThs: bspTalkRoot + "/tem/talk/talkRecord/getThs",

  // 基础设置-获取谈话室
  bsp_device_getThs: bspTalkRoot + "/base/area/getTalkingRoomList",

  // 个人谈话-获取人员标签数据
  bsp_talk_ryTagList: bspTalkRoot + "/tem/tag/talkTag/list",

  // 个人谈话-获取模板列表
  bsp_talk_talkTemplateList: bspTalkRoot + "/tem/template/talkTemplate/list",

  // 个人谈话-获取sip配置数据
  bsp_talk_sipconfig:
    bspTalkRoot + "/acp/pm/terminalVirtualAccount/getAvailableAccount",

  // 个人谈话-开始谈话
  bsp_talk_startRecord: bspTalkRoot + "/api/recorder/start",

  // 个人谈话-停止谈话
  bsp_talk_stopRecord: bspTalkRoot + "/api/recorder/stop/",

  // 个人谈话-发送心跳
  bsp_talk_sendHeartBeat: bspTalkRoot + "/api/recorder/heartbeat/",

  // 个人谈话-获取模板列表
  bsp_talk_listThyyTree: bspTalkRoot + "/tem/template/talkTemplate/listThyyTree",
};
