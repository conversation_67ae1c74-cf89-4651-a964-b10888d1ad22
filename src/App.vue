<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>
import { getUserCache } from "@/libs/util"
export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouterAlive: true,
      websocket: null,
    }
  },
  mounted() {
    this.initWebsocket()
  },
  methods: {
    reload() {
      let that = this
      that.isRouterAlive = false
      that.$nextTick(function () {
        that.isRouterAlive = true
      })
    },
    speak(content) {
      this.$speak(content, { rate: 0.8, lang: 'zh-CN' })
    },
    //关闭WebSocket连接
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close();
      }
    },
    initWebsocket() {
      let _this = this
      const sid = getUserCache.getIdCard()  //'14102720100225960X';
      const wsUrl = serverConfig.websocketPath + `websocket/${sid}`;
      // 初始化 WebSocket
      if (_this.websocket) return; // 防止重复连接
      if ('WebSocket' in window) {
        _this.websocket = new WebSocket(wsUrl);
      } else {
        alert('当前浏览器不支持 WebSocket');
      }
      // 连接成功
      this.websocket.onopen = function () {
        console.log('WebSocket 连接成功');
      };
      // 接收消息
      this.websocket.onmessage = function (event) {
        console.log(event, event.data, 'eventeventevent')
        let msgData = event.data && event.data!='WebSocket 连接成功' ? JSON.parse(event.data) : {}
        if (msgData.content) {
          _this.speak(msgData.content)
          _this.$Notice.info({
            title: msgData.title,
            desc: msgData.content,
            duration: 10
          });
        }

      };

      // 错误处理
      this.websocket.onerror = function () {
        console.log('WebSocket 连接发生错误');
      };

      // 关闭连接
      this.websocket.onclose = function () {
        _this.closeWebSocket();
        console.log('WebSocket 连接已关闭');
      };

      // 页面关闭前断开连接
      window.onbeforeunload = function () {
        _this.closeWebSocket();
      };
    }
  }
}
</script>

<style lang="less">
.size {
  width: 100%;
  height: 100%;
}

html,
body {
  .size;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#app {
  .size;
  font-family: Source Han Sans CN, Source Han Sans CN;
}

.ivu-modal /deep/ .ivu-modal-header {
  padding: 0 !important;
}
</style>
