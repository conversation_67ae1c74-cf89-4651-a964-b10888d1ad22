 let websocket = '';
import {getUserCache} from "@/libs/util"
 // 获取当前页面 URL 中的 sid 参数或随机生成一个
 function getSid() {
   const urlParams = new URLSearchParams(window.location.search);
   return urlParams.get('sid') || Math.floor(1000 + Math.random() * 9000); // 4位数字
 }

 const sid = getUserCache.getIdCard()  //'14102720100225960X';
 const wsUrl = `ws://127.0.0.1:9112/websocket/${sid}`;

 // 页面加载完成后更新 sid 显示
 window.onload = function () {
   document.getElementById('sid-value').textContent = sid;
 };

 // 初始化 WebSocket
 if ('WebSocket' in window) {
   websocket = new WebSocket(wsUrl);
 } else {
   alert('当前浏览器不支持 WebSocket');
 }

 // 连接成功
 websocket.onopen = function () {
   console.log('WebSocket 连接成功');
 };

 // 接收消息
 websocket.onmessage = function (event) {
   addMessage(event.data);
 };

 // 错误处理
 websocket.onerror = function () {
   console.log('WebSocket 连接发生错误');
 };

 // 关闭连接
 websocket.onclose = function () {
   this.closeWebSocket();
   console.log('WebSocket 连接已关闭');
 };

 // 页面关闭前断开连接
 window.onbeforeunload = function () {
   this.closeWebSocket();
 };

 // 发送消息
 function send() {
   let message = document.getElementById('text').value.trim();
   if (!message) return;

   if (websocket && websocket.readyState === WebSocket.OPEN) {
     websocket.send(`{"msg":"${message}","sid":"${sid}", "time": "${new Date().toLocaleTimeString()}"}`);
     addMessage(`{"msg":"${message}","sid":"${sid}", "time": "${new Date().toLocaleTimeString()}"}`);
     document.getElementById('text').value = '';
   } else {
     addMessage("WebSocket 连接未建立，请稍后再试。");
   }
 }

 //关闭WebSocket连接
 function closeWebSocket() {
   if (websocket) {
     websocket.close();
   }
 }

 // 添加消息到聊天区
 function addMessage(content) {
   console.log(content);
   let msgBox = document.getElementById('message-box');
   const time = new Date().toLocaleTimeString();
   const div = document.createElement('div');

   let messageData;
   let isSystemMessage = false;

   try {
     messageData = JSON.parse(content);
   } catch (e) {
     isSystemMessage = true;
   }

   if (isSystemMessage) {
     div.className = 'message-center';
     div.innerHTML = `
            <span class="time">${time}</span>
            <span class="system-msg"> ${content}</span>
            `;
   } else {
     const isMyMessage = String(messageData.sid) === String(sid);
     div.className = isMyMessage ? 'message-right' : 'message-left';
     div.innerHTML = `
            <span class="time">${messageData.time}</span>
            <span class="${isMyMessage ? 'user-msg' : 'system-msg'}">
                ${isMyMessage ? '' : '用户#' + messageData.sid + ':'} ${messageData.msg}
            </span>
            `;
   }

   msgBox.appendChild(div);
   msgBox.scrollTop = msgBox.scrollHeight;
 }
