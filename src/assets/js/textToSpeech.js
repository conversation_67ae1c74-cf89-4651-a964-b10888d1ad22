// plugins/textToSpeech.js
const TextToSpeechPlugin = {
  install(Vue) {
    const speechSynthesis = window.speechSynthesis
    
    Vue.prototype.$speak = (text, options = {}) => {
        console.log(text, options,'optionsoptionsoptions21121212')
      return new Promise((resolve) => {
        if (speechSynthesis.speaking) {
          speechSynthesis.cancel()
        }
        
        const utterance = new SpeechSynthesisUtterance(text)
        
        // 设置选项
        utterance.rate = options.rate || 1
        utterance.pitch = options.pitch || 1
        utterance.voice = options.voice || null
        utterance.lang = options.lang || 'zh-CN'
        
        utterance.onend = resolve
        utterance.onerror = resolve
        
        speechSynthesis.speak(utterance)
      })
    }
    
    Vue.prototype.$stopSpeaking = () => {
      speechSynthesis.cancel()
    }
    
    Vue.prototype.$pauseSpeaking = () => {
      speechSynthesis.pause()
    }
    
    Vue.prototype.$resumeSpeaking = () => {
      speechSynthesis.resume()
    }
    
    Vue.prototype.$getVoices = () => {
      return speechSynthesis.getVoices()
    }
  }
}

export default TextToSpeechPlugin