<template>
  <div class="safety-checks-container">
    <div class="" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="xlcp-cpjhlb" :customFunc="true" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':xlcp-cpjhlb:add')"
                  @click.native="handleAdd('add')">新增
          </Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <!--  && row.enable_status == 2 -->
          <Button type="primary"
                  v-if="func.includes(globalAppCode + ':xlcp-cpjhlb:edit') && row.status == '01'"
                  @click.native="handleEdit(index, row)">编辑
          </Button>
          <Button v-if="func.includes(globalAppCode + ':xlcp-cpjhlb:detail')"
                  @click.native="handleDetail(index, row)" style="margin-left: 10px;">详情
          </Button>
        </template>
        <template slot="slot_enable_status" slot-scope="{ func, row, index }">
          <i-switch v-model="row.enable_status" size="large" :true-value="'1'" :false-value="'0'"
                    @on-change="changeSwitch(row)">
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </template>
      </s-DataGrid>
    </div>

    <div class="add-template" v-if="AddFormContainer">
      <div class="modal-scroll-content">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda"/>
          基础信息配置
        </p>
        <!-- 表单内容 -->
        <Form ref="formValidate" :model="formValidate" :label-width="120" style="margin-top: 10px;">
          <Row>
            <Col span="23">
              <FormItem label="计划名称" prop="planName"
                        :rules="{ required: true, message: '计划名称不能为空', trigger: 'blur' }">
                <Input v-model="formValidate.planName" placeholder="请输入计划名称" type="textarea"
                       :autosize="{ minRows: 2, maxRows: 5 }" maxlength="50"/>
              </FormItem>
            </Col>
            <Col span="23">
              <FormItem label="计划说明" prop="remark"
                        :rules="{ required: false, message: '计划说明不能为空', trigger: 'blur' }">
                <Input v-model="formValidate.remark" placeholder="请输入计划说明" type="textarea"
                       :autosize="{ minRows: 2, maxRows: 5 }" maxlength="50"/>
              </FormItem>
            </Col>
            <Col span="23">
              <FormItem prop="enableStatus"
                        :rules="{ required: true, type: 'number', message: '生效状态不能为空', trigger: 'change' }"
                        :label="'生效状态'">
                <i-Switch :true-value="1" :false-value="0" v-model="formValidate.enableStatus"/>
              </FormItem>
            </Col>
            <Col span="23">
              <FormItem prop="tableIdArr"
                        :rules="{ required: true, type: 'array', message: '关联测量表不能为空', trigger: 'change' }"
                        :label="'关联测量表'">
                <Select v-model="formValidate.tableIdArr" filterable multiple label-in-value
                        @on-change="handleSelectChange">
                  <Option v-for="item in tableList" :value="item.id" :key="item.id">{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            触发方式配置
          </p>
          <Row>
            <Col span="23">
              <FormItem prop="triggerType" :label="'触发类型'"
                        :rules="{ required: true, message: '触发类型不能为空', trigger: 'change' }">
                <RadioGroup v-model="formValidate.triggerType">
                  <Radio v-for="item in planList" :label="item.code" :key="item.code">{{ item.name }}
                  </Radio>
                </RadioGroup>
                <span v-if="lastTime" style="margin-left: 50px">最新执行时间：{{ lastTime }}</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="6" v-if="formValidate.triggerType == '01'">
              <FormItem label="周期类型" prop="triggerObj.type"
                        :rules="{ required: true, message: '计划说明不能为空', trigger: 'blur' }">
                <Select v-model="formValidate.triggerObj.type" @on-change="changeTriggerType">
                  <Option v-for="item in zqlxList" :value="item.code" :key="item.code">{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type == 'day'">
              <FormItem label="间隔天数" prop="triggerObj.num"
                        :rules="{ required: true, message: '间隔天数不能为空', trigger: 'blur' }">
                <Input v-model="formValidate.triggerObj.num" placeholder="请输入间隔天数(天)" type="number"/>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type == 'week'">
              <FormItem label="间隔每周" prop="triggerObj.numArr"
                        :rules="{ required: true, type: 'array', message: '间隔周数不能为空', trigger: 'blur' }">
                <CheckboxGroup v-model="formValidate.triggerObj.numArr" @on-change="changeWeeks">
                  <Checkbox v-for="item in weekList" :label="item.code" :key="item.code">{{ item.name }}
                  </Checkbox>
                </CheckboxGroup>
              </FormItem>
            </Col>

            <Col span="6"
                 v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type == 'month'">
              <FormItem label="月份间隔" prop="triggerObj.monthNum"
                        :rules="{ required: true, type: 'number', message: '请选择月份间隔必须在0-12之间', trigger: 'change' }">
                <InputNumber v-model="formValidate.triggerObj.monthNum" :min="0" :max="12"
                             placeholder="请输入月份间隔必须在0-12之间"
                             :active-change="false" :precision="0" style="width: 268px;"></InputNumber>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type == 'year'">
              <FormItem label="年份间隔" prop="triggerObj.yearNum"
                        :rules="{ required: true, type: 'number', message: '请选择年份间隔必须在0-12之间', trigger: 'blur' }">
                <InputNumber v-model="formValidate.triggerObj.yearNum" :min="0" :max="12"
                             placeholder="请输入年份间隔必须在0-12之间"
                             :active-change="false" :precision="0" style="width: 268px;"></InputNumber>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type == 'cus'">
              <FormItem label="配置触发周期" prop="triggerObj.cron"
                        :rules="{ required: true, message: '配置触发周期不能为空', trigger: 'blur' }">
                <el-popover v-model="cronPopover">
                  <vueCron @change="onChangeCron" @close="cronPopover = false">
                  </vueCron>
                  <el-input slot="reference" @click="cronPopover = true"
                            v-model="formValidate.triggerObj.cron" placeholder="请输入定时策略"
                            size="small"></el-input>
                </el-popover>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type != 'cus'">
              <FormItem label="开始时间" prop="triggerObj.startTime"
                        :rules="{ required: true, type: 'date', message: '开始时间不能为空', trigger: 'change' }">
                <DatePicker v-model="formValidate.triggerObj.startTime" :options="startOptions"
                            @on-open="cleanDatePickerDom" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            placeholder="请选择执行时间"
                            :key="'picker1'"/>
              </FormItem>
            </Col>

            <Col span="6" v-if="formValidate.triggerType == '01' && formValidate.triggerObj.type != 'cus'">
              <FormItem label="结束时间" prop="triggerObj.endTime"
                        :rules="{ required: true, type: 'date', message: '结束时间不能为空', trigger: 'change' }">
                <DatePicker v-model="formValidate.triggerObj.endTime" :options="endOptions"
                            @on-open="cleanDatePickerDom" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            placeholder="请选择执行时间"
                            :key="'picker1'"/>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23" v-if="formValidate.triggerType == '02'">
              <FormItem label="触发条件" prop="triggerConfig"
                        :rules="{ required: true, message: '触发条件不能为空', trigger: 'blur' }">
                <Input v-model="formValidate.triggerConfig" placeholder="请输入触发条件SQL" type="textarea"
                       :autosize="{ minRows: 2, maxRows: 5 }"/>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23" v-if="formValidate.triggerType === '03'">
              <FormItem label="执行时间" prop="triggerConfig"
                        :rules="{ required: true, type: 'date', message: '执行时间不能为空', trigger: 'change' }">
                <DatePicker v-model="formValidate.triggerConfig" :options="dateOptions" type="datetime"
                            format="yyyy-MM-dd HH:mm:ss" placeholder="请选择执行时间"/>
              </FormItem>
            </Col>
          </Row>
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            推送对象范围配置
          </p>
          <Row style="padding-top: 15px;">
            <Col span="23">
              <FormItem label="推送人员选择" prop="selectUseIdList"
                        :rules="{ required: true, message: '推送人员选择不能为空', trigger: 'change' }">
                <Input v-model="formValidate.selectUseIdList" placeholder="请选择推送人员" @on-focus="open = true"
                       style="width: 100%"/>
              </FormItem>
            </Col>
          </Row>
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            测评规则
          </p>
          <Row style="padding-bottom: 30px;">
            <Col span="23">
              <FormItem label="答题次数" prop="evalNumber"
                        :rules="{ required: true, message: '答题次数不能为空', type: 'number', trigger: 'change' }">
                <RadioGroup v-model="formValidate.evalNumber">
                  <Radio v-for="item in dtcsList" :label="item.code" :key="item.code">{{ item.name }}</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="23" style="display: flex;">
              <FormItem label="完成时限" prop="completionDeadline"
                        :rules="{ required: true, message: '完成时限不能为空', type: 'number', trigger: 'blur' }">
                <el-input-number v-model="formValidate.completionDeadline" :min="0" style="width: 50%;"
                                 type="number"/>
                <span>天内</span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>

      <div class="bsp-base-fotter">
        <Button @click="handleReset('formValidate')" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSubmit('formValidate')" :loading="loading">确认</Button>
      </div>
    </div>
    <div v-if="detailContainer" class="detail-container">
      <div class="detail-container-child">
        <div class="plan-add-user">
          <div class="header-plan-name">
            <h3>{{ planInfo.planName }}</h3>
            <div class="color-num-plan">
              <div>计划编号{{ planInfo.planCode }}</div>
              <div>{{ planInfo.planTypeName }}</div>
            </div>

            <ul class="create-time-person">
              <li>
                <div><span>创建时间：</span><span>{{ planInfo.addTime }}</span></div>
                <div class="status-creater" style="margin-top: 12px;"><span>创 建 人：</span><span>{{
                    planInfo.addUserName
                  }}</span></div>
              </li>

              <li style="margin-left: 80px;">
                <div><span>测评状态：</span><span>{{ planInfo.statusName }}</span></div>
                <div class="status-creater" style="margin-top: 12px;"><span>启用状态：</span><span>{{
                    planInfo.enableStatus == 1 ? "启用" : "禁用"
                  }}</span></div>
              </li>

            </ul>
          </div>
          <div class="plan-status-time">
            <ul>
              <li class="center-li">
                <div><span></span><span class="color-title line_throuth">{{ planInfo.runDays }}天</span>
                </div>
                <div><span>已运行</span><span></span></div>
              </li>

              <li class="center-li">
                <div><span></span><span class="color-title line_throuth">{{ planInfo.fillNums }}份</span>
                </div>
                <div><span>累计完成填写</span><span></span></div>
              </li>

              <li class="center-li">
                <div><span></span><span class="color-title line_throuth">{{
                    planInfo.noFillNums
                  }}份</span></div>
                <div><span>当前未完成</span><span></span></div>
              </li>

              <li class="center-li">
                <div><span></span><span class="color-title line_throuth">{{
                    planInfo.overFillNums
                  }}份</span>
                </div>
                <div><span>累计逾期</span><span></span></div>
              </li>
            </ul>
          </div>
        </div>
        <!-- Tab切换 -->
        <div class="tab-detail-circumstance">
          <Tabs value="name1" @on-click="getEvalPlanPushRecordlist">
            <TabPane label="测评计划详情" name="name1">
              <Form ref="formData" inline>
                <div class="fm-content-box">
                  <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda"/>
                    量表信息
                  </p>
                  <Row>
                    <Col span="3"><span>推送测评量表</span></Col>
                    <Col span="9"><span>{{ planInfo.tableName }}</span></Col>
                    <Col span="3"><span>规定完成时限</span></Col>
                    <Col span="9"><span>{{ planInfo.completionDeadline }}天内</span></Col>
                  </Row>

                  <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda"/>
                    触发方式
                  </p>
                  <Row v-if="planInfo.triggerType == '03'">
                    <Col span="3" class="col-title"><span>触发方式</span></Col>
                    <Col span="9"><span>{{ planInfo.triggerTypeName }}</span></Col>
                    <Col span="3" class="col-title"><span>触发时间</span></Col>
                    <Col span="9"><span>{{ planInfo.triggerConfig }}</span></Col>
                  </Row>
                  <Row v-if="planInfo.triggerType == '02'">
                    <Col span="3" class="col-title"><span>触发方式</span></Col>
                    <Col span="9"><span>{{ planInfo.triggerTypeName }}</span></Col>
                    <Col span="3" class="col-title"><span>触发条件</span></Col>
                    <Col span="9"><span>{{ planInfo.triggerConfig }}</span></Col>
                  </Row>
                  <Row v-if="planInfo.triggerType == '01'">
                    <Col span="3" class="col-title"><span>触发方式</span></Col>
                    <Col span="5"><span>{{ planInfo.triggerTypeName }}</span></Col>
                    <Col span="3" class="col-title"><span>周期类型</span></Col>
                    <Col span="5"><span>{{ planInfo.typeName }}</span></Col>
                    <Col span="3" class="col-title"><span>间隔天数</span></Col>
                    <Col span="5"><span>{{ planInfo.triggerConfig.num }}</span></Col>
                  </Row>
                  <Row v-if="planInfo.triggerType == '01'">
                    <Col span="3" class="col-title"><span>开始时间</span></Col>
                    <Col span="5"><span>{{ planInfo.triggerConfig.startTime }}</span></Col>
                    <Col span="3" class="col-title"><span>结束时间</span></Col>
                    <Col span="5"><span>{{ planInfo.triggerConfig.endTime }}</span></Col>
                    <Col span="3" class="col-title"><span></span></Col>
                    <Col span="5"><span></span></Col>
                  </Row>

                  <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda"/>
                    测评规则
                  </p>
                  <Row>
                    <Col span="3" class="col-title"><span>答题次数</span></Col>
                    <Col span="9"><span>{{ planInfo.evalNumber === 1 ? '1次' : '多次' }}</span></Col>
                    <Col span="3" class="col-title"><span>完成时限</span></Col>
                    <Col span="9"><span>{{ planInfo.completionDeadline }}天内</span></Col>
                  </Row>

                  <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda"/>
                    推送对象
                  </p>
                  <Row>
                    <Col span="3" class="col-title"><span>推送对象</span></Col>
                    <Col span="21">
                      <span style="text-decoration: underline;cursor: pointer;"
                            @click="handleGetTsdxList('', '')">{{ planInfo.pushTargetName }}</span>
                    </Col>
                  </Row>

                </div>
              </Form>
            </TabPane>
            <TabPane label="执行情况" name="name2">
              <div class="btn-list">
                <RadioGroup v-model="txStatus" type="button" button-style="solid"
                            @on-change="changeTxStatus">
                  <Radio label="1">已填写：{{
                      txInfo['YTX']
                    }}
                  </Radio>
                  <Radio label="0">未填写：{{
                      txInfo['DTX']
                    }}
                  </Radio>
                  <Radio label="2">已逾期：{{ txInfo['YYQ'] }}</Radio>
                </RadioGroup>
              </div>
              <div class="table-container">
                <Table height="380" :columns="columns" :data="zxqlList"></Table>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleResetDetail" style="margin-right: 10px;">取消</Button>
      </div>
    </div>
    <Modal v-model="open" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
           title="人员列表">
      <div class="select-use">
        <prisonSelect v-if="open" ref="prisonSelect" ryzt="ZS" :isMultiple="true"
                      :selectUseIds="selectUseIds"/>
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="open = false" class="save">关 闭</Button>
      </div>
    </Modal>
    <Modal v-model="dtkShow" :title="'答题卡列表'" width="1100" :footer-hide="true">
      <div class="dtk-container">
        <div class="left-img">
          <div class="person-prison">
            <h2 class="person-name">{{ personInfo.jgryxm }}</h2>
            <span class="prison-name">{{ personInfo.roomName }}</span>
          </div>

        </div>
        <div class="right-title">
          <div class="header-plan-name">
            <h3>{{ personInfo.planName }}</h3>
            <span class="plan-code">计划编号{{ personInfo.planCode }}</span>
            <span class="plan-type-name">{{ personInfo.planTypeName }}</span>
            <span class="filling-status-name">{{ personInfo.fillingStatusName }}</span>
          </div>

          <div class="tssj-txzt">
            <div class="tssj-time">
              <span>推送时间：</span>
              <span>{{ personInfo.pushTime }}</span>
            </div>
            <ul>
              <li>
                <span>填写时间：</span>
                <span>{{ personInfo.fillingTime }}</span>
              </li>
              <li>
                <span>填写状态：</span>
                <span>{{ personInfo.fillingStatusName }}</span>
              </li>
              <li>
                <span>填写平台：</span>
                <span>{{ personInfo.pushTime }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="table-container-list">
        <Table height="300" :columns="dtkColumns" :data="pushRecordAnswerList"></Table>
      </div>
    </Modal>
    <!-- 推送人员列表 -->
    <Modal v-model="tsryOpen" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1100"
           title="推送人员列表">
      <div class="select-use">
        <Form ref="formInline" :model="formInline" :label-width="140" inline>
          <FormItem label="监室号">
            <Input v-model="formInline.areaName" placeholder=""></Input>
          </FormItem>
          <FormItem label="人员名称">
            <Input v-model="formInline.xm" placeholder=""></Input>
          </FormItem>
          <FormItem>
            <Button @click="handleSetSearchTsry">重置</Button>
            <Button type="primary" @click="handleSearchTsry" style="margin-left: 20px;">查询</Button>
          </FormItem>
        </Form>
        <Table height="300" :columns="columnsTsry" :data="tsrylList"></Table>

      </div>
      <div slot="footer">
        <Button type="primary" @click="tsryOpen = false" class="save">确 定</Button>
        <Button @click="tsryOpen = false" class="save">关 闭</Button>
      </div>
    </Modal>
    <!-- 答题卡预览详情内容 -->
    <div class="add-template" v-if="dtkContainer">
      <div style="width: 100%;height: 100%;">
        <!-- 答题答题模式 -->
        <div class="preview-page" v-if="normalPreview">
          <div class="preview-detail-container">
            <previewDetail :id="tableId"/>
          </div>
          <div class="topic-container">
            <tablePreviewCommon :id="tableId" :selectedRadio="!showSubmit ? selectedRadio : {}"
                                :selectedCheckbox="!showSubmit ? selectedCheckbox : {}"
                                :selectedSimple="!showSubmit ? selectedSimple : {}"
                                :previewAnswerInfo="!showSubmit ? previewAnswerInfo : {}" :justPreview="justPreview">
              <!-- <template v-slot:changeBtnType>
                  <btnChange @handleChangeBtn="handleChangeBtn" />
              </template> -->
            </tablePreviewCommon>
          </div>
        </div>
        <!-- 极简模式预览 -->
        <!-- <div v-if="simplePerview">
            <div class="preview-detail-container">
                <previewDetail :id="tableId" />
                <tablePreviewSimple :id="tableId" :selectedRadio="!showSubmit ? selectedRadio : {}"
                    :selectedCheckbox="!showSubmit ? selectedCheckbox : {}"
                    :selectedSimple="!showSubmit ? selectedSimple : {}">
                    <template v-slot:changeBtnType>
                        <btnChange changeTip="切换常规答题模式" @handleChangeBtn="handleChangeNormal" />
                    </template>
                </tablePreviewSimple>
            </div>
        </div> -->
      </div>

      <div class="bsp-base-fotter">
        <Button @click="handleClosePriview" style="margin-right: 10px;">取消</Button>
      </div>
    </div>
  </div>
</template>

<script>
import {sDataGrid} from 'sd-data-grid'
import {mapActions} from 'vuex'
import {prisonSelect} from "sd-prison-select";
import {cron} from 'vue-cron'
import previewDetail from './previewDetail.vue';
import tablePreviewCommon from './tablePreviewCommon.vue';
import tablePreviewSimple from './tablePreviewSimple.vue';
import btnChange from './btnChange.vue';
import dayjs from 'dayjs'

export default {
  name: "plan",
  data() {
    return {
      tsryOpen: false,
      cronPopover: false,
      open: false,
      tableType: '',
      tableContainer: true,
      AddFormContainer: false,
      dtkContainer: false,
      dtkShow: false,
      normalPreview: true,
      simplePerview: false,
      // modalVisible: false,
      params: {},
      formValidate: {
        planName: "",
        remark: "",
        enableStatus: 1,
        tableIdArr: [],
        triggerType: "01",
        evalNumber: 1,
        triggerConfig: "",
        completionDeadline: 0,
        pushTarget: {},
        selectUseIdList: "",
        triggerObj: {
          type: 'day',
          startTime: "",
          endTime: "",
          num: "",
          numArr: [],
          monthNum: 1,
          yearNum: "",
          cron: ""
        },
      },
      txStatus: "1",
      detailContainer: false,
      tableList: [],
      planList: [],
      selectUseIds: "",
      detailInfo: {},
      timeList: [],
      dtcsList: [],
      zqlxList: [],
      cycleConfigArr: [],
      loading: false,
      weekList: [{name: '星期一', code: 1}, {name: '星期二', code: 2}, {name: '星期三', code: 3},
        {name: '星期四', code: 4}, {name: '星期五', code: 5}, {name: '星期六', code: 6},
        {name: '星期日', code: 7}],
      id: "",
      planInfo: {},
      planCode: "",
      txInfo: {},
      zxqlList: [],
      formInline: {
        areaName: "",
        xm: "",
      },
      tsrylList: [],
      dtkColumns: [
        {
          title: '序号',
          key: 'idx',
        },
        {
          title: '提交时间',
          key: 'submitTime',
        },
        {
          title: '作答用时',
          key: 'useTime',
        },
        {
          title: '测评得分',
          key: 'score',
        },
        {
          title: '测评结果',
          key: 'evalResults',
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {type: 'primary'},
                on: {click: () => this.handleGetDetails(params.row)}
              }, '详情')
            ])

          }
        }
      ],
      columnsTsry: [
        {
          title: '序号',
          key: 'number',
        },
        {
          title: '推送人员',
          key: 'xm',
        },
        {
          title: '监室',
          key: 'room_name',
        },
      ],
      columns: [
        {
          title: '测评编号',
          key: 'evalNo',
        },
        {
          title: '推送对象',
          key: 'jgryxm',
        },
        {
          title: '监室',
          key: 'roomName',
        },
        {
          title: '推送时间',
          key: 'pushTime',
        },
        {
          title: '填写完成时间',
          key: 'fillingTime',
        },
        {
          title: '测评得分',
          key: 'score',
        },
        {
          title: '测评结果',
          key: 'evalResults',
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 120,
          render: (h, params) => {
            if (params.row.fillingStatus == 1) {
              return h('div', [
                h('Button', {
                  props: {type: 'primary'},
                  on: {click: () => this.detailTable(params.row)}
                }, '详情')
              ])
            }
          }
        }
      ],
      isUpdateId: '',
      selectedData: [],
      tableName: [],
      pushRecordAnswerList: [],
      showSubmit: true,
      previewAnswerInfo: {},
      tableId: "",
      selectedCheckbox: {},
      selectedRadio: {},
      selectedSimple: {},
      personInfo: {},
      justPreview: false,
      dateOptions: {
        disabledDate: (date) => {
          const todayStart = new Date().setHours(0, 0, 0, 0);
          return date.valueOf() < todayStart;
        }
      },
      startOptions: {
        disabledDate: (date) => {
          // 结束时间存在时，禁止选择其之后的日期
          return this.formValidate.triggerObj.endTime && date.valueOf() > this.formValidate.triggerObj.endTime.valueOf();
        }
      },
      endOptions: {
        disabledDate: (date) => {
          // 开始时间存在时，禁止选择其之前的日期
          return this.formValidate.triggerObj.startTime && date.valueOf() < this.formValidate.triggerObj.startTime.valueOf();
        }
      },
      lastTime: ''
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getTableTypeByUrl() {
      if (this.$route.query.tableType) {
        this.tableType = this.$route.query.tableType
      } else {
        this.tableType = ''
      }
      this.handleGetPsyList('02', this.tableType)
    },
    handleGetPsyList(usageStatus, tableType) {
      this.authPostRequest({
        url: this.$path.evalTable_list,
        params: {
          name: "",
          orgCode: "",
          tableType: tableType,
          usageStatus: usageStatus
        }
      }).then(res => {
        if (res.success) {
          this.tableList = res.data
        }
      })
    },
    handleGetJHCFFS() {
      this.authGetRequest({url: "/bsp-com/static/dic/acp/ZD_PSY_JHCFFS.js"}).then(res => {
        let plan = eval('(' + res + ')')
        this.planList = plan()
      })
    },
    handleGetZD_PSY_ZQLX() {
      this.authGetRequest({url: "/bsp-com/static/dic/acp/ZD_PSY_ZQLX.js"}).then(res => {
        let timeType = eval('(' + res + ')')
        this.timeList = timeType()
        this.zqlxList = timeType()
      })
    },
    handleGetZD_PSY_DTCS() {
      this.authGetRequest({url: "/bsp-com/static/dic/acp/ZD_PSY_DTCS.js"}).then(res => {
        let dtcs = eval('(' + res + ')')
        this.dtcsList = dtcs()
        this.dtcsList.forEach(item => {
          item.code = Number(item.code)
        })
      })
    },
    changeTxStatus(code) {
      this.hanldeEvalPlanPushRecordListNum(code)
    },
    handleAdd() {
      this.isUpdateId = ""
      this.AddFormContainer = true
      this.tableContainer = false
      this.selectUseIds = ''
      this.selectUseIdList = ''
      this.formValidate.selectUseIdList = ''
    },
    handleSelectChange(selectedItems) {
      this.selectedData = selectedItems.map(item => ({
        id: item.value,
        name: item.label
      }));
      this.tableName = this.selectedData.map(item => item.name.replaceAll('\n                            ', ''))
    },
    onChangeCron(v) {
      this.formValidate.triggerObj.cron = v
    },
    handleEdit(index, row) {
      this.authGetRequest({url: this.$path.evalPlan_get, params: {id: row.id}}).then(res => {
        if (res.success) {
          this.formValidate.id = res.data.id
          this.formValidate.planName = res.data.planName
          // this.formValidate.planCode = res.data.planCode
          // this.formValidate.planType = res.data.planType
          this.formValidate.remark = res.data.remark
          // this.formValidate.tableId = res.data.tableId
          // this.formValidate.tableName = res.data.tableName
          this.formValidate.triggerType = res.data.triggerType
          this.formValidate.triggerConfig = res.data.triggerConfig
          this.formValidate.pushTarget = res.data.pushTarget
          // this.formValidate.pushTargetName = res.data.pushTargetName
          // this.formValidate.pushTargetUserName = res.data.pushTargetUserName
          this.formValidate.evalNumber = res.data.evalNumber
          this.formValidate.completionDeadline = res.data.completionDeadline
          // this.formValidate.enableMessagePush = res.data.enableMessagePush
          // this.formValidate.messagePush = res.data.messagePush
          this.formValidate.status = res.data.status
          this.formValidate.enableStatus = res.data.enableStatus
          this.formValidate.enableStatus = Number(this.formValidate.enableStatus)
          this.isUpdateId = res.data.id;
          this.formValidate.selectUseIdList = res.data.pushTargetUserName
          if (res.data.pushTarget) {
            this.selectUseIds = JSON.parse(res.data.pushTarget).jgrybms.join(",")
          }
          this.formValidate.completionDeadline = res.data.completionDeadline
          if (this.formValidate.triggerType == '01') {
            this.$set(this.formValidate, 'triggerObj', JSON.parse(res.data.triggerConfig))
            this.$set(this.formValidate.triggerObj, 'startTime', new Date(JSON.parse(res.data.triggerConfig).startTime));
            this.$set(this.formValidate.triggerObj, 'endTime', new Date(JSON.parse(res.data.triggerConfig).endTime));
            if (JSON.parse(res.data.triggerConfig).type == 'year') {
              this.$set(this.formValidate.triggerObj, 'yearNum', JSON.parse(res.data.triggerConfig).num);
            }
            if (JSON.parse(res.data.triggerConfig).type == 'day') {
              this.$set(this.formValidate.triggerObj, 'num', JSON.parse(res.data.triggerConfig).num);
            }
            if (JSON.parse(res.data.triggerConfig).type == 'month') {
              this.$set(this.formValidate.triggerObj, 'monthNum', JSON.parse(res.data.triggerConfig).num);
            }
            if (JSON.parse(res.data.triggerConfig).type == 'week') {
              let numWeekList = JSON.parse(res.data.triggerConfig).num.split(',')
              let newNum = numWeekList.map(item => {
                return Number(item)
              })
              this.formValidate.triggerObj.numArr = newNum
            }
          } else if (this.formValidate.triggerType === '02') {
            // 条件触发
            this.formValidate.triggerConfig = res.data.triggerConfig
          } else if (this.formValidate.triggerType === '03') {
            // 单次触发
            this.formValidate.triggerConfig = new Date(res.data.triggerConfig)
          }
          this.$set(this.formValidate, 'tableIdArr', res.data.tableId.split(','))
          this.tableContainer = false
          this.AddFormContainer = true
        }
      })
    },
    handleDetail(index, row) {
      this.authGetRequest({url: this.$path.evalPlan_get, params: {id: row.id}}).then(res => {
        if (res.success) {
          this.tableContainer = false;
          this.detailContainer = true
          this.planInfo = res.data
          this.planInfo.pushTarget = res.data.pushTarget

          if (res.data.triggerType == '01') {
            this.planInfo.triggerConfig = JSON.parse(res.data.triggerConfig)
            switch (this.planInfo.triggerConfig.type) {
              case 'day':
                this.planInfo.typeName = "按日"
                break;
              case 'week':
                this.planInfo.typeName = "按周"
                break;
              case 'month':
                this.planInfo.typeName = "按月"
                break;
              case 'year':
                this.planInfo.typeName = "按年"
                break;
              default:
                this.planInfo.typeName = "自定义"
                break;
            }
          }
          this.planCode = res.data.planCode;
        }
      })
    },
    handleChangeBtn() {
      this.normalPreview = false
      this.simplePerview = true
    },
    handleChangeNormal() {
      this.normalPreview = true
      this.simplePerview = false
    },

    changeSwitch(row) {
      // 如果
      let status = row.enable_status == '1' ? "1" : '0'
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否更新？',
        onOk: () => {
          this.$store.dispatch('postRequest', {
            url: this.$path.evalPlan_updateEableStatus,
            params: {
              enableStatus: status,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        },
        onCancel: () => {
          let num = row.enableStatus == 1 ? 1 : 0
          this.$store.dispatch('postRequest', {
            url: this.$path.evalPlan_updateEableStatus,
            params: {
              enableStatus: num,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        }
      })
    },
    changeTriggerType(code) {
      this.$set(this.formValidate.triggerObj, 'type', code)
    },
    cleanDatePickerDom() {
      const pickerDom = document.getElementById('ui-datepicker-div');
      if (pickerDom) pickerDom.remove();
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          //周期
          let msg = ""
          let isId = ""
          if (this.isUpdateId) {
            msg = "更新成功"
            isId = this.isUpdateId
          } else {
            msg = "创建成功"
            isId = ""
            this.$set(this.formValidate, 'status', '01')
          }
          if (this.formValidate.pushTarget !== null && typeof this.formValidate.pushTarget === 'object'
            && !Array.isArray(this.formValidate.pushTarget)) {
            this.formValidate.pushTarget = JSON.stringify(this.formValidate.pushTarget)
          }
          if (this.formValidate.completionDeadline <= 0) {
            this.$Message.error('请选择完成时限')
            return;
          }
          if (this.formValidate.triggerType == '01') {
            this.$set(this.formValidate, 'triggerConfig', {})
            const {type, num, startTime, endTime, numArr, cron, monthNum, yearNum} = this.formValidate.triggerObj
            this.formValidate.triggerConfig.type = type
            this.formValidate.triggerConfig.num = num
            this.formValidate.triggerConfig.cron = cron
            // 当选中周的时候，将num传参1，2
            if (type == 'week') {
              this.formValidate.triggerConfig.num = numArr.join(',')
            }
            if (type == 'month') {
              this.formValidate.triggerConfig.num = monthNum
            }
            if (type == 'year') {
              this.formValidate.triggerConfig.num = yearNum
            }
            if (type == 'cus') {
              this.formValidate.triggerConfig.startTime = ""
              this.formValidate.triggerConfig.endTime = ""
            } else {
              this.formValidate.triggerConfig.startTime = dayjs(startTime).format('YYYY-MM-DD HH:mm:ss')
              this.formValidate.triggerConfig.endTime = dayjs(endTime).format('YYYY-MM-DD HH:mm:ss')
            }
            this.formValidate.triggerConfig = JSON.stringify(this.formValidate.triggerConfig)
          } else if (this.formValidate.triggerType == '03') {
            this.formValidate.triggerConfig = dayjs(this.formValidate.triggerConfig).format('YYYY-MM-DD HH:mm:ss')
            this.$set(this.formValidate, 'triggerConfig', this.formValidate.triggerConfig)
          } else {
            this.$set(this.formValidate, 'triggerConfig', this.formValidate.triggerConfig)
          }
          this.$set(this.formValidate, 'tableId', this.formValidate.tableIdArr.join(','))
          this.$set(this.formValidate, 'tableName', this.tableName.join(','))
          this.loading = true
          this.authPostRequest({
            url: this.$path.evalPlan_createOrUpdate,
            params: {...this.formValidate, id: isId}
          }).then(res => {
            if (res.success) {
              console.log(res.data)
              this.loading = false
              this.$Message.success(msg)
              this.tableContainer = true
              this.AddFormContainer = false
              this.isUpdateId = ""


              setTimeout(() => {
                this.on_refresh_table()
                this.handleResetForm()
              }, 200)

              this.$refs[name].resetFields();

            } else {
              this.loading = false
              this.$Message.error(res.message)
            }

          })
        } else {
          this.$Message.error('验证失败');
          return;
        }
      })
    },
    // 重置表单
    handleResetForm() {
      try {
        this.formValidate.planName = ""
        this.formValidate.remark = ""
        this.formValidate.enableStatus = 1
        this.formValidate.tableIdArr = []
        this.formValidate.triggerType = "01"
        this.formValidate.evalNumber = 1
        this.formValidate.triggerConfig = ""
        this.formValidate.completionDeadline = ""
        if (this.formValidate.triggerObj) {
          this.formValidate.triggerObj.type = "day"
          this.formValidate.triggerObj.startTime = ""
          this.formValidate.triggerObj.endTime = ""
          this.formValidate.triggerObj.monthNum = ""
          this.formValidate.triggerObj.yearNum = ""
          this.formValidate.triggerObj.num = ""
          this.formValidate.triggerObj.numArr = []
        }

      } catch (error) {
        console.error(error, '[error]');
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
      this.handleResetForm()
      this.tableContainer = true
      this.AddFormContainer = false
    },
    handleResetDetail() {
      this.tableContainer = true;
      this.detailContainer = false
    },
    handleDelete(index, {id}) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.lifeTemplate_delete,
            params: {
              ids: id
            }
          }).then(res => {
            if (res.success) {
              this.on_refresh_table()
            }
          })
        }
      })
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1)
    },
    useSelect() {
      this.detailInfo = {
        ...this.$refs.prisonSelect.checkedUse,
      };
      let checkedPriJgrybms = Object.values(this.detailInfo)
      this.selectUseIds = checkedPriJgrybms.map((item) => {
        return `${item.roomName}/${item.xm}`
      }).join(',')
      this.formValidate.selectUseIdList = checkedPriJgrybms.map((item) => {
        return `${item.roomName}/${item.xm}`
      }).join(',')
      let areaCodes = checkedPriJgrybms.map((item) => {
        return `${item.jsh}`
      })
      let jgrybms = checkedPriJgrybms.map((item) => {
        return `${item.jgrybm}`
      })
      this.formValidate.pushTarget = {areaCodes: [], jgrybms: jgrybms}
      this.open = false;
    },
    changeWeeks() {
    },
    getEvalPlanPushRecordlist(name) {
      if (name == 'name2') {
        this.handleGetPlanPushRecordlist()
        this.hanldeEvalPlanPushRecordListNum(1)
      }
    },
    handleGetPlanPushRecordlist() {
      this.postRequest({url: this.$path.evalPlanPushRecord_list, params: {planCode: this.planCode}}).then(res => {
        if (res.success) {
          this.txInfo = res.data
        }
      })
    },
    hanldeEvalPlanPushRecordListNum(fillingStatus, planCode = this.planCode) {
      this.authPostRequest({
        url: this.$path.evalPlanPushRecord_list_num,
        params: {fillingStatus, planCode}
      }).then(res => {
        if (res.success) {
          this.zxqlList = res.data
          this.$set(this, 'zxqlList', res.data)
        }
      })
    },
    handleGetYtx() {
      this.hanldeEvalPlanPushRecordListNum(1)
    },
    // 执行情况的详情答题卡页面
    detailTable(params) {
      this.tableId = params.tableId
      this.personInfo = params
      this.authPostRequest({
        url: this.$path.evalPlanPushRecordAnswer_list,
        params: {evalNo: params.evalNo}
      }).then(res => {
        if (res.success) {
          this.pushRecordAnswerList = res.data
          this.pushRecordAnswerList.forEach((item, index) => {
            item.idx = index + 1
            item.useTime = `${item.timeSpentAnswer.hour}:${item.timeSpentAnswer.minute}:${item.timeSpentAnswer.nano}`
          })
          this.dtkShow = true
        }
      })
    },
    handleGetDetails(item) {
      this.handletablePreviewCommonAndAnswer(item.id)
      this.detailContainer = false
      this.dtkContainer = true
      this.dtkShow = false
      this.previewAnswerInfo = item
      this.justPreview = true
      // this.handletablePreviewSimpleAndAnswer(item.id)
      this.showSubmit = false
    },
    // 量表预览-常规模式-带答案
    handletablePreviewCommonAndAnswer(answerId) {
      this.postRequest({
        url: this.$path.tablePreviewCommonAndAnswer,
        params: {tableId: this.tableId, answerId}
      }).then(res => {
        if (res.success) {
          setTimeout(() => {
            this.selectedRadio = res.data.dxt.questions.reduce((obj, item) => {
              obj[item.questionId] = item.answer;
              return obj;
            }, {})

            this.selectedCheckbox = res.data.fxt.questions.reduce((obj, item) => {
              obj[item.questionId] = item.answer.split(',');
              return obj;
            }, {})


            this.selectedSimple = res.data.jdt.questions.reduce((obj, item) => {
              obj[item.questionId] = item.answer;
              return obj;
            }, {})

          }, 1000)
          // this.AddFormContainer = true;
          // this.detailContainer = false
        }
      })

    },
    handleClosePriview() {
      this.detailContainer = true
      this.dtkContainer = false
    },
    handleSearchTsry() {
      this.handleGetTsdxList(this.formInline.areaName, this.formInline.xm)
    },
    handleSetSearchTsry() {
      this.formInline.areaName = ""
      this.formInline.xm = ""
      this.handleGetTsdxList("", "")
    },
    handleGetTsdxList(areaName, xm) {
      this.tsryOpen = true
      this.authPostRequest({
        url: this.$path.getPushTarge_List,
        params: {areaName: areaName, xm: xm, planId: this.planInfo.id}
      }).then(res => {
        if (res.success) {
          this.tsrylList = res.data
          this.tsrylList.forEach((item, index) => {
            item['number'] = index + 1
          })
        }
      })
    },
    handleTriggerChange() {
      let lastParam = {
        triggerType: this.formValidate.triggerType
      }
      if (this.formValidate.triggerType == '01') {
        let obj = {
          type: this.formValidate.triggerObj.type,
          startTime: this.formValidate.triggerObj.startTime ?
            dayjs(this.formValidate.triggerObj.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
          endTime: this.formValidate.triggerObj.endTime ?
            dayjs(this.formValidate.triggerObj.endTime).format('YYYY-MM-DD HH:mm:ss') : '',
          cron: this.formValidate.triggerObj.cron
        }
        if (this.formValidate.triggerObj.type == 'day') {
          obj.num = this.formValidate.triggerObj.num ? this.formValidate.triggerObj.num : '0'
        } else if (this.formValidate.triggerObj.type == 'week') {
          obj.num = this.formValidate.triggerObj.numArr.join(',')
        } else if (this.formValidate.triggerObj.type == 'month') {
          obj.num = this.formValidate.triggerObj.monthNum ? this.formValidate.triggerObj.monthNum : '0'
        } else if (this.formValidate.triggerObj.type == 'year') {
          obj.num = this.formValidate.triggerObj.yearNum ? this.formValidate.triggerObj.yearNum : '0'
        }
        lastParam.triggerConfig = JSON.stringify(obj)
      } else if (this.formValidate.triggerType == '02') {
        // 条件触发
        lastParam.triggerConfig = this.formValidate.triggerConfig
      } else if (this.formValidate.triggerType == '03') {
        // 单次触发
        lastParam.triggerConfig = dayjs(this.formValidate.triggerConfig).format('YYYY-MM-DD HH:mm:ss')
      }
      this.authPostRequest({
        url: this.$path.evalPlan_getLastTime,
        params: lastParam
      }).then(res => {
        if (res.success) {
          this.lastTime = res.data
        } else {
          this.lastTime = ''
          this.$Message.error(res.message)
        }
      })
    }
  },

  components: {
    sDataGrid,
    prisonSelect,
    cron,
    previewDetail,
    tablePreviewCommon,
    tablePreviewSimple,
    btnChange
  },

  created() {
    // 只查询已启用的量表
    this.getTableTypeByUrl()
    this.handleGetJHCFFS()
    this.handleGetZD_PSY_ZQLX()
    this.handleGetZD_PSY_DTCS()
  },

  computed: {},
  watch: {
    'formValidate.triggerObj': {
      handler(newVal, oldVal) {
        this.handleTriggerChange(); // 触发自定义方法
      },
      deep: true // 深度监听对象内部属性变化
    },
    'formValidate.triggerType': {
      handler(newVal, oldVal) {
        if(newVal === '02'){
          this.formValidate.triggerConfig = ''
          this.lastTime = ''
          return;
        }
        this.handleTriggerChange(); // 触发自定义方法
      }
    },
    'formValidate.triggerConfig': {
      handler(newVal, oldVal) {
        this.handleTriggerChange(); // 触发自定义方法
      }
    }
  },

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN;
  font-weight: 700;
  font-size: 16px;
  color: #00244a;
  // margin-bottom: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}


.safety-checks-container {
  width: 100%;
  height: 100%;

  .detail-container {
    width: 100%;
    height: 100%;
    background-color: #F1F5F6;

    .detail-container-child {
      padding: 20px;

      .plan-add-user {
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 1px rgba(0, 34, 84, 0.12);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #E4EAF0;
        display: flex;
        align-items: center;

        .header-plan-name {
          // display: flex;
          align-items: center;
          margin: 16px 0 16px 16px;
          padding: 16px;
          background: url('../../../assets/images/header-bg-num-person.png') no-repeat center;
          margin-right: 32px;

          .color-num-plan {
            display: flex;
            align-items: center;
            margin-top: 12px;

            div:nth-child(1) {
              width: 180px;
              height: 26px;
              line-height: 20px;
              background: #E8ECF0;
              border-radius: 4px 4px 4px 4px;
              font-weight: normal;
              font-size: 14px;
              color: #7F8C9A;
              padding: 2px;
            }

            div:nth-child(2) {
              width: 64px;
              height: 26px;
              line-height: 20px;
              background: #FDF4E7;
              border-radius: 4px 4px 4px 4px;
              font-weight: normal;
              font-size: 14px;
              padding: 2px;
              color: #F09115;
              margin-left: 5px;
            }
          }

          .create-time-person {
            display: flex;
            list-style: none;
            margin-top: 12px;

            ul {
              list-style: none;

              li {
                // margin-top: 12px;

                .status-creater {
                  margin-top: 12px;
                }


              }
            }
          }
        }

        .plan-status-time {

          ul {
            list-style: none;
            display: flex;
            justify-content: space-around;
            // margin-top: 20px;
            // margin-left: -68px;
            // margin-top: 15px;

            li {

              // text-align: center;
              &.center-li {
                padding: 5px 0;
                width: 200px;
                text-align: center;
                background: #FFFFFF;
                box-shadow: 0px 2px 6px 1px rgba(0, 34, 84, 0.12);
                border-radius: 4px 4px 4px 4px;
                border: 1px solid #E4EAF0;
                margin-left: 16px;

                div:last-child {
                  font-weight: 400;
                  font-size: 16px;
                  color: #8D99A5;
                }
              }

              &.center-li:first-child {
                margin-left: 0px;
              }

              .color-title {
                font-weight: 700;
                font-size: 18px;
              }

              .line_throuth {
                font-weight: bold;
                font-size: 24px;
                color: #2390FF;
                line-height: 32px;
                text-align: center;
              }
            }
          }
        }
      }

      .tab-detail-circumstance {
        margin-top: 20px;
        width: 100%;
        background-color: #fff;
        padding: 20px;
        border-radius: 4px;
      }
    }


  }
}

.table-container {
  margin-top: 20px;
}

.push-recordAnswer-List {
  margin-top: 20px;

  ul {
    list-style: none;
    margin-bottom: 20px;
    padding: 20px 0 15px 30px;
    border-radius: 4px;
    background-color: #fff;
    position: relative;
    display: flex;
    align-items: center;
    text-align: center;

    li {
      margin-left: 100px;

      &:first-child {
        display: flex;
        align-items: center;
        margin-left: 0;
      }
    }
  }
}

.dtk-container {
  background: #FFFFFF;
  box-shadow: 0px 2px 6px 1px rgba(0, 34, 84, 0.12);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #E4EAF0;
  display: flex;
  // justify-content: space-between;
  padding: 16px;
  margin: 16px;

  .left-img {
    width: 320px;
    height: 136px;
    background: url('../../../assets/images/person-info-bg.png') no-repeat center;

    .person-prison {
      margin: 24px 84px 86px 136px;
    }
  }

  .right-title {
    margin-left: 16px;

    .header-plan-name {
      display: flex;
      align-items: center;

      .plan-code {
        width: 180px;
        height: 26px;
        background: #F5F7FA;
        border-radius: 4px 4px 4px 4px;
        padding: 4px;
        font-weight: normal;
        font-size: 14px;
        color: #7F8C9A;
        margin-left: 16px;
      }

      .plan-type-name {
        width: 64px;
        height: 26px;
        background: #FDF4E7;
        border-radius: 4px 4px 4px 4px;
        padding: 4px;
        font-weight: normal;
        font-size: 14px;
        color: #F09115;
        margin-left: 12px;
      }

      .filling-status-name {
        width: 64px;
        height: 26px;
        background: #E4FCEA;
        border-radius: 4px 4px 4px 4px;
        font-weight: normal;
        font-size: 14px;
        color: #00B42A;
        padding: 4px 11px;
        margin-left: 12px;
      }
    }

    .tssj-txzt {
      font-size: 16px;
      color: #8D99A5;

      .tssj-time {
        margin-top: 16px;
      }

      ul {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;

        li {
          margin-left: 20px;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }

  }

  ul {
    list-style: none;
  }
}

.table-container-list {
}
</style>
