<template>
  <div class="content-defaulet">
    <div class="content-defaulet-main">
      <tabs ref="tabs" v-if="showFormCompnent" mark="adjustmentRoom" @changeTabsMark="changeTabsMark"
        :curmark="tabsMark" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func, row, index, resetMethod, funcMark }">
          <Button type="primary" v-if="func.includes(globalAppCode + ':gjywjstzlb:add')"
            @click.native="addEvent('add', row, '', resetMethod)">监室调整申请</Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':jsdzlbdsh:patchApprove')"
            @click.native="patchApprove('patchApprove', row, '', resetMethod)">批量审批</Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index, resetMethod, funcMark }">
          <Button type="primary" v-if="func.includes(globalAppCode + ':gjywjstzlb:approve') && row.status == '02'"
            @click.native="approve(row, row, '', resetMethod)">审批</Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':jsdzlbdsh:approve') && row.status == '02'"
            @click.native="approve(row, row, '', resetMethod)">审批</Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':gjywjstzlb:info')" style="margin: 0 10px"
            @click.native="info(row, row, '', resetMethod)">详情</Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':gjywjstzlb:Bring') && row.status == '04'"
            @click.native="bringTake(row, row, '', resetMethod)">带入带出登记</Button>
        </template>
      </tabs>
      <div v-if="!showFormCompnent">
        <component v-bind:is='component' @on_show_table="on_show_table" :selectApproveData="selectApproveData"
          :saveType="saveType" :modalTitle="modalTitle" :curId="formData.id">
        </component>
      </div>
    </div>
    <!-- 带入带出 -->
    <Modal v-model="openModalBring" :mask-closable="false" :closable="true" class-name="select--modal" width="50%"
      :title="modalTitle">
      <bringTake ref="bringTake" v-if='openModalBring' :curId='formData.id' @returnInspect='returnInspect' />

      <div slot="footer">
        <Button @click="openModalBring = false">取消</Button>
        <Button type="primary" @click="submitSecurity" :loading="loadingSecurity">提交</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import tabs from "@/components/tabs/index.vue";
import applicationRoom from "./applicationRoom.vue";
import approve from "./approve.vue";
import bringTake from "./bringOrTake.vue"
import patchApprove from "./patchApprove.vue"
import { mapActions } from "vuex";
import { getToken, getTabsParams } from "@/libs/util";
import demoJson from "./json/demoJson.json";
import { getGraphNodeName } from "@/util";

export default {
  components: {
    tabs, applicationRoom, approve, bringTake, patchApprove
  },
  data() {
    return {
      jgrybm: this.$route.query.jgrybm ? this.$route.query.jgrybm : '',
      openModalBring: false,
      component: null,
      showFormCompnent: true,
      modalTitle: '',
      resetMethod: null,
      saveType: 'add',
      tabsMark: '',
      tabsName: '',
      formData: {},
      loadingSecurity: false,
      selectApproveData: [],
      params: {
        renderJson: demoJson,
        disabledNodesId: ["952d9d59-1445-4a82-82df-0ddd31b6b555"],
        nodeClick: this.nodeClick
      }
    }
  },
  mounted() {
    if (this.jgrybm) {
      this.addEvent()
    }
  },
  methods: {
    on_show_table() {
      this.showFormCompnent = true
      this.component = null
      console.log(this.tabsMark, '121221this.tabsMark')

      // this.resetMethod();
    },
    changeTabsMark(e, name) {
      // console.log(e, name, 'e, name')
      this.tabsMark = e;
      this.tabsName = name;
    },
    addEvent() {
      this.showFormCompnent = false
      this.saveType = "add";
      this.formData = {};
      this.component = 'applicationRoom'
      this.modalTitle = "监室调整申请";
    },
    saveForm() {

    },
    cancal() {

    },
    approve(row, rowData, lxType, resetMethod) {
      this.resetMethod = resetMethod
      this.showFormCompnent = false
      this.saveType = "approve";
      this.formData = row;
      this.component = 'approve'
      this.modalTitle = "监室调整审批";
    },
    patchApprove(row, rowData, lxType, resetMethod) {
      this.resetMethod = resetMethod

      console.log(this.$refs.tabs.$refs.adjustmentRoomApprove, this.$refs.tabs.$refs[this.tabsMark + '-grid'][0].batch_select, 'patchApprove --.batch_select()')
      let selectArr = this.$refs.tabs.$refs[this.tabsMark + '-grid'][0].batch_select
      if (selectArr && selectArr.length > 0) {
        this.showFormCompnent = false
        this.saveType = "approve";
        this.formData = row;
        this.component = 'patchApprove'
        this.selectApproveData = selectArr
        this.modalTitle = "监室调整批量审批";
      } else {
        this.$Message.error('请勾选需审批数据！')
        return
      }


    },
    info(row) {
      this.showFormCompnent = false
      this.saveType = "info";
      this.formData = row;
      this.component = 'approve'
      this.modalTitle = "监室调整详情";
    },
    // 带入带出
    bringTake(row, rowData, lxType, resetMethod) {
      this.resetMethod = resetMethod
      this.saveType = "bring";
      this.formData = row;
      // this.component='approve'
      this.modalTitle = "带入带出登记";
      this.openModalBring = true

    },
    submitSecurity() {
      this.$refs.bringTake.submitClick()
    },
    returnInspect(tag) {
      if (tag) {
        this.loadingSecurity = false
        this.on_show_table()
        this.openModalBring = false
        this.$refs.tabs.resetTable()
      } else {
        this.loadingSecurity = false

      }
    },
    //流程图节点点击事件
    nodeClick(node) {
      console.log(node, '流程图节点点击事件')
      console.log(getGraphNodeName(node))
    }
  }
}
</script>