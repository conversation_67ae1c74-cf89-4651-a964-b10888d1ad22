<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
        text-align: left;
      ">
      人员表现鉴定登记
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector @change="selectUser" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
        <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" label-position="left"
          @submit.native.prevent>
          <div class="form-title">
            健康情况
          </div>
          <div class="form-content no-round">
            <Row>
              <Col :span="24">
              <FormItem label="身体状况" prop="healthStzk">
                <RadioGroup v-model="formData.healthStzk">
                  <Radio label="1">健康</Radio>
                  <Radio label="2">不健康</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="外伤情况" prop="healthWsqk">
                <RadioGroup v-model="formData.healthWsqk">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="重大疾病" prop="healthZdjb">
                <RadioGroup v-model="formData.healthZdjb">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="精神状况" prop="healthJszk">
                <RadioGroup v-model="formData.healthJszk">
                  <Radio label="1">良好</Radio>
                  <Radio label="2">不好</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="重大疾病及出所住院原因" prop="healthZdjbjcszyyy">
                <Input v-model="formData.healthZdjbjcszyyy" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写重大疾病及出所住院原因"></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
          <div class="form-title no-round" style="border-top: 0;">
            关押期间表现
          </div>
          <div class="form-content no-round">
            <Row>
              <Col :span="24">
              <FormItem label="所规所纪制度" prop="performanceSgsjzd">
                <RadioGroup v-model="formData.performanceSgsjzd">
                  <Radio label="1">遵守</Radio>
                  <Radio label="2">不遵守</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="一日生活管理" prop="performanceYrshgl">
                <RadioGroup v-model="formData.performanceYrshgl">
                  <Radio label="1">服从</Radio>
                  <Radio label="2">不服从</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="自残自伤行为或倾向" prop="performanceZszcxwhqx">
                <RadioGroup v-model="formData.performanceZszcxwhqx">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="殴打他人行为" prop="performanceOdtrxw">
                <RadioGroup v-model="formData.performanceOdtrxw">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="曾列为重大安全风险情况">
                <Checkbox v-model="formData.performanceZdaqfxqk1" true-value="1" false-value="2">一般</checkBox>
                <Checkbox v-model="formData.performanceZdaqfxqk2" true-value="1" false-value="2">三级</Checkbox>
                <Checkbox v-model="formData.performanceZdaqfxqk3" true-value="1" false-value="2">二级</Checkbox>
                <Checkbox v-model="formData.performanceZdaqfxqk4" true-value="1" false-value="2">一级</Checkbox>
              </FormItem>
              </Col>
            </Row>
          </div>
          <div class="form-title no-round" style="border-top: 0;">
            家属告知联系情况
          </div>
          <div class="form-content no-round">
            <Row>
              <Col :span="24">
              <FormItem label="家属姓名及电话" prop="familyXmjdh">
                <Input v-model="formData.familyXmjdh" placeholder="请填写家属姓名及电话"></Input>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="羁押期间联系家属情况" prop="familyJyqjlxjsqk">
                <RadioGroup v-model="formData.familyJyqjlxjsqk">
                  <Radio label="1">联系过</Radio>
                  <Radio label="2">未联系</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
            </Row>
          </div>
          <div class="form-title no-round" style="border-top: 0;">
            接收信息
          </div>
          <div class="form-content no-round">
            <Row>
              <Col :span="24">
              <FormItem label="接收单位" prop="jsdw">
                <Input v-model="formData.jsdw" placeholder="请填写接收单位"></Input>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="移送因由" prop="ysyy">
                <s-dicgrid placeholder="请选择" v-model="formData.ysyy" ref="dicGrid" dicName="ZD_PERFORMANCE_YSYY" />
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="其他情况" prop="qtqk">
                <Input v-model="formData.qtqk" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写其他情况"></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="handleCancel">返 回</Button>
      <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      formData: {
        familyJyqjlxjsqk: "1",
        familyXmjdh: "",
        healthJszk: "1",
        healthStzk: "1",
        healthWsqk: "1",
        healthZdjb: "1",
        healthZdjbjcszyyy: "",
        id: "",
        jgrybm: "",
        jsdw: "",
        performanceOdtrxw: "1",
        performanceSgsjzd: "1",
        performanceYrshgl: "1",
        performanceZdaqfxqk1: "2",
        performanceZdaqfxqk2: "2",
        performanceZdaqfxqk3: "2",
        performanceZdaqfxqk4: "2",
        performanceZszcxwhqx: "1",
        qtqk: "",
        ysyy: ""
      },
      ruleValidate: {
        // familyXmjdh: [
        //   {
        //     required: true,
        //     message: "该项为必填项",
        //     trigger: "blur",
        //   },
        // ],
        jsdw: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        ysyy: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "change",
          },
        ],
      },
      loading: false,
    };
  },
  watch: {
  },
  created() {
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    selectUser(data) {
      this.formData.jgrybm = data.jgrybm;
      this.formData.id = data.id;
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$router.replace({ name: "behaviorIdentificationKssList" });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        const check = this.formData.jgrybm;
        if (valid && check) {
          this.loading = true;
          this.authPostRequest({
            url: api.create,
            params: this.formData,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("呈批成功");
              this.$refs.form.resetFields();
              this.$router.replace({ name: "behaviorIdentificationKssList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-title {
  text-align: center;
  background: #f5f7fa;
  line-height: 2.2;
  border: 1px solid #d7d7d7;
  border-top-left-radius: 0.2em;
  border-top-right-radius: 0.2em;

  &.no-round {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.form-content {
  flex: 1;
  padding: 15px;
  border: 1px solid #d7d7d7;
  border-top: unset;
  border-bottom-left-radius: 0.2em;
  border-bottom-right-radius: 0.2em;

  &.no-round {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.fixStyle {
  /deep/ .ivu-form-item-label {
    float: none;
    display: inline-block;
  }

  /deep/ .ivu-form-item-content {
    margin-left: 0 !important;
  }

  /deep/ .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }

  /deep/ .el-tabs__content {
    background: #f5f9fc;
    padding: 10px;
    height: 100%;
  }
}
</style>
