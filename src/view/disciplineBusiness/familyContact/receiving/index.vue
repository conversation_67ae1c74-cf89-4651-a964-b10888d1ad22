<template>
  <div style="width: 100%; height: 100%;">
    <!-- 列表页面 -->
    <div class="table-container" v-if="!familyContactContainera" style="display: inline-flex;width: 100%;">
      <s-DataGrid ref="grid" funcMark="sxgltz" :customFunc="true" style="width: 100%;">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':sxgltz:recive')"
                  @click.native="addEvent">收信登记
          </Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <div style="display: inline-flex">
            <Button type="primary" style="margin-right: 10px;"
                    v-if="func.includes(globalAppCode + ':sxgltz:approval') && (row.status === '01' || row.status === '02' || row.status === '03')"
                    @click.native="handleAudit(index, row)">审核
            </Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':sxgltz:zj') && row.status === '04'" style="margin-right: 10px"
                    @click.native="zjEvent(row)">转交
            </Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':sxgltz:info')" style="margin-right: 10px"
                    @click.native="infoEvent(row)">详情
            </Button>
          </div>
        </template>
      </s-DataGrid>
    </div>

    <!-- 收信登记表单页面 -->
    <div class="receiving-registration" v-if="approvalSelectPeople">
      <DetailCardLayout
        ref="detailLayout"
        :left-config="leftConfig"
        :right-cards="rightCards"
        :bottom-actions="bottomActions"
        :show-bottom-actions="true"
        :responsive="true"
        @card-action="handleCardAction"
        @bottom-action="handleBottomAction"
        @collapse-change="handleCollapseChange"
      >
        <!-- 左侧人员信息面板 -->
        <template #left>
          <personnel-selector
            v-model="personInfo.jgrybm"
            title="选择在押人员"
            :enableScan="false"
            :personnelType="ryzt"
            :show-case-info="true"
            placeholder="请选择需要操作的在押人员"
            @change="handleSelectPersonnel"
          />
        </template>

        <!-- 右侧收信登记表单 -->
        <template #receiving-registration>
          <Form ref="formData" :model="formData" :label-width="140" :label-colon="true">
            <!-- 基本信息 -->
            <div class="form-section">
              <div class="section-title">基本信息</div>
              <Row :gutter="16">
                <Col span="12">
                  <FormItem label="送信人姓名" prop="sendMailUser"
                            :rules="[{ trigger: 'blur,change', message: '请填写送信人姓名', required: true }]">
                    <Input v-model="formData.sendMailUser" placeholder="请填写送信人姓名" />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="关系" prop="relation"
                            :rules="[{ trigger: 'blur,change', message: '请选择关系', required: true }]">
                    <s-dicgrid v-model="formData.relation" dicName="ZD_RYGXDM"/>
                  </FormItem>
                </Col>
              </Row>
              <Row :gutter="16">
                <Col span="12">
                  <FormItem label="送信人联系电话" prop="sendContactNumber">
                    <Input v-model="formData.sendContactNumber" placeholder="请填写联系电话" />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="来信日期" prop="sendDate">
                    <el-date-picker
                      format='yyyy-MM-dd HH:mm:ss'
                      value-format='yyyy-MM-dd HH:mm:ss'
                      v-model="formData.sendDate"
                      style="width: 100%;"
                      type="datetime"
                      size='small'
                      placeholder="选择日期时间"/>
                  </FormItem>
                </Col>
              </Row>
              <Row :gutter="16">
                <Col span="12">
                  <FormItem label="信件邮编" prop="mailNo">
                    <Input v-model="formData.mailNo" placeholder="请填写邮编" />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="来信单位" prop="sendPrison">
                    <Input v-model="formData.sendPrison" placeholder="请填写来信单位" />
                  </FormItem>
                </Col>
              </Row>
            </div>

            <!-- 来信地址 - 单独一行 -->
            <div class="form-section">
              <Row :gutter="16">
                <Col span="24">
                  <FormItem label="来信地址" prop="sendAddress">
                    <Input v-model="formData.sendAddress" placeholder="请填写来信地址" />
                  </FormItem>
                </Col>
              </Row>
            </div>

            <!-- 信件内容 - 单独一行 -->
            <div class="form-section">
              <Row :gutter="16">
                <Col span="24">
                  <!-- :rules="[{ trigger: 'blur,change', message: '请上传信件内容', required: true }]" -->
                  <FormItem label="信件内容" prop="mailUrl"
                            >
                    <img-upload v-if="showattUrl"
                                :maxSize="4000000"
                                :serviceMark="serviceMark"
                                :bucketName="bucketName"
                                :defaultList="defaultListsuper"
                                @fileRemove="fileRemove"
                                @fileComplete="fileCompleteSupervisor"/>
                  </FormItem>
                </Col>
              </Row>
            </div>
          </Form>
        </template>
      </DetailCardLayout>
    </div>
    <familyReview v-if="familyContactContainera && saveType === 'approval'" @close="familyContactContainera=false"
                  :jgrybm="jgrybm" :jgryxm="jgryxm" :roomName="roomName" :curId="curId" :saveType="saveType"/>
    <Detail v-if="familyContactContainera && saveType === 'info'" @close="familyContactContainera=false"
            :jgrybm="jgrybm" :jgryxm="jgryxm" :roomName="roomName" :curId="curId" :saveType="saveType" />
    <!-- 转接 -->
    <Modal
      v-model="openModalAdd"
      :mask-closable="false"
      :closable="true"
      class-name="select--modal"
      width="40%"
      :title="modalTitle"
    >
      <div class="addOffice-wrap ">
        <div class="fm-content-wrap " style="padding: 0px;">
          <Form
            ref="addOfficeForm"
            :model="zjData"
            :label-width="140"
            :label-colon="true"
            style="margin:0 16px;height: 100%;"
          >
            <div>
              <Row>
                <Col span="23">
                  <FormItem
                    label="转交时间"
                    prop="passTime"
                    :rules="[  {
                      trigger: 'blur,change',
                      message: '请选择',
                      required: true,
                    },
                  ]"  style="width: 100%">
                    <el-date-picker format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss'
                                    v-model="zjData.passTime" style="width: 100%;"
                                    type="datetime" size='small'
                                    placeholder="选择日期时间"/>
                  </FormItem>
                </Col>
                <Col span="23">
                  <FormItem
                    label="备注"
                    prop="remark"
                    style="width: 100%">
                    <Input v-model="zjData.passRemark" type="textarea" :rows="6" />
                  </FormItem>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </div>
      <div slot="footer">
        <Button @click="openModalAdd = false">取消</Button>
        <Button type="primary" @click="handleZj" :loading="loading" >提交</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {sDataGrid} from 'sd-data-grid'
import {mapActions} from 'vuex'
import {prisonSelect} from 'sd-prison-select'
import {imgUpload} from 'sd-minio-upimg'
import familyReview from './familyReview.vue'
import Detail from "./detail.vue"
import DetailCardLayout from '@/components/bsp-layout/layouts/DetailCardLayout.vue'

export default {
  name: 'familyReceiv',
  components: {
    Detail,
    sDataGrid,
    prisonSelect,
    imgUpload,
    familyReview,
    DetailCardLayout
  },
  data() {
    return {
      ryzt: 'ZS',
      openModalAdd: false,
      showattUrl: true,
      familyContactContainera: false,
      approvalInfo: false,
      saveType: 'add',
      approvalSelectPeople: false,
      ryxxInfo: {},
      selectUseIds: '',
      openModal: false,
      personImg: require("../../../../assets/images/cateringManage/default_Img.svg"),
      formData: {},
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      defaultListsuper: [],
      loading: false,
      showData: false,
      modalTitle: '转交',
      jgrybm: '',
      jgryxm: '',
      curId: '',
      roomName: '',
      zjData: {},

      // 人员信息
      personInfo: {
        jgrybm: '',
        jgryxm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'receiving-registration',
          title: '收信登记',
          icon: 'ios-mail',
          iconColor: '#5b8ff9',
          slot: 'receiving-registration',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '取消',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'submit',
          label: '确认',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ]
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

    // 处理人员选择
    handleSelectPersonnel(personnelData, jgrybm) {
      console.log('选择人员:', personnelData, jgrybm)
      this.personInfo.jgrybm = jgrybm

      // 更新人员信息中的姓名
      if (personnelData && personnelData.xm) {
        this.personInfo.jgryxm = personnelData.xm
      }

      // 更新旧的ryxxInfo以保持兼容性
      this.ryxxInfo = personnelData || {}
    },

    // 处理卡片操作
    handleCardAction(event) {
      console.log('卡片操作:', event)
    },

    // 处理折叠变化
    handleCollapseChange(collapsed) {
      console.log('折叠状态变化:', collapsed)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleClose()
          break
        case 'submit':
          this.handleSubmit()
          break
      }
    },

    // 转交
    zjEvent (row){
      this.openModalAdd = true
      this.curId = row.id
      this.zjData = {}
    },
    handleZj () {
      if(!this.zjData.passTime){
        this.$Message.error('转交时间不能为空')
        return
      }
      this.zjData.id = this.curId
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_pass,
        params: this.zjData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('操作成功')
          this.$refs.grid.query_grid_data(1);
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    // 详情
    infoEvent (row) {
      this.familyContactContainera = true
      this.saveType = 'info'
      this.jgrybm = row.jgrybm
      this.curId = row.id
      this.jgryxm = row.xm
      this.roomName = row.room_name
    },
    // 审核
    handleAudit(index, {id, jgrybm, xm, room_name}) {
      this.familyContactContainera = true
      this.saveType = 'approval'
      this.modalTitle = '收信审核'
      this.jgrybm = jgrybm
      this.curId = id
      this.jgryxm = xm
      this.roomName = room_name
    },
    handleSubmit() {
      // 验证必填的人员信息
      if (!this.personInfo.jgrybm || !this.personInfo.jgryxm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      if (!this.formData.sendMailUser) {
        this.$Message.error('送信人姓名不能为空')
        return
      }
      if (!this.formData.relation) {
        this.$Message.error('关系不能为空')
        return
      }
      // if (!this.formData.mailUrl) {
      //   this.$Message.error('信件内容不能为空')
      //   return
      // }

      // 使用新的人员信息
      this.formData.jgrybm = this.personInfo.jgrybm
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_create,
        params: this.formData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('保存成功')
          // 刷新列表
          if (this.$refs.grid) {
            this.$refs.grid.query_grid_data(1)
          }
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    handleClose () {
      this.approvalSelectPeople = false
      this.familyContactContainera = false
      this.ryxxInfo = {}
      this.formData = {}
      this.openModalAdd = false
      // 清理新的人员信息
      this.personInfo = {
        jgrybm: '',
        jgryxm: ''
      }
    },
    fileRemove() {
    },
    fileCompleteSupervisor(data) {
      this.defaultListsuper = data
      this.$set(this.formData, 'mailUrl', data && data.length > 0 ? JSON.stringify(data) : '')
    },
    addEvent() {
      this.saveType = 'add'
      this.familyContactContainera = true
      this.approvalSelectPeople = true
      this.formData = {}
    },
    useSelect() {
      this.selectUseIds = this.$refs.prisonSelect.checkedUse[0].jgrybm
      this.getPrisonerSelectCompomenOne(this.selectUseIds)
      this.openModal = false
    },
    getPrisonerSelectCompomenOne() {
      let params = {
        jgrybm: this.selectUseIds,
        ryzt: 'ZS'
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code === 0) {
          this.ryxxInfo = resp.data
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    }
  }
}

</script>

<style scoped lang="less">
.table-container {
  padding: 10px 15px;
  height: 95%;
}

.receiving-registration {
  height: 100%;

  /deep/ .main-content > div {
    padding: 0;
  }

  /deep/ .personnel-select-area {
    width: 370px;
  }

  // 表单区域样式
  .form-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8eaec;
    }
  }

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }

  // 文件上传组件样式优化
  /deep/ .upload-box {
    width: 100%;

    .file-item {
      width: 100%;
      max-width: 500px;
      height: auto;
      min-height: 55px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(206, 224, 240, 1);
      border-radius: 4px;
      display: flex;
      padding: 10px;
      margin-bottom: 10px;
      box-sizing: border-box;
    }

    .file-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      min-width: 0;
    }

    .file-text {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;

      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
      }

      .file-size {
        flex-shrink: 0;
        color: #999;
        font-size: 12px;
      }
    }

    .file-img {
      width: 32px;
      height: 32px;
      margin-right: 10px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .space-btw {
      display: flex;
      align-items: center;
      line-height: normal;

      span {
        margin: 0 3px;
      }
    }

    .ivu-btn {
      margin-bottom: 15px;

      .ivu-icon {
        margin-right: 5px;
        position: relative;
        top: 2px;
      }
    }

    .ivu-progress {
      margin-top: 5px;
    }
  }
}

// 转交弹窗样式
.addOffice-wrap {
  width: 100%;
  display: flex;
}
</style>
