<template>
    <div>
        <Card title="领导意见" dis-hover :bordered="false">
            <div class="com-form-container">
                <div class="com-module-layout fm-content-info">
                    <!-- <p class="detail-title">基本信息</p> -->
                    <div class="fm-content-box">
                        <Row>
                            <Col span="4"><span>姓名</span></Col>
                            <Col span="8"><span>{{ formItem.xm }}</span></Col>
                            <Col span="4"><span>身份证号</span></Col>
                            <Col span="8"><span>{{ formItem.sfz }}</span></Col>
                            <Col span="4"><span>联系电话</span></Col>
                            <Col span="8"><span>{{ formItem.contactPhone }}</span></Col>
                            <Col span="4"><span>背景审查时间</span></Col>
                            <Col span="8"><span>{{ formItem.backgroundCheckTime }}</span></Col>
                            <Col span="4"><span>背景审查情况</span></Col>
                            <Col span="20"><span>{{ formItem.backgroundCheckResult }}</span></Col>
                            <Col span="4"><span>食品安全知识和技能培训情况</span></Col>
                            <Col span="20"><span>{{ formItem.foodSafetyTrainResult }}</span></Col>
                            <Col span="4"><span>取得健康证明日期</span></Col>
                            <Col span="8"><span>{{ formItem.healthCertDate }}</span></Col>
                            <Col span="4"><span>食品安全知识和技能培训时间</span></Col>
                            <Col span="8"><span>{{ formItem.foodSafetyTrainTime }}</span></Col>
                            <Col span="4"><span>登记时间</span></Col>
                            <Col span="8"><span>{{ formItem.operTime }}</span></Col>
                            <Col span="4"><span>登记人</span></Col>
                            <Col span="8"><span>{{ formItem.addUserName }}</span></Col>
                            <Col span="4"><span>健康证明材料</span></Col>
                            <Col span="20"><span>
                                <FileUpload
                                    mode="view"
                                    :default-list="fileList"
                                    :width="100"
                                    :height="100"
                                    :auto-upload="true"
                                    :format="['jpg', 'png', 'pdf']"
                                    style="margin-top: 10px;"
                                />
                            </span></Col>
                        </Row>
                    </div>
                </div>
                
                <div class="bsp-base-fotter">
                    <Button @click="on_show_table(false)">返 回</Button>
                </div>
            </div>

        </Card>

    </div>

</template>
<script>
// import { fileUpload } from 'sd-minio-upfile'
import { getUserCache } from '@/libs/util'
import FileUpload from '@/components/bsp-upload/FileUpload.vue'
import step from "@/components/detail/step.vue";
import { Row } from 'view-design';
export default {
    name: "leaderApprove",
    props: {
        msgDate: Object,
    },

    components: {
        FileUpload,
        step
    },
    // directives: { loading },
    data() {

        return {
            openPrison: false,
            orgCodeList: [],
            formItem: {},
            representForm:{},
            // 文件上传
            showFile: true,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            leaderFormItem: {
                status: '04'
            },
            params:{},
            fileList: [],
            idCard: getUserCache.getIdCard(),
            userName: getUserCache.getUserName(),

        };
    },
    created() {
    },
    mounted() {
        this.getInfor();
        this.params.supervise_id = this.msgDate.id
        // console.log(this.userName,'userName111')
    },
    methods: {
        on_show_table() {
            this.$emit('on_show_table')
        },
        getInfor() {
            this.$store.dispatch('authGetRequest', { 
                url: this.$path.management_kitchen_personnel_detail, 
                params: {
                    id: this.msgDate.id
                }
            }).then(resp => {
                // console.log(resp,'详情')
                if (resp.success) {

                    this.formItem = resp.data
                    if (this.formItem.healthCertMaterial) {
                        this.fileList = JSON.parse(this.formItem.healthCertMaterial)
                    }
                } else {
                    this.$Message.error(resp.msg)
                }
            })
        },
    }

};
</script>
