<template>
  <div>
    <div class="">
      <div class="bsp-base-content" v-if="!showData">
        <s-DataGrid ref="grid" funcMark="jspc-hfrylb" :customFunc="true" :params="params" v-if="!showAdd">
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':jspc-hfrylb:add')" @click.native="addEvent('add')">新增</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }" >
              <div style="display: flex;">
                <Button type="primary" v-if="func.includes(appCode + ':jspc-hfrylb:edit')"   @click.native="editEvent(index,row,'edit')" >编辑</Button>&nbsp;&nbsp;
              <Button type="primary" v-if="func.includes(appCode + ':jspc-hfrylb:detail')"   @click.native="detaillEvent(row)" >详情</Button>&nbsp;&nbsp;

              <Button type="error" v-if="func.includes(appCode + ':jspc-hfrylb:delete')" @click.native="delEvent(row)" >删除</Button>
              </div>
          </template>
          <!-- 健康材料证明 -->
           <template slot="slot_health_cert_material" slot-scope="{ row }">
            <span>
              <FileUpload
                                    mode="view"
                                    :default-list="JSON.parse(row.health_cert_material)"
                                    :width="100"
                                    :height="100"
                                    :auto-upload="true"
                                    :format="['jpg', 'png', 'pdf']"
                                    style="margin-top: 10px;"
                                />
            </span>
                </template>
        </s-DataGrid>
      </div>
      <!-- 提讯登记 -->
      <div v-if='showData' class='InquiryTitle'>{{modalTitle}}</div>
      <addForm v-if='(saveType=="add" || saveType == "edit") && showData' :saveType="saveType" @toback='toback' :curId='curData.id' />

      <div v-if="showAdd" style="height: 100%">
            <component :is="component" @on_show_table="on_show_table" :msgDate="msgDate" />
        </div>
	  </div>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import addForm from './addForm.vue'
import detail from './detail.vue'

// import info from './info.vue'
import FileUpload from '@/components/bsp-upload/FileUpload.vue'

export default {
    components: {
	  sDataGrid,
      addForm,
      detail,
      // info,
      FileUpload
	},
    data() {
        return {
             showAdd: false,
            component: null,
            appName: serverConfig.APP_NAME,
            appCode: serverConfig.APP_MARK,
            appId: serverConfig.APP_ID,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            params:{},
            showData: false,
            modalTitle: '',
            saveType: '',
            loadingsignIn: false,
            fileList: [],
            formData: {
              userList: [],
              publishType: '01',
              isFullPolice: '0',
              userId:"",
              userIdName:"",
              orgCode:""
            },
            curData: {},
            ruleValidate: {},
        }
    },
    methods: {
      // 详情
      detaillEvent(row){
        this.component = 'detail'
            this.msgDate = row
            this.showAdd = true
      },
        on_show_table() {
            this.showAdd = false
            this.component = null
            // this.$refs.grid.query_grid_data(1)
        },
        addEvent(type) {
          this.saveType = type
          this.modalTitle = '伙房人员管理--新增'
          this.showData = true
        },
        editEvent(index,row,type){
          this.saveType = type
          this.curData = row
          this.modalTitle = '伙房人员管理-编辑'
          this.showData = true
        },
        delEvent(row) {
            this.$Modal.confirm({
                title: '温馨提示',
                content: '请确认是否删除?',
                onOk: () => {
                    this.$store.dispatch('authGetRequest',{
                        url: this.$path.management_kitchen_personnel_delete,
                        params:{
                            ids: row.id
                        }
                    }).then(res => {
                        if(res.success) {
                            this.$nextTick(() => {
                            this.on_refresh_table()
                            })
                        } else {
                            this.$Message.error('接口操作失败!')
                        }
                    })
                }
            })
        },
        toback() {
          this.showData = false
        },
        on_refresh_table () {
          this.$refs.grid.query_grid_data(1)
        }
    },
    created() {
      // console.log(this.$route.query);
      // if(this.$route.query && this.$route.query.type) {
      //   if(!this.$route.query.bussinId) {
      //     this.$Modal.error({
      //       title: '温馨提示',
      //       content: '业务数据不能为空!'
      //     })
      //     return;
      //   }
        
      //   this.saveType = 'handle'
      //   this.curData = {
      //     id: this.$route.query.bussinId,
      //     publish_type: this.$route.query.type
      //   }
      //   this.showData = true
      // }
    }
} 
</script>

<style lang="less" scoped>
.com-sub-title{
  border-left: 4px solid #3491fa;
  padding-left: 8px;
  font-size: 16px;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;
}
/deep/.ivu-modal-body {
  height: 500px !important;
  overflow: auto;
}
.ivu-form-item{
    margin-bottom: 10px !important;
  }
 .InquiryTitle{
    border-bottom:1px solid #dcdee2;
    padding:16px;
 }
 .ivu-table-cell-slot button{
  margin:5px 0;
 }
</style>