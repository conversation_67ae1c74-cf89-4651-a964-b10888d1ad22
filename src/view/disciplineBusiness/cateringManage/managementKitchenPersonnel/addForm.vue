<template>
  <div>
    <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="140" :label-colon="true" style="margin-top: 15px;">
        <Row>
            <Col span="12">
                <FormItem label="姓名" prop="xm" style="width: 100%;" :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]">
                    <Input v-model="formData.xm" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col><Col span="12">
                <FormItem label="身份证号" prop="sfz" style="width: 100%;" :rules="[{ pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,trigger: 'blur', message: '请输入正确的身份证号', required: true }]">
                    <Input v-model="formData.sfz" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="联系电话" prop="contactPhone" style="width: 100%;"  :rules="[{ pattern: /^1[3-9]\d{9}$/, trigger: 'blur', message: '请输入正确的电话号码', required: true }]">
                    <Input v-model="formData.contactPhone" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="背景审查时间" prop="backgroundCheckTime" style="width: 100%;">
                   <el-date-picker style="width: 778px;" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"  v-model="formData.backgroundCheckTime" size="small"  placeholder="请选择"  />
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem label="背景审查情况" prop="backgroundCheckResult" style="width: 100%;">
                    <Input v-model="formData.backgroundCheckResult" type="textarea" :autosize="{minRows: 5,maxRows: 6}"></Input>
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="取得健康证明日期" prop="healthCertDate" style="width: 100%;" >
                    <el-date-picker style="width: 778px;" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"  v-model="formData.healthCertDate" size="small"  placeholder="请选择"  />
                </FormItem>
            </Col>
            <Col span="12">
                <FormItem label="食品安全知识和技能培训时间" prop="foodSafetyTrainTime" style="width: 100%;">
                    <el-date-picker style="width: 778px;" type="datetime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"  v-model="formData.foodSafetyTrainTime" size="small"  placeholder="请选择"  />
                </FormItem>
            </Col>
           
            <Col span="24">
                <!-- healthCertMaterial -->
                <FormItem label="健康证明材料"  style="width: 100%;" >
                    <!-- <file-upload :key="formData.healthCertMaterial" :serviceMark="serviceMark"
                    :bucketName="bucketName" :defaultList="fileList" :beforeUpload="() => true"
                    @fileComplete="fileCompleteFile" /> -->
                    <FileUpload
                        :mode="mode"
                        :default-list="fileList"
                        :width="100"
                        :height="100"
                        :auto-upload="true"
                        @on-success="handleSuccess"
                        @on-remove="handleRemove"
                        :format="['jpg', 'png', 'pdf','xls','xlsx', 'docx', 'mp4']"
                        style="margin-top: 10px;"
                    />
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem label="食品安全知识和技能培训情况" prop="foodSafetyTrainResult" style="width: 100%;">
                    <Input v-model="formData.foodSafetyTrainResult" type="textarea" :autosize="{minRows: 5,maxRows: 6}"></Input>
                </FormItem>
            </Col>
        </Row>
    </Form>

    <div class='bsp-base-fotter' style="text-algin: center;">
        <Button @click="toback">取消</Button>
        <Button type="primary" @click="submitJb" :loading="loadingsignIn">确认</Button>
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/bsp-upload/FileUpload.vue'

export default {  
    components: {
        FileUpload,
    },
    data(){
        return {
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            formData: {
                healthCertMaterial: ''
            },
            fileList: [],
            ruleValidate: {},
            loadingsignIn: false,
            mode: 'edit'
        }
    },
    props: {
        curId:String,
        saveType:String
    },
    watch: {
        curId:{
            immediate: true,
            handler(value) {
                if(value) {
                    // console.log(value,'curId');
                    if(this.saveType !== 'add') {
                        this.getData(value)
                    }
                }
            },
            deep: true
        }
    },
    methods: {
        fileCompleteFile(file) {
          this.fileList = file
          this.$set(this.formData,'healthCertMaterial',JSON.stringify(file))
        //   console.log(file,'file');
          let str = file.map(i => i.fileName).join(',')
        //   console.log(str);
          this.$set(this.formData,'knowledgeName',str)
        },
        // 附件上传成功回调
        handleSuccess(file, res, uploadList) {
            // console.log(file, res, uploadList,'file, res, uploadList');
            this.fileList = uploadList
            this.$set(this.formData,'healthCertMaterial',JSON.stringify(uploadList))
            let str = uploadList.map(i => i.name).join(',')
            this.$set(this.formData,'knowledgeName',str)
        },
        // 附件删除回调
        handleRemove(file, fileList) {
            this.$set(this.formData,'healthCertMaterial',JSON.stringify(fileList))
            let str = fileList.map(i => i.name).join(',')
            this.$set(this.formData,'knowledgeName',str)
        },
        submitJb() {
        //   console.log(this.saveType,'saveType');
            
        //   console.log(this.formData,'this.formData');
          
          this.$refs.formData.validate(valid => {
            if(valid) {
            //   console.log(this.formData,'提交数据');
              this.loadingsignIn = true
              this.saveData()
            } else {
              this.$Message.error('请填写完整内容!')
            }
          })
        },
        saveData() {
          let url = ''
          if(this.saveType == 'add') {
            url = this.$path.management_kitchen_personnel_create
          } else if(this.saveType == 'edit') {
            url = this.$path.management_kitchen_personnel_edit
          }

          this.$store.dispatch('authPostRequest',{
            url,
            params: this.formData
          }).then(res => {
            if(res.success) {
                if(this.saveType == 'add') {
                        this.$Message.success('新增成功!')
                } else if(this.saveType == 'edit') {
                        this.$Message.success('编辑成功!')
                }

              this.loadingsignIn = false
              this.$nextTick(() => {
                this.toback()
              })
            } else {
              this.loadingsignIn = false
              this.$Message.error(res.msg || '接口操作失败!')
            }
          })
        },
        getData(id) {
            this.$store.dispatch('authGetRequest',{
                url: this.$path.management_kitchen_personnel_detail,
                params: {
                    id
                }
            }).then(res => {
                if(res.success) {
                    this.formData = res.data
                    this.fileList = res.data.healthCertMaterial ? JSON.parse(res.data.healthCertMaterial) : []
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        toback() {
            this.$emit('toback')
        }
    },
    created() {
        if(this.saveType && this.saveType == 'add'){
            // console.log('11111111');
            
            this.formData = {}
            this.fileList = []
        }
    }
}
</script>

<style>

</style>