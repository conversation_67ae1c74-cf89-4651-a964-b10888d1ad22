<template>
    <div>
        <div class="fm-content-info">
            <div class="fm-content-box">
                <p class="fm-content-info-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
                </p>
                <Row>
                    <Col span="3" class="col-title"><span>检查时间</span></Col>
                    <Col span="5"><span>{{ formValidate.checkTime }}</span></Col>
                    <Col span="3"><span>带队领导</span></Col>
                    <Col span="5"><span>{{ formValidate.leaderUserName }}</span></Col>
                    <Col span="3"><span>参加民辅警</span></Col>
                    <Col span="5"><span>{{ formValidate.involvementUserName }}</span></Col>
                </Row>
                <Row>
                    <Col span="3" class="col-title"><span>是否岗位协同</span></Col>
                    <Col span="5"><span>{{ formValidate.isJoin == "1" ? '是' : '否' }}</span></Col>
                    <Col span="3"><span>岗位协同</span></Col>
                    <Col span="5"><span>{{ filtrateJoinGw(formValidate.joinGw) }}</span></Col>
                    <Col span="3"><span>推送内容</span></Col>
                    <Col span="5"><span>{{ formValidate.remarks }}</span></Col>
                </Row>
                <Row>
                    <Col span="3" class="col-title"><span>登记人</span></Col>
                    <Col span="5"><span>{{ formValidate.operatorXm }}</span></Col>
                    <Col span="3"><span>登记时间</span></Col>
                    <Col span="5"><span>{{ formValidate.operatorTime }}</span></Col>
                    <Col span="3"><span>数据来源</span></Col>
                    <Col span="5"><span>{{sjlyList.find(item => item.code === formValidate.dataSources)?.name}}</span>
                    </Col>
                </Row>
                <p class="fm-content-info-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />检查明细
                </p>
                <Row>
                    <Col span="3" class="col-title"><span>检查监室</span></Col>
                    <Col span="21"><span>{{ formValidate.checkRoomName }}</span></Col>
                </Row>
                <Table :columns="columns" :data="dataTable" border style="margin-top: 10px;">
                    <template slot-scope="{ row, index }" slot="qcqy">
                        {{qcqyList.find(item => item.code === row.qcqy)?.name}}
                    </template>
                    <template slot-scope="{ row, index }" slot="jcjg">
                        {{ row.jcjg == '0' ? '正常' : '异常' }}
                    </template>
                </Table>
            </div>
        </div>
        <div class="bsp-base-fotter">
            <Button @click="onCancel">取 消</Button>

        </div>
    </div>
</template>

<script>

export default {
    name: 'detailSafetyCheck',
    props: {
        rowData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            formValidate: {},
            dataTable: [],
            joinGwList: [],
            sjlyList: [],
            qcqyList: [],
            columns: [
                {
                    type: 'index',
                    width: 80,
                    align: 'center',
                    title: '序号'
                },
                {
                    title: '清查区域',
                    slot: 'qcqy',
                    align: 'center',
                },

                {
                    title: '检查人',
                    key: 'jcrName',
                    align: 'center',
                },
                {
                    title: '检查结果',
                    slot: 'jcjg',
                    align: 'center',
                },
                {
                    title: '情况说明',
                    key: 'qksm',
                    align: 'center',
                },
            ],
        }
    },
    created() {
        this.getDetail();
    },
    mounted() {
        this.handleGetZD_RCQJ_QCWY();
        this.handleGetZD_RCQJGWXT();
        this.handleGetZD_DATA_SOURCES();
    },
    methods: {
        onCancel() {
            this.$emit('on_show_table');
        },
        getDetail() {
            let params = {};
            if (this.rowData.pcId) {
                params = {
                    id: this.rowData.id,
                    pcId: this.rowData.pcId
                };
            } else {
                params = {
                    id: this.rowData.id
                };
            }
            this.$store.dispatch('authGetRequest', { url: this.$path.pam_getCleanDaily, params: params }).then(res => {
                if (res.success) {
                    this.formValidate = res.data;
                    this.dataTable = res.data.cleanDailyDetails || [];
                } else {
                    this.$Message.error(res.message);
                }
            })
        },
        handleGetZD_RCQJGWXT() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_RCQJGWXT.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.joinGwList = scales();
            });
        },
        handleGetZD_RCQJ_QCWY() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_RCQJ_QCWY.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.qcqyList = scales();
            });
        },
        handleGetZD_DATA_SOURCES() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_DATA_SOURCES.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.sjlyList = scales();
            });
        },
        filtrateJoinGw(value) {
            if (this.formValidate.isJoin == '1' && value) {
                return value;
            } else {
                return '';
            }

        },
    }
}


</script>
