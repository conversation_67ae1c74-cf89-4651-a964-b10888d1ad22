<template>
  <div class="safety-checks-container">
    <div class="table-container" v-if="tableContainer">
          <s-DataGrid ref="grid1" funcMark="yrshzdlb" :customFunc="true" :params="params">
            <template slot="customHeadFunc" slot-scope="{ func }">
              <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':yrshzdlb:add')"
                @click.native="handleAddSafetyCheck('add')">新增</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':yrshzdlb:approval') && row.status == '01'"
                @click.native="handleApprovalSp(index, row)">审批</Button>
              <Button type="primary" style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':yrshzdlb:detail')"
                @click.native="handledetail(index, row)">详情</Button>
            </template>
          </s-DataGrid>
    </div>
    <div v-if="addOneDayTem">
      <div class="add-One-Day-Tem">
        <Form ref="formDayTem" :model="formValidate" :label-width="120" :rules="ruleValidate">
          <div style="margin-top: 20px;">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
            </p>
            <Row>
              <Col span="23">
              <FormItem label="制度名称" prop="name">
                <Input v-model="formValidate.name" placeholder="请输入制度名称" style="width: 300px;"></Input>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="23">
              <FormItem label="周期设置" prop="cycleSetting">
                <RadioGroup v-model="formValidate.cycleSetting" @on-change="changeCycle">
                  <Radio v-for="item in daysList" :label="item.code" :key="item.code">{{ item.name }}</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
            </Row>
            <Row v-if="changeCycleShow">
              <Col span="12" style="margin-left: 120px;">
              <CheckboxGroup v-model="formValidate.cycleConfigArr" @on-change="changeWeeks">
                <Checkbox v-for="item in weekList" :label="item.code" :disabled="chioceDisabled" :key="item.code">{{
                  item.name }}
                </Checkbox>
              </CheckboxGroup>
              </Col>
            </Row>
            <Row v-if="changeCycleShow" style="margin-left: 120px;padding: 10px 0;">
              <Col span="12">
              <DatePicker @on-change="changeChiocedDateTimeRange" v-model="formValidate.customizeTimeArr"
                type="datetimerange" format="yyyy-MM-dd HH:mm:ss" :disabled="timeDisabled" style="width: 350px;"
                placeholder="请选择自定义时间" />
              </Col>
            </Row>
            <Row>
              <Col span="23">
              <FormItem label="监室" prop="lifeRoomsArr"
                :rules="{ required: true, message: '请选择监室', trigger: 'blur,change' }">
                <Input v-model="formValidate.lifeRoomsArr" @on-focus="openModal = true" placeholder="请选择监室"></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
      <div>
        <Form ref="formTemAdd" :model="formValidate1" :label-width="120" :rules="ruleValidate1">
          <div style="margin-top: 20px;">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />事务信息
            </p>
            <Row>
              <Col span="8">
              <FormItem label="制度模板" prop="templateName">
                <Select v-model="formValidate1.templateName" placeholder="请输入制度模板名称" @on-change="changeOneDayTem">
                  <Option v-for="item in lifeTemplateList" :value="item.id" :key="item.id">{{ item.templateName }}
                  </Option>
                </Select>
              </FormItem>
              </Col>
              <Col style="margin-left: 100px; margin-top: 2px;">
              <Button type="primary" @click.native="handleOneDayTem">制度模板</Button>
              </Col>
              <Row>
                <Col span="23">
                <template v-for="(item, index) in formDynamicDetail.lifeEvents">
                  <div class="form-list" v-if="item.status">
                    <div class="index-num">{{ index + 1 }}</div>
                    <div class="form-content">
                      <div class="header-form">
                        <div class="time-chioce">
                          <FormItem v-if="item.status" :label="'时间范围'">
                            <span>{{ item.startTime }}</span>
                          </FormItem>
                          <div v-if="item.status" style="margin-top: -29px;margin-left: 10px;">-
                          </div>
                          <FormItem style="margin-left: -110px;" v-if="item.status">
                            <span>{{ item.endTime }} {{ `(${item.endTimeSpan == 0 ? "当日" : "次日"})`
                            }}</span>
                          </FormItem>
                        </div>
                        <div class="switch-container-detail" v-if="item.isEnabledVoice == 1">
                          <Icon type="md-megaphone" />
                          <span>语音播报</span>
                        </div>
                      </div>
                      <div class="select-template">
                        <FormItem v-if="item.status" :label="'事务选择'">
                          <span>{{ item.eventName }}</span>
                        </FormItem>
                      </div>
                    </div>
                  </div>
                </template>
                </Col>
              </Row>
            </Row>
          </div>
        </Form>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleReset('form')" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSubmit('form')" :loading="loading">确认</Button>
      </div>
    </div>
    <!-- 一日生活制度的审批 -->
    <div v-if="approvalContainer">
      <Form ref="formValidate" :model="formValidate" :label-width="150" style="width: 100%;">
        <div class="fm-content-form">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
          </p>
          <Form ref="formData" :model="approvalContainerInfo" inline>
            <div class="fm-content-box">
              <Row>
                <Col span="3" class="col-title"><span>制度名称</span></Col>
                <Col span="9"><span>
                  {{ approvalContainerInfo.name }}
                </span>
                </Col>
                <Col span="3" class="col-title"><span>周期设置</span></Col>
                <Col span="9" v-if="approvalContainerInfo.cycleSetting == '02'"><span>
                  {{ approvalContainerInfo.customizeTime }}
                </span></Col>
                <Col span="9"
                  v-else-if="approvalContainerInfo.cycleSetting == '01' || approvalContainerInfo.cycleSetting == '03'">
                <span>
                  {{ approvalContainerInfo.cycleSettingName }}
                </span></Col>
              </Row>
              <Row>
                <Col span="3" class="col-title"><span>监室</span></Col>
                <Col span="21"><span>
                  {{ lifeRoomsName }}
                </span>
                </Col>
              </Row>
              <Row>
                <Col span="3" class="col-title"><span>创建人</span></Col>
                <Col span="9"><span>
                  {{ approvalContainerInfo.addUserName }}
                </span>
                </Col>
                <Col span="3" class="col-title"><span>创建时间</span></Col>
                <Col span="9"><span>
                  {{ approvalContainerInfo.addTime }}
                </span></Col>
              </Row>
            </div>
          </Form>
        </div>
      </Form>
      <Form ref="formValidate" :model="formValidate" :label-width="150" style="width: 100%;">
        <div class="fm-content-form">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />事务信息
          </p>
          <Form ref="formData" inline>
            <div class="fm-content-box">
              <Row>
                <Col span="24">
                <div style="display: flex;flex-flow: wrap">
                  <template v-for="(item, index) in formDynamicDetail.lifeEvents">
                    <div class="form-list-approval" :key="index">
                      <div class="index-num-approval">{{ index + 1 }}</div>
                      <div class="form-content-approval">
                        <div class="header-form-approval" style="display: flex;flex-flow: column;align-items: center;">
                          <div class="time-chioce-approval">
                            <span>{{ item.startTime }}</span>
                            <span style="margin-top: -29px;margin-left: 10px;">-
                            </span>
                            <span>{{ item.endTime }} {{ `(${item.endTimeSpan == 0 ? "当日" : "次日"})`
                            }}</span>
                          </div>
                          <div class="select-template-approval">
                            <span>{{ item.eventName }}</span>
                          </div>
                        </div>
                      </div>
                      <template v-if="index !== formDynamicDetail.lifeEvents.length - 1">
                        <div class="svg-arrow">
                          <img src="../../../../assets/images/longarrow.svg" alt="">
                        </div>
                      </template>
                      <!-- 最后一项显示占位符或空 -->
                      <template v-else>
                        <!-- 留空或自定义占位内容 -->
                      </template>

                      <div class="switch-container-detail-approval" v-if="item.isEnabledVoice == 1">
                        <Icon type="md-megaphone" />
                      </div>
                    </div>
                  </template>
                </div>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Form>
      <Form ref="formValidate5" :model="formValidate5" :label-width="150" style="width: 100%;"
        v-if="approvalDetailType == 'approvaldo'">
        <div class="fm-content-form">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />一日生活制度审批
          </p>
          <Row style="padding: 10px;">
            <Col span="24">
            <FormItem label="审批结果" prop="status" :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
              <RadioGroup v-model="formValidate5.status">
                <Radio label="02">通过</Radio>
                <Radio label="03">不通过</Radio>
              </RadioGroup>
            </FormItem>
            </Col>
            <!-- /pam/daily/life/approval-update -->
            <Col span="24">
            <FormItem label="审批意见" prop="approvalComments"
              :rules="{ required: true, message: '请输入审批意见', trigger: 'blur' }">
              <Input v-model="formValidate5.approvalComments" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                placeholder="请输入审批意见"></Input>
            </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
      <div v-if="approvalDetailType == 'detail'" style="margin-bottom: 40px;">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />审批结果
        </p>
        <Form ref="formData" inline>
          <div class="fm-content-box">
            <Row>
              <Col span="3" class="col-title"><span>审批结果</span></Col>
              <Col span="21" v-if="approvalContainerInfo.approvalResult == 5">同意<span>
              </span>

              </Col>
              <Col span="21" v-if="!approvalContainerInfo.approvalResult">
              <span></span>
              </Col>
              <Col span="21" v-if="approvalContainerInfo.approvalResult == 6">
              <span>不同意</span>
              </Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>审批意见</span></Col>
              <Col span="21"><span>
                {{ approvalContainerInfo.approvalComments }}
              </span>
              </Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>审批人</span></Col>
              <Col span="9"><span>
                {{ approvalContainerInfo.approverXm }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>审批时间</span></Col>
              <Col span="9"><span>
                {{ approvalContainerInfo.approverTime }}
              </span></Col>
            </Row>
          </div>
        </Form>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleResetApproval('formValidate5')" style="margin-right: 10px;">取消</Button>
        <Button v-if="approvalDetailType == 'approvaldo'" type="primary"
          @click="handleSubmitApproval('formValidate5')">确认</Button>
      </div>
    </div>
    <!-- 一日生活制度的详情页面 -->
    <div v-if="approvalDetailCOntainer">
      <div class="fm-content-info">
        <Form ref="formData" :model="approvalContainerInfo" inline>
          <div class="fm-content-box">
            <Row>
              <Col span="3" class="col-title"><span>模板名称</span></Col>
              <Col span="9"><span>
                {{ approvalContainerInfo.name }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>周期设置</span></Col>
              <Col span="9" v-if="approvalContainerInfo.cycleSetting == '02'"><span>
                {{ approvalContainerInfo.customizeTime }}
              </span></Col>
              <Col span="9"
                v-else-if="approvalContainerInfo.cycleSetting == '01' || approvalContainerInfo.cycleSetting == '03'">
              <span>
                {{ approvalContainerInfo.cycleSettingName }}
              </span></Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>监室</span></Col>
              <Col span="21"><span>
                {{ lifeRoomsName }}
              </span>
              </Col>
            </Row>
            <Row>
              <Col span="3" class="col-title"><span>创建人</span></Col>
              <Col span="9"><span>
                {{ approvalContainerInfo.addUserName }}
              </span>
              </Col>
              <Col span="3" class="col-title"><span>创建时间</span></Col>
              <Col span="9"><span>
                {{ approvalContainerInfo.addTime }}
              </span></Col>
            </Row>
          </div>
        </Form>


        <Form ref="formTemAdd" :model="formValidate1" :label-width="120" :rules="ruleValidate1">
          <div style="margin-top: 20px;">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />事务信息
            </p>
            <Row>
              <Col span="24">
              <div style="display: flex;flex-flow: wrap">
                <template v-for="(item, index) in formDynamicDetail.lifeEvents">
                  <div class="form-list-approval">
                    <div class="index-num-approval" style="top: 15px;">{{ index + 1 }}</div>
                    <div class="form-content-approval">
                      <div class="header-form-approval" style="display: flex;flex-flow: column;align-items: center;">
                        <div class="time-chioce-approval">
                          <span>{{ item.startTime }}</span>
                          <span style="margin-top: -29px;margin-left: 10px;">-
                          </span>
                          <span>{{ item.endTime }} {{ `(${item.endTimeSpan == 0 ? "当日" : "次日"})`
                          }}</span>
                        </div>
                        <div class="select-template-approval">
                          <span>{{ item.eventName }}</span>
                        </div>
                      </div>
                    </div>
                    <template v-if="index !== formDynamicDetail.lifeEvents.length - 1">
                      <div class="svg-arrow">
                        <img src="../../../../assets/images/longarrow.svg" alt="">
                      </div>
                    </template>
                    <!-- 最后一项显示占位符或空 -->
                    <template v-else>
                      <!-- 留空或自定义占位内容 -->
                    </template>

                    <div class="switch-container-detail-approval" v-if="item.isEnabledVoice == 1">
                      <Icon type="md-megaphone" />
                    </div>
                  </div>
                </template>
              </div>
              </Col>
            </Row>

          </div>
        </Form>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleResetApprovalDetailCOntainer" style="margin-right: 10px;">取消</Button>
      </div>
    </div>
    <Modal v-model="modalAddType" width="1200" title="新建制度模板">
      <div class="modal-scroll-content">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
        </p>
        <!-- 表单内容 -->
        <Form ref="formValidate2" :model="formValidate2" :label-width="120" style="margin-top: 10px;">
          <Row>
            <Col span="8">
            <FormItem label="监室事务" prop="templateName"
              :rules="{ required: true, message: '监室事务名称不能为空', trigger: 'blur' }">
              <Input v-model="formValidate2.templateName" placeholder="监室事务名称" style="width: 300px;"></Input>
            </FormItem>
            </Col>
          </Row>
        </Form>
        <Form ref="form" :model="formDynamic" :label-width="120">
          <div style="margin-top: 20px;">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />事务信息
            </p>
            <template v-for="(item, index) in formDynamic.lifeTemplateEvents">
              <div class="form-list" v-if="item.status">
                <div class="index-num">{{ index + 1 }}</div>
                <div class="form-content">
                  <div class="header-form">
                    <div class="time-chioce">
                      <FormItem v-if="item.status" :label="'时间范围'" :prop="'lifeTemplateEvents.' + index + '.startTime'"
                        :rules="dynamicStartTimeRules">
                        <TimePicker v-model="item.startTime" format="HH:mm" style="width: 80px" />
                      </FormItem>
                      <!-- :rules="{ required: true, type: 'date', message: '请选择时间', trigger: 'blur' }" -->
                      <div v-if="item.status" style="margin-top: -20px;margin-left: 10px;">至
                      </div>
                      <FormItem style="margin-left: -110px;" v-if="item.status"
                        :prop="'lifeTemplateEvents.' + index + '.endTimeSpan'"
                        :rules="{ required: true, type: 'number', message: '请选择', trigger: 'change' }" :label="''">
                        <Select v-model="item.endTimeSpan" style="width:80px">
                          <Option :value="0">当日</Option>
                          <Option :value="1">次日</Option>
                        </Select>
                      </FormItem>
                      <FormItem style="margin-left: -110px;" v-if="item.status"
                        :prop="'lifeTemplateEvents.' + index + '.endTime'" :rules="dynamicEndTimeRules" :label="''">
                        <TimePicker v-model="item.endTime" format="HH:mm" style="width: 80px" />
                      </FormItem>
                    </div>
                    <div class="switch-container">
                      <FormItem style="margin-right: 120px;" v-if="item.status"
                        :prop="'lifeTemplateEvents.' + index + '.isEnabledVoice'"
                        :rules="{ required: true, type: 'number', message: '语音播报不能为空', trigger: 'change' }"
                        :label="'语音播报'">
                        <i-Switch :true-value="1" :false-value="0" v-model="item.isEnabledVoice" />
                      </FormItem>
                    </div>
                  </div>
                  <div class="select-template">
                    <FormItem v-if="item.status" :label="'事务选择'" :prop="'lifeTemplateEvents.' + index + '.eventId'"
                      :rules="{ required: true, type: 'array', message: '事务选择不能为空', trigger: 'change' }">
                      <Select v-model="item.eventId" @on-change="(val) => handleMultiChange(val, index)"
                        :label-in-value="true" multiple placeholder="请选择事务项">
                        <Option :value="item.id" v-for="item in lifeDictEventList" :key="item.id">{{
                          item.eventName }}</Option>
                      </Select>
                    </FormItem>
                  </div>
                </div>
                <div class="del-icon" v-if="index >= 1">
                  <i class="el-icon-delete" @click="handleDeleteItem(index)"></i>
                </div>
              </div>
            </template>
          </div>
          <FormItem>
            <Row>
              <Col span="12">
              <Button type="dashed" long @click="handleAddLifeItem" icon="md-add">添加</Button>
              </Col>
            </Row>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button @click="handleCancel">取消</Button>
        <Button type="primary" @click="hanndleSubmit">取消</Button>
      </template>
    </Modal>
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
      title="监室列表">
      <div class="select-use">
        <roomSelect v-if="openModal" ref="prisonSelect" ryzt="ALL" :isMultiple="true" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="openModal = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import { roomSelect } from 'sd-room-select'
export default {
  name: "oneDay",
  data() {
    return {
      tableContainer: true,
      addOneDayTem: false,
      approvalContainer: false,
      approvalDetailCOntainer: false,
      openModal: false,
      loading: false,
      name: "",
      params: {},
      formValidate: {
        name: "",
        cycleSetting: "",
        cycleConfigArr: [],
        customizeTimeArr: [],
        lifeRoomsArr: "",
        lifeRoomsList: [],
        lifeRooms: []
      },
      formValidate1: {
        templateName: ""
      },
      formValidate2: {
        templateName: ""
      },
      formValidate5: {
        status: "02",
        approvalComments: ""
      },
      ruleValidate1: {
        templateName: [{ required: true, message: '制度模板名称不能为空', trigger: 'change' }],
      },
      ruleValidate: {
        name: [{ required: true, message: '监室事务名称不能为空', trigger: 'blur' }],
        cycleSetting: [{ required: true, message: '请选择周期设置', trigger: 'change' }],
      },
      daysList: [],
      weekList: [{ name: '星期天', code: 1 }, { name: '星期一', code: 2 }, { name: '星期二', code: 3 }, { name: '星期三', code: 4 }, { name: '星期四', code: 5 }, { name: '星期五', code: 6 }, { name: '星期六', code: 7 }],
      changeCycleShow: false,
      selectWeekChioced: [],
      chioceDisabled: false,
      timeDisabled: false,
      cityList: [],
      lifeTemplateList: [],
      formDynamicDetail: {
        lifeEvents: [

        ]
      },
      id: "",
      modalAddType: false,
      formDynamic: {
        lifeTemplateEvents: [
          {
            startTime: "",
            endTimeSpan: 0,
            isEnabledVoice: 0,
            eventId: [],
            eventName: [],
            sort: 1,
            status: 1
          }
        ]
      },
      dynamicStartTimeRules: [
        { required: true, validator: this.validateStartTime, trigger: 'change' } // 使用blur触发验证
      ],
      dynamicEndTimeRules: [
        { required: true, validator: this.validateEndTime, trigger: 'change' } // 使用blur触发验证
      ],
      lifeDictEventList: [],
      roomList: [],
      id: "",
      approvalDetailInfo: {
        name: ""
      },
      lifeRoomsName: "",
      approvalContainerInfo: {

      },
      approvalDetailType: "",
      singleRoom: "",
      searchForm: {
        jsh: '',
        pageSize: 100,
        pageNo: 1,
        mainAssistantManager: false,
        mainAssistantManagerRoom: '0'

        // airMonitoringRoom:''//[]
      },
      total: 0,
      roomData: [],
      single: []
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleGetYRSHZD() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_YRSHZD_ZQ.js" }).then(res => {
        let days = eval('(' + res + ')')
        this.daysList = days()
      })
    },
    selectRoom(item) {
      console.error(item);

      this.$set(this, 'singleRoom', item.roomCode)
      // this.$emit('transferRoom',item)
    },
    getRoom() {
      this.$store.dispatch('authPostRequest', {
        url: this.$path.acp_areaPrisonRoom_page,
        params: this.searchForm
      }).then(res => {
        if (res.success) {
          this.roomList = res.data.list ? res.data.list : []
          // this.total = res.data.total ? res.data.total : 0
          // this.roomList = res.data
        }
      })
    },
    getNo(pageNo) {
      this.$set(this.searchForm, 'pageNo', pageNo)
      this.getRoom()
    },
    getSize(pageSize) {
      this.$set(this.searchForm, 'pageSize', pageSize)
      this.getRoom()
    },
    changeCycle(data) {
      if (data == '02') {
        this.changeCycleShow = true
      } else {
        this.changeCycleShow = false
      }
    },
    changeWeeks(data) {
      this.selectWeekChioced = data
      if (data.length > 0) {
        this.timeDisabled = true
      } else {
        this.timeDisabled = false
      }
    },
    // 使劲选择
    changeChiocedDateTimeRange(data) {
      data.forEach(item => {
        if (item) {
          this.chioceDisabled = true
        } else {
          this.chioceDisabled = false
        }
      })
    },
    handleAddSafetyCheck() {
      this.tableContainer = false
      this.addOneDayTem = true
      // this.formDynamicDetail.lifeEvents = []
      // this.formValidate = {}
    },
    handleOneDayTem() {
      this.modalAddType = true
    },
    validateStartTime(rule, value, callback) {
      console.error(value, '[value]');
      if (!value) {
        callback(new Error('请选择时间'));
      } else {
        // 验证通过时必须调用callback()
        callback(); // 这是解决图标转圈问题的关键
      }
    },
    validateEndTime(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择时间'));
      } else {
        // 验证通过时必须调用callback()
        callback(); // 这是解决图标转圈问题的关键
      }
    },
    hanndleGetlifeDictEventList() {
      this.authPostRequest({
        url: this.$path.lifeDictEvent_get_list, params: { cityCode: "", eventName: "", eventStatus: "", orgCode: "", regCode: "" }
      }).then(res => {
        if (res.success) {
          this.lifeDictEventList = res.data
        }
      })
    },
    handleAddLifeItem() {
      this.index++;
      this.formDynamic.lifeTemplateEvents.push({
        startTime: "",
        endTimeSpan: "",
        isEnabledVoice: false,
        eventId: [],
        eventName: [],
        sort: this.index,
        status: 1
      });
    },
    handleDeleteItem(index) {
      if (this.formDynamic.lifeTemplateEvents.length > 1) {
        this.formDynamic.lifeTemplateEvents[index].status = 0;
        this.formDynamic.lifeTemplateEvents.splice(index, 1)
      }
    },
    handleGetlifeTemplateList() {
      this.authPostRequest({ url: this.$path.lifeTemplate_lifeTemplate_list, params: { cityCode: "", orgCode: "", regCode: "", templateName: "" } }).then(res => {
        if (res.success) {
          this.lifeTemplateList = res.data
        }
      })
    },
    // 点击下拉框选择制度信息
    changeOneDayTem(data) {
      this.loading = true
      this.authGetRequest({ url: this.$path.lifeTemplate_by_template_id, params: { templateId: data } }).then(res => {
        if (res.success) {
          if (res.data) {
            res.data.forEach(item => {
              item.status = true
              item.eventName = item.eventName
            })
            this.formDynamicDetail.lifeEvents = res.data
            this.loading = false
            // console.error(this.formDynamicDetail.lifeEvents, '[[[[[[]]]]]]');
          }
        } else {
          this.loading = false
        }
      })
    },
    formatDate(date) {
      // 将Date对象格式化为"yyyy-MM-dd HH:mm:ss"格式
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    useSelect() {
      let roomInfo = this.$refs.prisonSelect.checkedRoom
      this.formValidate.lifeRoomsArr = roomInfo.map(item => item.roomName).join(",")
      this.formValidate.lifeRoomsList = roomInfo.map(item => item.roomCode)
      this.openModal = false
    },
    // 一日生活制度新增
    handleSubmit(name) {
      this.$refs['formDayTem'].validate((valid) => {
        if (valid) {
          this.$refs['formTemAdd'].validate((valid) => {
            if (valid) {

              try {
                if (this.formValidate.cycleConfigArr.length > 0) this.formValidate.cycleConfig = this.formValidate.cycleConfigArr.join(',')
                if (this.formValidate.customizeTimeArr.length > 0) {
                  let NewArr = this.formValidate.customizeTimeArr.map(item => {
                    return this.formatDate(item)
                  })
                  this.formValidate.customizeTime = NewArr.join('~')
                }
                this.formValidate.lifeRooms = this.formValidate.lifeRoomsList.map(item => {
                  let obj = {
                    roomId: item
                  }
                  return obj
                })
                let newList = this.formDynamicDetail.lifeEvents.map(item => {
                  let newObj = {
                    startTime: item.startTime,
                    endTime: item.endTime,
                    endTimeSpan: item.endTimeSpan,
                    eventId: item.eventId,
                    eventName: item.eventName,
                    isEnabledVoice: item.isEnabledVoice,
                    sort: item.sort
                  }
                  return newObj
                })
                // 当选中下拉框的制度时候，查询出来以后在对数据进行提交
                this.authPostRequest({ url: this.$path.lifeTemplate_life_create, params: { ...this.formValidate, lifeEvents: newList, id: "" } }).then(res => {
                  if (res.success) {
                    this.tableContainer = true
                    this.addOneDayTem = false
                    this.formValidate = {}
                    this.formValidate1 = {}
                    this.$set(this.formValidate, 'name', "")
                    this.$set(this.formValidate, 'cycleSetting', "")
                    this.$set(this.formValidate, 'cycleConfigArr', [])
                    this.$set(this.formValidate, 'customizeTimeArr', [])
                    this.$set(this.formValidate, 'lifeRoomsArr', "")
                    this.$set(this.formValidate1, 'templateName', "")
                    this.$set(this.formDynamicDetail, 'lifeEvents', [])
                    this.formDynamicDetail.lifeEvents = []
                    this.name = "name2"
                    this.$Message.success("制度模板创建成功")
                  }
                })
              } catch (error) {
                console.error(error);
              }
            } else {
              this.$Message.error('验证失败');
            }
          })
        } else {
          this.$Message.error('验证失败');
        }
      })
    },
    // 一日生活制度的审批按钮
    handleApprovalSp(index, { id }) {
      this.handleGetDetail(id)
      this.handlelifeTemplate_daily_life_room_list(id)
      this.handlelifeTemplate_daily_life_id(id)
      this.$set(this.formValidate5, 'status', "02")
      this.approvalDetailType = "approvaldo"
      this.tableContainer = false
      this.approvalContainer = true
    },
    handleInstructions(index, { id }) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.lifeTemplate_life_delete,
            params: {
              ids: id
            }
          }).then(res => {
            if (res.success) {
              this.on_refresh_table()
            }
          })
        }
      })
      // this.authGetRequest({ url: this.$path.lifeTemplate_life_delete, params: { ids: id } })
    },
    handleReset(name) {
      this.tableContainer = true
      this.addOneDayTem = false

      this.$set(this.formValidate, 'name', "")
      this.$set(this.formValidate, 'cycleSetting', "")
      this.$set(this.formValidate, 'cycleConfigArr', [])
      this.$set(this.formValidate, 'customizeTimeArr', [])
      this.$set(this.formValidate, 'lifeRoomsArr', "")
      this.$set(this.formValidate1, 'templateName', "")
      this.$set(this.formDynamicDetail, 'lifeEvents', [])
      this.formDynamicDetail.lifeEvents = []
    },
    handleGetDetail(id) {
      this.authGetRequest({ url: this.$path.lifeTemplate_life_get, params: { id } }).then(res => {
        if (res.success) {
          this.formValidate.cycleSetting = res.data.cycleSetting
          this.formValidate.name = res.data.name
          this.approvalDetailInfo.name = res.data.name
          this.approvalContainerInfo = res.data
          console.error(this.approvalContainerInfo, 'this.approvalContainerInfo ');
          

          if (this.approvalContainerInfo.cycleSetting == '01') {
            this.approvalContainerInfo.cycleSettingName = "每天"
          } else if (this.approvalContainerInfo.cycleSetting == '02') {
            this.approvalContainerInfo.cycleSettingName = "自定义"
          } else {
            this.approvalContainerInfo.cycleSettingName = "节假日"
          }

          this.id = res.data.id
        }
      })
    },
    handlelifeTemplate_daily_life_id(id) {
      this.authGetRequest({ url: this.$path.lifeTemplate_daily_life_id, params: { dailyLifeId: id } }).then(res => {
        if (res.success) {
          res.data.forEach(item => {
            item.status = true
            item.eventName = item.eventName
          })
          this.formDynamicDetail.lifeEvents = res.data
        }
      })
    },
    handlelifeTemplate_daily_life_room_list(id) {
      this.authGetRequest({ url: this.$path.lifeTemplate_daily_life_room_list, params: { dailyLifeId: id } }).then(res => {
        if (res.success) {
          this.formValidate.lifeRoomsArr = res.data.map(item => item.roomId)
          this.$set(this, 'lifeRoomsName', res.data.map(item => item.roomName).join(','))
        }
      })
    },

    handleDispose(index, { id }) {
      this.handleGetDetail(id)
      this.handlelifeTemplate_daily_life_id(id)
      this.handlelifeTemplate_daily_life_room_list(id)
      this.tableContainer = false
      this.addOneDayTem = true
    },
    // 一日生活制度详情的哪个按钮 --- 和新增是一个Tab页面的哪个
    handleDetailTz(index, { id }) {
      this.handlelifeTemplate_daily_life_id(id)
      this.handleGetDetail(id)

      this.tableContainer = false
      this.approvalDetailCOntainer = true
      this.name = "name2"
    },
    handleResetApprovalDetailCOntainer() {
      this.tableContainer = true
      this.approvalDetailCOntainer = false

      // this.tableContainer = false;
      // this.approvalContainer = true
    },
    handleCancel() {
      this.modalAddType = false
      this.$refs['form'].resetFields();
    },
    hanndleSubmit() {
      if (this.formValidate2.templateName) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            try {
              this.formDynamic.lifeTemplateEvents.forEach(item => {
                item.eventId = item.eventId.join(',')
                item.eventName = item.eventName.join(',')
                item.isEnabledVoice = item.isEnabledVoice ? 1 : 0
              })
            } catch (error) {
              console.error(error);
            }
            if (this.formDynamic.lifeTemplateEvents.length >= 1) {
              this.authPostRequest({ url: this.$path.lifeTemplate_create, params: { ...this.formDynamic, ...this.formValidate2, id: "" } }).then(res => {
                if (res.success) {
                  this.$Message.success('制度模板创建成功')
                  this.modalAddType = false
                  this.handleGetlifeTemplateList()
                } else {
                  this.$Message.error(res.message)
                }
              })
            } else {
              this.$Message.error('请先进行事务信息添加');
            }
          } else {
            this.$Message.error('验证失败');
            return;
          }
        })
      } else {
        this.$Message.error('监室事务名称不能为空');
      }
    },
    handleMultiChange(selectedItems, index) {
      this.formDynamic.lifeTemplateEvents[index].eventName = selectedItems.map(item => item.label);
    },
    on_refresh_table() {
      this.$refs.grid1.query_grid_data(1)
      this.$refs.grid.query_grid_data(1)
    },
    handleResetApproval(name) {
      // this.$refs['formValidate5'].resetFields();
      this.formValidate5 = {}
      this.formDynamicDetail.lifeEvents = []
      this.tableContainer = true
      this.approvalContainer = false
    },
    handleSubmitApproval(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.formValidate5.id = this.id
          this.authPostRequest({ url: this.$path.lifeTemplate_daily_approval_update, params: this.formValidate5, }).then(res => {
            if (res.success) {
              // this.$refs.grid.query_grid_data(1)
              this.tableContainer = true
              this.approvalContainer = false
              this.formValidate5 = {}
              this.formDynamicDetail.lifeEvents = []
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证失败')
        }
      })
    },
    // 详情按钮这个是待审批那个页面的详情按钮
    handledetail(index, { id }) {
      this.approvalDetailType = "detail"
      this.handleGetDetail(id)
      this.handlelifeTemplate_daily_life_id(id)
      this.handlelifeTemplate_daily_life_room_list(id)
      this.tableContainer = false;
      this.approvalContainer = true
    }
  },

  components: {
    sDataGrid,
    roomSelect
  },

  created() {
    this.handleGetYRSHZD()
    this.handleGetlifeTemplateList()
    this.hanndleGetlifeDictEventList()
    // this.handleGetAreaPrisonRooms()
    this.getRoom()

  },

  computed: {},
  watch: {
    'formValidate5.status': {
      handler(newVal) {
        if (newVal == '02') {
          this.formValidate5.approvalComments = "拟同意"
        } else {
          this.formValidate5.approvalComments = "不同意"
        }
      },
      immediate: true
    },
    'single': {
      handler(newVal) {
        console.error(newVal);

      },
      immediate: true
    }
  },

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

//
/deep/.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #00244A;
  /* margin-bottom: 16px; */
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-bottom: 1px solid #CEE0F0;
}

.form-list-approval {
  border: solid 1px #f5f5f5;
  width: 200px;
  padding: 10px 5px 20px 5px;
  position: relative;
  background-color: #fff;
  margin: 5px;
  margin-right: 60px;

  // &::after {
  //   content: "→";
  //   margin-left: 5px;
  //   color: #3498db;
  // }


  .index-num-approval {
    position: absolute;
    left: 11px;
    top: 24px;
    background-color: #2390ff;
    color: #fff;
    border-radius: 14px;
    line-height: 14px;
    text-align: center;
    width: 15px;
    height: 15px;
  }

  .form-content-approval {

    .select-template-approval {
      background-color: #eff6ff;
      // width: 110px;
      padding: 0 5px;
      text-align: center;
      border-radius: 3px;
    }
  }

  .svg-arrow {
    transform: rotate(90deg);
    position: absolute;
    right: -42px;
    top: 46px;

    img {
      width: 30px;
    }
  }
}

.form-list {
  width: 1000px;
  // height: 120px;
  padding: 10px 10px 10px 0;
  border: solid 1px #f5f5f5;
  border-radius: 4px;
  margin-bottom: 10px;
  position: relative;

  .index-num {
    position: absolute;
    top: 20px;
    width: 30px;
    border-radius: 0 15px 15px 0;
    color: #fff;
    background-color: #2390ff;
  }

  .form-content {
    margin-left: 40px;
    margin-right: 20px;

    .header-form {
      display: flex;
      justify-content: space-between;

      .time-chioce {
        display: flex;
        align-items: center;
      }
    }
  }

  .del-icon {
    position: absolute;
    right: 10px;
    top: 5px;
    ;
  }
}


.switch-container-detail-approval {
  position: absolute;
  padding: 2px;
  right: 0;
  top: 0;
  background: #ff7700;
  color: #fff;
  font-size: 14px;
  border-radius: 0 5px 0 5px;
}

.switch-container-detail {
  position: absolute;
  padding: 5px 8px;
  right: 0;
  top: 0;
  background: #ff7700;
  color: #fff;
  font-size: 14px;
  border-radius: 0 5px 0 5px;
}

.modal-scroll-content {
  max-height: 400px;
  /* 最大高度，超出后滚动 */
  overflow-y: auto;
  /* 垂直滚动 */
  padding: 16px;
}

.page-box {
  text-align: right;
  margin: 10px 0 10px;
}

.activeRoom {
  border: 1px solid blue !important;
}

.room-wrap {
  font-size: 14px;
  width: 100%;
  display: flex;
  margin-top: 16px;
  flex-wrap: wrap;
  height: 230px;
  overflow-y: auto;
  align-content: flex-start;

  .room-wrap-box {
    position: relative;
    display: flex;
    // width: 19%;
    border: 1px solid rgb(220, 222, 226);
    padding: 0 10px;
    margin: 0 5px 5px 0;
    border-radius: 6px;
    align-items: center;
    cursor: pointer;

    &:hover {
      border-color: blue;
    }

    .bottom-roomName-container {
      position: relative;

      .room-check {
        position: absolute;
        left: -40px;
        top: 3px;
      }
    }

    img {
      width: 30px;
      height: 30px;
      margin-right: 2px;
    }
  }
}
</style>
