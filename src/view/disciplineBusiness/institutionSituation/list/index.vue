<template>
  <s-DataGrid ref="grid1" funcMark="jjks-sqgllb" :customFunc="true">
    <template slot="customHeadFunc" slot-scope="{ func }">
      <Button v-if="func.includes(globalAppCode + ':jjks-sqgllb:sqdj')" type="primary"
        @click.native="toCreateSettle">所情登记</Button>
    </template>
    <template slot="customRowFunc" slot-scope="{ func, row }">
      <Button v-if="func.includes(globalAppCode + ':jjks-sqgllb:xkcz') && row.status === '0'" type="primary"
        @click="toSettle(row)">巡控处置</Button>
      <Button v-if="func.includes(globalAppCode + ':jjks-sqgllb:sqcz') && row.status === '1'" type="primary"
        @click="toInspectionOrCheck(row, 1)">所情处置</Button>
      <Button v-if="func.includes(globalAppCode + ':jjks-sqgllb:ldsh') && row.status === '2'" type="primary"
        @click="toInspectionOrCheck(row, 2)">领导审核</Button>
      <Button v-if="func.includes(globalAppCode + ':jjks-sqgllb:xq') && +row.status >= 3" type="primary"
        @click="toDetail(row)">详情</Button>
    </template>
  </s-DataGrid>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
    };
  },
  created() {
  },
  methods: {
    // 新增所情登记
    toCreateSettle() {
      this.$router.replace({
        path: `/discipline/institutionSituation/settle`,
      });
    },
    // 去所情处置或审核页
    toInspectionOrCheck(row, type) {
      this.$router.replace({
        path: `/discipline/institutionSituation/inspectionOrCheck?id=${row.id}&type=${type}`,
      });
    },
    // 去巡控处置页面
    toSettle(row) {
      this.$router.replace({
        path: `/discipline/institutionSituation/settle?id=${row.id}`,
      });
    },
    toDetail(row) {
      this.$router.replace({
        path: `/discipline/institutionSituation/detail?id=${row.id}`,
      });
    },
  },
};
</script>

<style scoped lang="less"></style>
