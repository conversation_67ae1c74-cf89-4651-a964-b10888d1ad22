<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      {{ routerData.id ? '巡控处置' : '所情登记' }}
    </div>
    <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
      <Row style="padding: 10px" :gutter="16">
        <Col :span="8">
        <div :class="'card-background-' + formData.eventLevel" style="color: #fff; border-radius: 0.5em; padding: 10px">
          <Row>
            <Col :span="6">
            <div
              style="border: 1px solid #efefef;border-radius: 0.5em;background-color: #ffffff09; display: flex; flex-direction: column; height: 156px;align-items: center; padding: 10px">
              <Icon :size="64" style="margin: 10px 0" type="md-notifications" />
              <div style="">
                <s-dicgrid style="width: 80px" :isSearch="false" placeholder="请选择所情等级" v-model="formData.eventLevel"
                  ref="dicGrid" dicName="ZD_JJKS_SQDJ" />
              </div>
            </div>
            </Col>
            <Col :span="18">
            <div style="margin-left: 15px">
              <Row style="display:flex;" :gutter="16">
                <Col :span="12">
                <Select v-model="formData.areaId" @on-change="handleRoomChange" label-in-value>
                  <OptionGroup v-for="item in roomList" :key="item.areaCode" :label="item.areaName">
                    <Option style="padding-left: 2em" v-for="j in item.children" :label="j.roomName" :value="j.roomCode"
                      :key="j.roomCode"></Option>
                  </OptionGroup>
                </Select>
                </Col>
                <Col :span="12">
                <DatePicker v-model="formData.happenTime" type="datetime" placeholder="请选择报警时间">
                </DatePicker>
                </Col>
              </Row>
              <div style="margin-top: 8px;">
                <PeopleSelector v-model="formData.bjryList" class="fixStyle"
                  :options="{ addTitleValue: '报警人', limit: 3, multiple: false }" />
              </div>
            </div>
            </Col>
          </Row>
        </div>
        <Tabs value="realTime" style="margin-top: 15px;">
          <TabPane label="监控实况" name="realTime" style="min-height: 340px">
            <GoPlayer />
          </TabPane>
          <TabPane label="监控回放" name="playback" style="min-height: 340px">
            <GoPlayer :playConfig="{ isPlayback: true }" />
          </TabPane>
          <TabPane v-if="routerData.id" label="截图照片" name="screenshot" style="min-height: 340px">
            <div v-if="detail.screenshotUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
              <img v-for="i in detail.screenshotUrl.split(',')" :key="i" style="width: 175px; height:120px"
                :src="`//${i}`" alt="">
            </div>
            <div v-else>
              <noData />
            </div>
          </TabPane>
          <TabPane v-if="routerData.id" label="附件" name="attachment" style="min-height: 340px">
            <div v-if="detail.attUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
              <div v-for="i in detail.attUrl.split(',')" :key="i"
                style="display:flex; align-items: center; width: 225px; border: 1px solid #efefef; padding: 10px;font-size: 14px">
                <Icon :size="36" type="md-document" />
                <div style="margin-left: 15px;flex:1">
                  <p>名称：{{ 333 }}</p>
                  <p>大小：{{ 333 }}MB</p>
                  <p>类型： pdf</p>
                  <p style="text-align: right;">
                    <a href="javascript:;" style="color: #2b5fd9" download="">下载</a>
                  </p>
                </div>
              </div>
            </div>
            <div v-else>
              <noData />
            </div>
          </TabPane>
        </Tabs>
        </Col>
        <Col :span="16">
        <div style="height: 640px;overflow-y: auto; overflow-x: hidden;border: 1px solid #efefef; border-radius:
          0.25em">
          <div style="padding: 10px">
            <div style="margin-bottom: 15px;">
              <div class="form-title">所情事件</div>
              <Row style="padding: 10px 0">
                <Col :span="!routerData.id ? 22 : 24" style="padding-right: 1em;">
                <FormItem label="所情名称" prop="eventName">
                  <Select v-if="nameUseTemplate || routerData.id" :disabled="routerData.id" v-model="formData.templateId"
                    @on-change="handleNameChange" label-in-value>
                    <Option v-for="item in prisonEventTypeList" :label="item.templateName" :value="item.id"
                      :tag="item" :key="item.id">
                    </Option>
                  </Select>
                  <Input v-else v-model="formData.eventName" @on-blur="handleNameChange" :disabled="routerData.id"
                    placeholder="所情名称"></Input>
                </FormItem>
                </Col>
                <Col v-if="!routerData.id" :span="2">
                <Checkbox v-model="nameUseTemplate"
                  @on-change="() => { formData.eventName = ''; formData.eventType = ''; formData.templateId = ''}" style="margin-top: 5px;">使用模板
                </Checkbox>
                </Col>
                <Col :span="routerData.id ? 22 : 24" style="padding-right: 1em;">
                <FormItem label="所情类型" prop="eventType">
                  <Select v-if="nameUseTemplate || typeUseTemplate" v-model="formData.eventType" label-in-value>
                    <Option v-for="item in prisonEventTypeDetail.eventTypeSettingList" :label="item.eventType"
                      :value="item.eventType" :key="item.eventType">
                    </Option>
                  </Select>
                  <Input v-else v-model="formData.eventType" placeholder="所情类型"></Input>
                </FormItem>
                </Col>
                <Col v-if="routerData.id" :span="2">
                <Checkbox v-model="typeUseTemplate" @on-change="() => { formData.eventName = ''; }"
                  style="margin-top: 5px;">使用模板
                </Checkbox>
                </Col>
              </Row>
            </div>
            <div style="margin-bottom: 15px;">
              <div class="form-title">关联人员</div>
              <div style="padding: 10px 0">
                <el-collapse v-model="activeName" accordion>
                  <el-collapse-item title="在押人员" name="1">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '在押人员' }" v-model="formData.jgryList" />
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="工作人员" name="2">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '工作人员' }" type="worker" v-model="formData.gzryList" />
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="外来人员" name="3">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '外来人员' }" type="outer" v-model="formData.wlryList" />
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
            <div>
              <div class="form-title">所情处置</div>
              <div style="padding: 10px 0">
                <Row>
                  <Col :span="24">
                  <FormItem label="精准时间" prop="eventRangeTime">
                    <DatePicker v-model="formData.eventRangeTime" style="width: 420px" type="datetimerange"
                      placeholder="请选择精准时间">
                    </DatePicker>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="事件详情" prop="eventDetails">
                    <Input v-model="formData.eventDetails" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                      placeholder="请填写事件详情"></Input>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <Row>
                    <Col :span="22" style="padding-right: 1em;">
                    <FormItem label="处置情况" prop="handleInfo" style="margin-bottom: 15px;">
                      <Select v-if="handleInfoUseTemplate" v-model="formData.handleInfo" label-in-value>
                        <Option v-for="item in prisonEventTypeDetail?.handlingSituationList" :label="item.handleName"
                          :value="item.handleContent" :key="item.handleName">
                        </Option>
                      </Select>
                      <Input v-else v-model="formData.handleInfo" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                        placeholder="请填写处置情况"></Input>
                    </FormItem>
                    </Col>
                    <Col :span="2">
                    <Checkbox v-model="handleInfoUseTemplate" @on-change="() => { formData.handleInfo = ''; }"
                      style="margin-top: 5px;">
                      使用模板
                    </Checkbox>
                    </Col>
                  </Row>
                  </Col>
                  <Col :span="24">
                  <FormItem label="推送对象" prop="tsdxList">
                    <Row class="table-head">
                      <Col :span="5">
                      岗位名称
                      </Col>
                      <Col :span="19">
                      增加推送人员
                      </Col>
                    </Row>
                    <Row class="table-body" v-for="(role, index) in formData.tsdxList" :key="role.pushPostCode">
                      <Col :span="5" style="display: flex; align-items: center; justify-content: center;">
                      <Checkbox :key="role.pushPostCode" v-model="formData.tsdxList[index].checkedState"
                        :disabled="formData.tsdxList[index].isDefault">{{
                          role.pushPostName
                        }}</Checkbox>
                      </Col>
                      <Col :span="19">
                      <PeopleSelector :options="{ addTitleValue: '', limit: 1 }" type="worker"
                        :disabled="!formData.tsdxList[index].checkedState"
                        v-model="formData.tsdxList[index].otherUserList" />
                      </Col>
                    </Row>
                  </FormItem>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
        </Col>
      </Row>
    </Form>
    <div style="position: sticky; display: flex; justify-content: flex-end; gap: 10px;">
      <Button @click="goList">取 消</Button>
      <Button @click="handleSubmit(1)" type="primary">提 交</Button>
      <Button v-if="routerData.id" @click="handleSubmit(2)" type="info"
        style="background-color: #2db7f5; border-color: #2db7f5;">办
        结</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import { GoPlayer } from "@/components"
import { PeopleSelector } from "./components";
import noData from "@/components/noData";
export default {
  components: {
    GoPlayer,
    PeopleSelector,
    noData
  },
  data() {
    return {
      loading: false,
      nameUseTemplate: false,
      typeUseTemplate: false,
      handleInfoUseTemplate: false,
      routerData: {
        id: ""
      },
      roomList: [],
      formData: {
        areaId: "",
        areaName: "",
        eventLevel: '4',
        eventSrc: "001", // 所情登记时为001
        eventTemplateId: "",
        eventType: "",
        happenTime: new Date(),
        eventRangeTime: [],
        eventStartTime: "",
        eventEndTime: "",
        eventDetails: "",
        jgryList: [], // 在押人员
        bjryList: [], // 报警人员
        wlryList: [], // 外来人员
        gzryList: [], // 工作人员
        tsdxList: [], // 推送人员
        handleInfo: "" // 处置情况
      },
      // 单个所情来源下所有的所情类型列表
      prisonEventTypeList: [],
      // 单个所情事件详情
      prisonEventTypeDetail: {
        // 推送角色列表
        pushObjectSettingList: [],
        // 事件内容列表
        eventTypeSettingList: [],
        // 处置模板列表
        handlingSituationList: []
      },
      // 所有岗位类型
      pushRolesList: [],
      detail: {
        attUrl: "",
        screenshotUrl: "",
        jgryList: [], // 在押人员
        bjryList: [], // 报警人员
        wlryList: [], // 外来人员
        gzryList: [], // 工作人员
        tsdxList: [], // 推送人员
      },
      activeName: "1",
      ruleValidate: {}
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.fetch();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    fetch() {
      if (this.routerData.id) {
        this.getDetail(this.routerData.id);
      } else {
        this.getSolutionList();
      }
      this.getJailRoomList();
    },
    getDetail(id) {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.detail = res.data;
          this.getSolutionList(res.data.eventSrc);
          // 以下内容为重置数据类型，基本上可以忽视
          this.detail.eventRangeTime = [this.detail.eventStartTime, this.detail.eventEndTime];
          this.detail.jgryList = (this.detail.jgryList === null && []) || this.detail.jgryList;
          this.detail.bjryList = (this.detail.bjryList === null && []) || this.detail.bjryList;
          this.detail.wlryList = (this.detail.wlryList === null && []) || this.detail.wlryList;
          this.detail.gzryList = (this.detail.gzryList === null && []) || this.detail.gzryList;
          // 回显选择的事件内容
          Object.assign(this.formData, this.detail);
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 获取推送对象字典
    getPushRolesList() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: {
          dicName: "ZD_POST",
        },
      }).then((res) => {
        if (res.success) {
          this.formData.tsdxList = [];
          this.pushRolesList = res.data.filter(item => {
            // 固定的6个岗位【巡控、管教、领导、综合、押解、医务】
            return ["02", "03", "04", "05", "06", "07"].includes(item.code);
          });
          console.log("所情类型详情-推送岗位列表", this.prisonEventTypeDetail.pushObjectSettingList);
          console.log("所有岗位列表", this.pushRolesList);
          // 需要把获取到的推送对象转换到formData中的tsdxList
          this.pushRolesList.forEach(item => {
            const result = this.prisonEventTypeDetail.pushObjectSettingList?.findIndex(role => role.disposePost === item.code) > -1;
            this.formData.tsdxList.push({
              checkedState: result,
              isDefault: result,
              pushPostCode: item.code,
              pushPostName: item.name,
              pushUserName: "",
              pushUserSfzh: "",
            })
            this.formData.tsdxList = this.formData.tsdxList.sort((a, b) => {
              // 将checkedState为true的排在前面
              if (a.checkedState === b.checkedState) return 0;
              if (a.checkedState) return -1;
              return 1;
            })
          })
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 获取所情模板
    getSolutionList(eventSrc) {
      // 注意有两种获取所情模板的场景
      // 1、所情登记时，在勾选使用模板时，只获取【所情来源】为【所情登记】下的所有【所情事件】
      // 2、巡控核实时，只获取当前【所情来源】下所有【所情事件】
      this.authPostRequest({
        url: api.prisonEventTypeList,
        params: {
          eventSrc: eventSrc || '001', // 硬编码，可以但不提倡
        }
      }).then(res => {
        !res.success && this.$Message.error("获取所情模板失败");
        this.prisonEventTypeList = res.data;
      })
    },
    // 获取监室信息
    getJailRoomList() {
      this.authGetRequest({
        url: api.getJailArea,
        params: {
          orgCode: this.$store.state.common.orgCode
        }
      }).then((res) => {
        if (res.success) {
          this.roomList = res.data || [];
        } else {
          this.$Message.error("获取监室数据失败");
        }
      });
    },
    // 监室选择
    handleRoomChange(e) {
      this.formData.areaName = e.label;
    },
    // 所情名称变化
    handleNameChange(val) {
      if (val.tag) {
        Object.assign(this.prisonEventTypeDetail, val.tag);
        this.formData.eventTemplateId = val.value;
        this.formData.eventName = val.label;
      }
      // 所情名称变化时要清空部分数据
      this.formData.eventType = '';
      this.formData.handleInfo = "";
      this.getPushRolesList();
    },
    handleSubmit(type) {
      this.loading = true;
      this.formData.saveType = type;
      this.formData.eventStartTime = this.formData.eventRangeTime[0];
      this.formData.eventEndTime = this.formData.eventRangeTime[1];
      this.formData.tsdxList = this.formData.tsdxList.filter(item => item.checkedState === true);

      const convertList = []
      // 报警人员的列表personnelID要传身份证号因此需要特殊处理
      this.formData.bjryList.forEach(item => {
        convertList.push({
          ...item,
          personnelId: item.zjhm
        })
      });

      this.formData.bjryList = convertList;

      const tempList = [];
      // 将所选推送人员信息提取到第一层
      this.formData.tsdxList.forEach(item => {
        tempList.push({
          ...item,
          // 因为现有逻辑下推送岗位只允许选择一名成员，所以写死[0]
          pushUserName: item.otherUserList[0]?.xm,
          pushUserSfzh: item.otherUserList[0]?.zjhm
        })
      })
      this.formData.tsdxList = tempList;
      this.authPostRequest({
        url: this.routerData.id ? api.verify : api.create,
        params: this.formData,
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Message.success("操作成功");
          this.handleCancel();
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
    },
    handleCancel() {
      this.resetForm();
      this.goList();
    },
    goList() {
      this.$router.replace({ name: "institutionSituationList" });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-collapse-item {
  .el-collapse-item__header {
    background: #f9fafd !important;
    border: 1px solid #dbe4ff;
    padding: 0 1.2em;
    font-size: 16px;
    color: #4f4f4f;
  }

  .el-collapse-item__wrap {
    border: 1px solid #dbe4ff;
  }
}

.card-background {
  &-1 {
    background: linear-gradient(45deg, #f7645f 5%, #f54040);
  }

  &-2 {
    background: linear-gradient(45deg, #f9cc45 5%, #f8bb20);
  }

  &-3 {
    background: linear-gradient(45deg, #f8c638 5%, #cbce2b);
  }

  &-4 {
    background: linear-gradient(45deg, #58a0fe 5%, #168aff);
  }
}

.fixStyle {
  /deep/ .people-selector-item_plusmask {
    color: #fff;
    border-color: #fff;
  }
}

.form-title {
  font-size: 16px;
  font-weight: bold;
  padding-left: 10px;
  position: relative;

  &::before {
    content: "";
    width: 4px;
    background-color: #343cf3;
    position: absolute;
    left: 0;
    top: 10%;
    bottom: 10%;
  }
}

.table-head {
  text-align: left;
  line-height: 3;
  font-weight: bold;
  color: #232323;

  >div {
    padding: 0 15px;
    background-color: #2db7f5;
    border: 1px solid #eaeaea;

    &:nth-of-type(2) {
      border-left: none;
      border-right: none;
    }
  }
}

.table-body {
  >div {
    padding: 10px;
    border: 1px solid #eaeaea;
    border-top: none;

    &:nth-of-type(2n) {
      border-left: none;
      border-right: none;
    }
  }
}
</style>
