<template>
  <div class="people-selector">
    <template v-if="action === 'add' || (action !== 'add' && value.length > 0)">
      <div class="people-selector-item" :class="{ 'disabled': disabled }" v-for="(item, index) in value" :key="item.id">
        <div v-if="action === 'add'" class="people-selector-item_delmask" @click="handleRemove(index)">
          <Icon :size="20" type="md-trash" style="color:white"></Icon>
        </div>
        <div v-if="action === 'operate'" class="people-selector-item_operatemask"
          @click="handleDeductPoint(item, index)">
          扣{{ item.deductPoint || 0 }}分
        </div>
        <img class="people-selector-item_img" :src="item.zpUrl || defaultImg" alt="photos" />
        <span>{{ item.xm || item.jgryxm || item.personnelName }}</span>
      </div>
      <div v-if="action === 'add' && (!options.limit || selectCount < options.limit)" class="people-selector-item"
        :class="{ 'disabled': disabled }">
        <div class="people-selector-item_plusmask" @click="handleAdd">
          +</div>
        <span v-if="options.addTitleValue">{{ options.addTitleValue }}</span>
      </div>
    </template>
    <div v-else style="width: 100%;padding:5px;text-align:center;border:1px solid #efefef">
      <Icon :size="24" type="md-warning" />
      <p>暂无数据</p>
    </div>
    <!-- 选择监管人员弹窗 -->
    <PeopleSelect ref="peopleSelect" @select="handleChoseSubmit" :options="options" :type="type" />
    <!-- 扣分弹窗 -->
    <Modal v-model="deductPointModalVisible" :mask-closable="false" :closable="true" class-name="select-use-modal"
      width="420" title="扣分操作">
      <Form :model="formData" ref="form" :label-width="80" @submit.native.prevent>
        <FormItem label="扣分数量" prop="deductPoint">
          <Input v-model="formData.deductPoint" type="number" placeholder="请填写扣分数量"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleDeductSubmit">确 定</Button>
        <Button @click="deductPointModalVisible = false">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { PeopleSelect } from "@/components";
import defaultImg from "@/assets/images/detentionEnter/person.png";
export default {
  components: { PeopleSelect },
  props: {
    action: {
      type: String,
      default: 'add' // add 加人模式， show 回显已选人, operate 可扣分
    },
    type: {
      type: String,
      default: 'prisoner' // worker 工作人员 | outer 外来人员 | prisoner 被监管人员
    },
    value: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => ({ addTitleValue: "", limit: 0 })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deductPointModalVisible: false,
      formData: {
        deductPoint: 0,
        index: 0
      },
      defaultImg,
      selectCount: 0
    }
  },
  methods: {
    handleDeductPoint(item, index) {
      if (this.disabled) return;
      this.formData.deductPoint = item.deductPoint || 0;
      this.formData.index = index;
      this.deductPointModalVisible = true;
    },
    handleDeductSubmit() {
      const list = this.value;
      list[this.formData.index].deductPoint = this.formData.deductPoint;
      this.deductPointModalVisible = false;
      this.$emit("input", list);
    },
    handleRemove(index) {
      if (this.disabled) return;
      this.selectCount--;
      const copyList = [].concat(this.value);
      copyList.splice(index, 1);
      this.$emit("input", copyList);
    },
    handleAdd() {
      if (this.disabled) return;
      this.$refs.peopleSelect.open(this.value);
    },
    handleChoseSubmit(list) {
      if (list.length > 0) {
        console.log(111, list);
        
        this.selectCount = list.length;
        const convertedList = list.map(item => {
          if (this.type === 'prisoner') {
            item.personnelId = item.jgrybm;
            item.personnelName = item.xm;
            item.photoUrl = item.frontPhoto;
            item.roomId = item.jsh;
            // item.sqdjld = item.xxxxx;
          } else if (this.type === 'worker') {
            item.personnelId = item.zjhm;
            item.personnelName = item.xm;
            item.photoUrl = item.zpUrl || item.zp_url;
          } else {
            item.personnelId = item.id;
            item.personnelName = item.xm;
            item.photoUrl = item.zpUrl;
          }
          delete item.id;

          return item;
        })
        this.$emit('input', convertedList);
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
    }
  },
}
</script>

<style lang="less" scoped>
.people-selector {
  display: flex;
  flex-wrap: wrap;

  .people-selector-item {
    text-align: center;
    width: 80px;
    position: relative;
    cursor: pointer;

    &:not(:first-of-type) {
      margin-left: 10px;
    }

    &:hover {
      .people-selector-item_delmask {
        opacity: 1;
      }
    }

    &.disabled {
      cursor: not-allowed;

      .people-selector-item_plusmask {
        color: #5b5d63;
        border: 1px dashed #5b5d63;
      }
    }

    &_delmask {
      opacity: 0;
      line-height: 95px;
      position: absolute;
      right: 0;
      left: 0;
      top: 0;
      height: 95px;
      background: #00000052;
      transition: opacity 0.5s;
    }

    &_img {
      width: 100%;
      height: 95px;
      display: block;
    }

    &_plusmask {
      width: 100%;
      color: #5884ff;
      line-height: 90px;
      height: 95px;
      border: 1px dashed #5884ff;
      font-size: 28px;
    }

    &_operatemask {
      cursor: pointer;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      height: 20px;
      background-color: #349ffba5;
      color: #fff;
      font-size: 14px;
    }
  }
}
</style>
