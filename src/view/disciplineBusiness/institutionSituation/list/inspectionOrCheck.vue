<template>
  <div v-loading="loading" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      {{ routerData.type === '1' ? '所情处置' : '所领导审批' }}
    </div>
    <Row style="height: 680px;overflow-y: auto; overflow-x: hidden;padding: 10px" :gutter="16">
      <Col :span="8">
      <div :class="[detail.eventLevel ? 'card-background-' + detail.eventLevel : 'card-background-1']"
        style="color: #fff; border-radius: 0.5em; padding: 10px">
        <Row>
          <Col :span="6">
          <div
            style="border: 1px solid #efefef;border-radius: 0.5em;background-color: #ffffff09; display: flex; flex-direction: column; height: 136px;align-items: center; padding: 10px">
            <Icon :size="64" style="margin: 10px 0" type="md-notifications" />
            <div :class="[detail.eventLevel ? 'card-background-' + detail.eventLevel : 'card-background-1']"
              style="display: inline-block; max-width: 6em; text-align:center; padding: 0.1em 1em;border:1px solid; border-radius:0.5em;color: #fff">
              {{ detail.eventLevelName || '--' }}
            </div>
          </div>
          </Col>
          <Col :span="18">
          <div style="margin-left: 15px">
            <Row style="display:flex; align-items: baseline">
              <Col :span="12">
              <h3 style="color: inherit; font-size: 24px">{{ detail.areaName || '-' }}</h3>
              </Col>
              <Col :span="12">
              <div>
                <Icon type="ios-alert-outline" />
                {{ detail.eventSrcName || '-' }}
              </div>
              </Col>
            </Row>
            <div style="margin-top: 8px">报警时间： {{ detail.eventStartTime }}-{{ detail.eventEndTime }}</div>
            <div style="margin-top: 10px">所情事件： {{ detail.eventName || "-" }}</div>
            <div style="margin-top: 10px">报警人员： {{detail.bjryList.map(item => item.personnelName).join(',') || '-' }}</div>
          </div>
          </Col>
        </Row>
      </div>
      <div style="margin-top:10px">
        <Tabs value="jailPeople">
          <TabPane label="被监管人员" name="jailPeople">
            <PeopleSelector v-model="detail.jgryList" action="show" />
          </TabPane>
          <TabPane label="工作人员" name="workPeople">
            <PeopleSelector v-model="detail.gzryList" action="show" />
          </TabPane>
          <TabPane label="外来人员" name="outPeople">
            <PeopleSelector v-model="detail.wlryList" action="show" />
          </TabPane>
        </Tabs>
      </div>
      <Tabs value="realTime" style="margin-top:15px">
        <TabPane label="监控实况" name="realTime" style="min-height: 340px">
          <GoPlayer />
        </TabPane>
        <TabPane label="监控回放" name="playback" style="min-height: 340px">
          <GoPlayer :playConfig="{ isPlayback: true }" />
        </TabPane>
        <!-- 功能变更暂时屏蔽 -->
        <!-- <TabPane label="截图照片" name="screenshot" style="min-height: 340px">
          <div v-if="detail.screenshotUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
            <img v-for="i in detail.screenshotUrl.split(',')" :key="i" style="width: 175px; height:120px"
              :src="`//${i}`" alt="">
          </div>
          <div v-else>
            <noData />
          </div>
        </TabPane> -->
        <TabPane label="附件" name="attachment" style="min-height: 340px">
          <div v-if="detail.attUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
            <div v-for="i in detail.attUrl.split(',')" :key="i"
              style="display:flex; align-items: center; width: 225px; border: 1px solid #efefef; padding: 10px;font-size: 14px">
              <Icon :size="36" type="md-document" />
              <div style="margin-left: 15px;flex:1">
                <p>名称：{{ 333 }}</p>
                <p>大小：{{ 333 }}MB</p>
                <p>类型： pdf</p>
                <p style="text-align: right;">
                  <a href="javascript:;" style="color: #2b5fd9" download="">下载</a>
                </p>
              </div>
            </div>
          </div>
          <div v-else>
            <noData />
          </div>
        </TabPane>
      </Tabs>
      </Col>
      <Col :span="16">
      <div>
        <div style="border-bottom: 1px solid #efefefef;padding: 8px 0; display:flex; justify-content: space-between">
          <div style="padding-left: 10px;font-weight: bold;">{{ routerData.type === '1' ? '所情处置' : '所领导审批' }}</div>
          <div style="padding-right: 10px; display: flex">
            <div v-for="item in detail.jdList" :key="item.postName" style="margin-right: 15px">{{ item.postName
              }}: <span :style="{ color: item.handleStatus === '0' ? 'red' : 'blue' }">{{ item.handleStatusName }}</span>
            </div>
            <Icon :size="22" style="color: #349ffb;cursor:pointer; line-height: 1.2em" @click="refresh"
              type="md-sync" />
          </div>
        </div>
        <div style="padding: 10px">
          <el-collapse v-model="activeNames">
            <el-collapse-item :name="item.handlePostName" v-for="item in detail.trajectoryList" :key="item.id">
              <template slot="title">
                <div style="flex:1; display: flex; justify-content: space-between;">
                  <div>
                    <Icon
                      :type="activeNames.includes(item.handlePostName) ? 'ios-arrow-dropdown-circle' : 'ios-arrow-dropright-circle'"
                      :size="22" />
                    <span style="font-weight: bold; padding-left: 10px; font-size: 16px;">{{ item.handlePostName
                      }}</span>
                  </div>
                  <div>{{ item.handleTime }}</div>
                </div>
              </template>
              <div v-if="item.handlePostCode !== '03'" style="padding: 10px">
                <div style="line-height: 2.2;">事件详情：{{ item.eventDetails }}</div>
                <div style="line-height: 2.2;">备注：{{ item.feedbackInfo }}</div>
              </div>
              <div v-else style="padding: 10px">
                <div>
                  <div class="content-title">处置预案</div>
                  <div class="content-container">
                    333333333333
                  </div>
                </div>
                <div>
                  <div class="content-title">所情处置</div>
                  <div class="content-container">
                    333333333333
                  </div>
                </div>
                <div>
                  <div class="content-title">处置情况</div>
                  <div class="content-container">5555555</div>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item name="4" v-if="routerData.type === '2'">
              <template slot="title">
                <div style="flex:1; display: flex; justify-content: space-between;">
                  <div>
                    <Icon :type="activeNames.includes('4') ? 'ios-arrow-dropdown-circle' : 'ios-arrow-dropright-circle'"
                      :size="22" />
                    <span style="font-weight: bold; padding-left: 10px; font-size: 16px;">所领导岗</span>
                  </div>
                  <div>待审批</div>
                </div>
              </template>
              <div style="padding: 10px" class="check-box">
                <Form :model="formData" ref="form" :label-width="80" @submit.native.prevent>
                  <Row v-for="(item) in formData.approveResultList" :key="item.handleId">
                    <Col :span="24">
                    <FormItem :label="item.handlePostName" prop="approvalResult">
                      <RadioGroup v-model="item.approvalResult"
                        @on-change="(e) => { item.approvalComments = e === '1' ? '通过' : '不通过' }">
                        <Radio label="1">通过</Radio>
                        <Radio label="0">不通过</Radio>
                      </RadioGroup>
                    </FormItem>
                    </Col>
                    <Col :span="24">
                    <FormItem label="审批意见" prop="approvalComments">
                      <Input v-model="item.approvalComments" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                        placeholder="请填写审批意见"></Input>
                    </FormItem>
                    </Col>
                  </Row>
                </Form>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      </Col>
    </Row>
    <div style="position: sticky; display: flex; justify-content: flex-end; gap: 10px;">
      <Button @click="goList">取 消</Button>
      <Button v-if="routerData.type === '1'" @click="handleSubmit(0)" type="info"
        style="background-color: #2db7f5; border-color: #2db7f5;">保 存</Button>
      <Button @click="handleSubmit(1)" type="primary">提
        交</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import { GoPlayer } from "@/components"
import noData from "@/components/noData";
import { PeopleSelector } from "./components";
export default {
  components: {
    GoPlayer,
    PeopleSelector,
    noData
  },
  data() {
    return {
      loading: false,
      routerData: {
        id: "",
        type: "",
      },
      detail: {
        attUrl: "",
        screenshotUrl: "",
        jgryList: [],
        reportPrisonerList: [],
        wlryList: [],
        gzryList: [],
        trajectoryList: []
      },
      nodeData: [],
      activeNames: [],
      formData: {
        approveResultList: []
      },
    };
  },
  computed: {
  },
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.refresh()
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    refresh() {
      this.getDetail();
      this.getCurrent();
    },
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.detail = res.data;
          // 以下内容为重置数据类型，基本上可以忽视
          this.detail.eventRangeTime = [this.detail.eventStartTime, this.detail.eventEndTime];
          this.detail.jgryList = (this.detail.jgryList === null && []) || this.detail.jgryList;
          this.detail.reportPrisonerList = (this.detail.reportPrisonerList === null && []) || this.detail.reportPrisonerList;
          this.detail.wlryList = (this.detail.wlryList === null && []) || this.detail.wlryList;
          this.detail.gzryList = (this.detail.gzryList === null && []) || this.detail.gzryList;
          this.detail.trajectoryList = (this.detail.trajectoryList === null && []) || this.detail.trajectoryList;
          this.activeNames = [this.detail.trajectoryList[0].handlePostName];
          // 如果是领导岗位就把所有推送的岗位信息塞到formData中
          if (this.routerData.type === '2') {
            this.detail.trajectoryList.forEach(item => {
              this.formData.approveResultList.push({
                approvalComments: "通过",
                approvalResult: "1",
                handleId: item.id,
                handlePostCode: item.handlePostCode,
                handlePostName: item.handlePostName
              })
            })
          }
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 根据当前岗位获取所情处置的节点信息
    getCurrent() {
      this.loading = true;
      this.authGetRequest({
        url: api.getCurrent,
        params: { id: this.routerData.id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.nodeData = res.data;
          console.log(res.data);
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    handleSubmit(type) {
      this.loading = true;
      this.formData.saveType = type;
      console.log("handlesubmit", this.formData);
      this.authPostRequest({
        url: this.routerData.type === "2" ? api.approve : api.approve,
        params: { ...this.detail, ...this.formData },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Message.success("编辑成功");
          this.handleCancel();
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
    },
    handleCancel() {
      this.resetForm();
      this.goList();
    },
    goList() {
      this.$router.replace({ name: "institutionSituationList" });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-collapse-item {
  .el-collapse-item__header {
    background: #f9fafd !important;
    border: 1px solid #dbe4ff;
    padding: 0 1.2em;
    font-size: 16px;
    color: #4f4f4f;

    .el-collapse-item__arrow {
      display: none;
    }
  }

  .el-collapse-item__content {
    font-size: 15px;
    padding-bottom: 0;

    .check-box {
      .ivu-row:not(:last-child) {
        margin-bottom: 20px;
        border-bottom: 1px solid #efefef
      }
    }
  }

  .el-collapse-item__wrap {
    border: 1px solid #dbe4ff;
  }
}

.card-background {
  &-1 {
    background: linear-gradient(45deg, #f7645f 5%, #f54040);
  }

  &-2 {
    background: linear-gradient(45deg, #f9cc45 5%, #f8bb20);
  }

  &-3 {
    background: linear-gradient(45deg, #39c451 5%, #49a977);
  }

  &-4 {
    background: linear-gradient(45deg, #58a0fe 5%, #168aff);
  }
}

.content-title {
  padding-left: 5px;
  border-left: 4px solid #168aff;
  line-height: 20px;
}

</style>
