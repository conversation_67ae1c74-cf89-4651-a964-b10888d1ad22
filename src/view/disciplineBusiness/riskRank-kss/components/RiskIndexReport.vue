<template>
  <div class="riskIndexReport" v-loading.body="loading">
    <div style="display:flex; gap:10px; align-items: flex-end; padding: 5px 15px;border-bottom: 1px solid #efefef">
      <h3 style="color: #2b5fd9">风险评估报告</h3>
      <p style="flex: 1;font-size:13px;color: #686868;margin-left: 0.5em;">更新时间：{{ '2025-05-14 12:24' }}</p>
      <!-- <Tag color="error">风险</Tag> -->
    </div>
    <div v-if="detail && jgrybm" style="padding: 10px;">
      <ul class="nav-box">
        <li class="nav-item" :class="{ 'active': tabIndex === '1' }" @click="handleNavClick('1')">风险指标</li>
        <li class="nav-item" :class="{ 'active': tabIndex === '2' }" @click="handleNavClick('2')">加分指标</li>
        <li class="nav-item" :class="{ 'active': tabIndex === '3' }" @click="handleNavClick('3')">完整报告</li>
      </ul>
      <!-- 风险指标 -->
      <div v-show="tabIndex === '1'" class="index-box">
        <div class="index-header">
          <!-- <div class="header-tips">风险指标总数 {{ 9 }}项</div> -->
          <ul class="subnav-box">
            <li class="subnav-item" :class="{ 'active': subNavIndex === '0' }" @click="subNavIndex = '0'">全部（{{
              detail.allCount }}）
            </li>
            <li class="subnav-item" :class="{ 'active': subNavIndex === '1' }" @click="subNavIndex = '1'">高风险（{{
              detail.gfxCount }}）
            </li>
            <li class="subnav-item" :class="{ 'active': subNavIndex === '2' }" @click="subNavIndex = '2'">中风险（{{
              detail.zfxCount }}）
            </li>
            <li class="subnav-item" :class="{ 'active': subNavIndex === '3' }" @click="subNavIndex = '3'">低风险（{{
              detail.dfxCount }}）
            </li>
          </ul>
        </div>
        <div class="index-container">
          <Collapse v-if="detail.bgList.length" :value="subNavIndex === '0' ? ['1', '2', '3'] : subNavIndex">
            <Panel v-for="item in detail.bgList" :name="item.itemCode" :key="item.itemCode">
              <span>{{ item.itemName }}项（{{ item.itemCount }}）</span>
              <template v-if="item.indicatorList && item.indicatorList?.length">
                <List slot="content">
                  <ListItem v-for="(i, index) in item.indicatorList" :key="i.oneDesc">
                    <div>
                      <span>{{ `${index + 1}、` }}</span>
                      <span style="font-weight: 600;">{{ i.oneDesc }}：</span>
                      <span>{{ i.oneResult }}</span>
                      <Tag v-if="i.riskLevel" :color="['#ff2222', '#eb821b', '#f5b726'][i.riskLevel - 1]"
                        style="margin-left: 10px;">
                        {{
                        i.riskLevelName }}</Tag>
                    </div>
                  </ListItem>
                </List>
              </template>
            </Panel>
          </Collapse>
          <noData v-else style="margin-top: 150px;" />
        </div>
      </div>
      <!-- 加分指标 -->
      <div v-show="tabIndex === '2'" class="index-box">
        <div class="index-header">
          <div class="header-tips" style="flex:1; text-align: center;">加分指标总数 {{ detail.jfIndicatorCount }} 项</div>
        </div>
        <div class="index-container">
          <Collapse v-if="detail.jfIndicatorCount" :value="detail.bgList[0].itemCode">
            <Panel v-for="item in detail.bgList" :name="item.itemCode" :key="item.itemCode">
              <span>{{ item.itemName }}项（{{ item.count }}）</span>
              <template v-if="item.indicatorList && item.indicatorList?.length">
                <List slot="content">
                  <ListItem v-for="(i, index) in item.indicatorList" :key="i.oneDesc">
                    <div style="flex: 1">
                      <div>
                        <span>{{ index + 1 }}、</span>
                        <span style="font-weight: 600;">{{ i.oneDesc }}：</span>
                        <span>{{ i.oneResult }}</span>
                      </div>
                      <template v-if="i.manyList?.length">
                        <Collapse simple>
                          <Panel name="1">
                            <div>{{ i.manyDesc }}</div>
                            <List slot="content">
                              <ListItem v-for="(j) in i.manyList" :key="j">{{ j }}</ListItem>
                            </List>
                          </Panel>
                        </Collapse>
                      </template>
                    </div>
                  </ListItem>
                </List>
              </template>
            </Panel>
          </Collapse>
          <noData v-else style="margin-top: 150px;" />
        </div>
      </div>
      <!-- 完整报告 -->
      <div v-show="tabIndex === '3'" class="index-box report-box">
        <div style="display: flex; flex-direction: row; gap:10px">
          <div class="index-header">
            <p class="total-box"><span>{{ detail.allCount }}</span>项</p>
            <p class="title-box">总指标数</p>
          </div>
          <div class="index-header">
            <p class="total-box"><span style="color: #f59c2c">{{ detail.fxIndicatorCount }}</span>项</p>
            <p class="title-box">风险指标</p>
          </div>
          <div class="index-header">
            <p class="total-box"><span>{{ detail.jfIndicatorCount }}</span>项</p>
            <p class="title-box">加分指标</p>
          </div>
        </div>
        <div class="index-container">
          <Collapse v-if="detail.bgList.length" :value="detail.bgList.map(item => item.itemCode)">
            <Panel v-for="item in detail.bgList" :name="item.itemCode" :key="item.itemCode">
              <span>{{ item.itemName }}项（<span style="color:#f59c2c">{{ item.count }}</span>/{{ item.itemCount
                }}）</span>
              <template v-if="item.indicatorList && item.indicatorList?.length">
                <List slot="content">
                  <ListItem v-for="(i, index) in item.indicatorList" :key="i.oneDesc">
                    <div style="flex: 1">
                      <div>
                        <span>{{ `${index + 1}` }}、</span>
                        <span style="font-weight: 600;">{{ i.oneDesc }}：</span>
                        <span>{{ i.oneResult }}</span>
                        <Tag v-if="i.riskLevel" :color="['#ff2222', '#eb821b', '#f5b726'][i.riskLevel - 1]"
                          style="margin-left: 10px;">
                          {{ i.riskLevelName }}</Tag>
                      </div>
                      <template v-if="i.manyList?.length">
                        <Collapse simple>
                          <Panel name="1">
                            <div>{{ i.manyDesc }}</div>
                            <List slot="content">
                              <ListItem v-for="(j) in i.manyList" :key="j">{{ j }}</ListItem>
                            </List>
                          </Panel>
                        </Collapse>
                      </template>
                    </div>
                  </ListItem>
                </List>
              </template>
            </Panel>
          </Collapse>
          <noData v-else style="margin-top: 150px;" />
        </div>
      </div>
    </div>
    <noData v-else style="margin-top: 250px;" />
  </div>
</template>

<script>
import noData from "@/components/bsp-empty/index.vue";
import api from "../api.js";
export default {
  name: "RiskIndexReport",
  components: { noData },
  props: {
    jgrybm: { type: String, default: "" }
  },
  data() {
    return {
      tabIndex: '1',
      subNavIndex: '0',
      loading: false,
      detail: {
        bgList: [],
      }
    };
  },
  created() {
    this.getReport();
  },
  methods: {
    handleNavClick(index) {
      this.tabIndex = index;
      this.getReport();
    },
    getReport() {
      if (!this.jgrybm) return;
      this.loading = true;
      this.$store
        .dispatch("authGetRequest", {
          url: api.getReport,
          params: { jgrybm: this.jgrybm, type: this.tabIndex },
        })
        .then((res) => {
          this.loading = false;
          if (res.success) {
            Object.assign(this.detail, res.data);
          } else {
            this.$Message.warning("风险评估报告获取失败");
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.riskIndexReport {
  .nav-box {
    margin: 5px auto 10px;
    padding: 3px 15px;
    border-radius: 2em;
    background-color: #e0e7ef;
    display: flex;
    flex-direction: row;
    width: 80%;
    justify-content: space-around;
    gap: 8px;

    .nav-item {
      padding: 0.25em 1em;
      font-weight: bold;
      font-size: 14px;
      cursor: pointer;
      user-select: none;

      &.active {
        border-radius: 1em;
        background: #f7fbfe;
        color: #f59c2c;
      }
    }
  }

  .index-box {
    flex: 1;

    &.report-box {
      >div .index-header {
        flex: 1;
        flex-direction: column;
        text-align: center;

        p.total-box {
          margin-bottom: 3px;
          font-size: 13px;

          span {
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }

    .index-header {
      border-radius: 0.5em;
      background: linear-gradient(to right, #e9e5f6, #e9e5f6, #dde5f9, #e2ebfa, #eff4fb);
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 14px;
      padding: 7px 15px;
      margin-bottom: 10px;

      .header-tips {
        font-weight: bold;
      }

      .subnav-box {
        display: flex;
        flex: 1;
        flex-direction: row;
        justify-content: space-between;
        padding-top: 2px;
        padding-bottom: 2px;
        border-bottom: 2px solid transparent;

        .subnav-item {
          cursor: pointer;
          user-select: none;

          &.active {
            color: #2b5fd9;
            font-weight: bold;
            border-bottom: 2px solid #2b5fd9;
          }
        }
      }
    }

    .index-container {
      min-height: 428px;
      max-height: 528px;
      overflow-y: auto;

      /deep/ .ivu-collapse {
        border-left: none;
        border-right: none;
        border-bottom: none;
      }

      /deep/ .ivu-collapse-item {
        margin-bottom: 15px;
        border: 1px solid #dcdee2;
        border-top: none;
      }

      /deep/ .ivu-collapse-header {
        background-color: #eef2fb;
        border-top: 1px solid #dcdee2;
        font-weight: bold;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }

      /deep/ .ivu-collapse-simple .ivu-collapse-header {
        background-color: unset;
        border-top: none;
      }

      /deep/ .ivu-collapse-simple .ivu-collapse-item-active .ivu-collapse-header {
        border-bottom: 1px solid #dcdee2;
      }

      /deep/ .ivu-collapse-content,
      /deep/ .ivu-collapse-content-box {
        padding: 0;
      }

      /deep/ .ivu-list-item {
        padding: 8px;
      }
    }
  }
}
</style>
