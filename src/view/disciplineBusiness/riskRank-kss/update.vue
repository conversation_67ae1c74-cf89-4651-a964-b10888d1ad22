<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      风险评估登记
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
        <Record :jgrybm="routerData.jgrybm" :id="routerData.id"
          style="flex: 1; display: flex; flex-direction: column" />
      </div>
      </Col>
      <Col :span="9" style="border-left: 1px solid #efefef;">
      <RiskIndexReport :key="routerData.jgrybm" :jgrybm="routerData.jgrybm" />
      </Col>
      <Col :span="9" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
        <div>
          <div class="list-title">风险评估登记</div>
          <div style="border: 1px solid #efefef; padding: 10px">
            <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" label-position="left"
              @submit.native.prevent>
              <Row>
                <Col :span="8">
                <FormItem label="评估类型" prop="riskType">
                  <s-dicgrid v-model="formData.riskType" dicName="ZD_GJ_FXPG_TYPE" disabled :isSearch="false"
                    :multiple="false" />
                </FormItem>
                </Col>
                <Col :span="16">
                <FormItem style="margin-left: 15px" label="评估风险等级" prop="useSituation">
                  {{ formData.riskLevelName }}
                </FormItem>
                </Col>
                <Col :span="24">
                <FormItem class="fixStyle" label="评估理由" prop="assmtReasons">
                  <el-tabs tab-position="left" style="
                              height: 350px;
                              border: 1px solid #efefef;
                              border-radius: 0.5em;
                            ">
                    <el-tab-pane v-for="item in dict.level" :key="item.name">
                      <div style="display: flex; align-items: center; justify-content: space-between;gap:10px"
                        slot="label">
                        <div :class="'tab-item-level_' + item.code"
                          style="line-height: 24px; padding: 5px; font-size: 12px; color: #fff; transform: scale(0.8);border-radius: 0.2em">
                          {{ item.name.substring(0, 2) }}</div>{{ item.name }}
                      </div>
                      <CheckboxGroup v-model="formData.assmtReasons">
                        <Row v-for="dic in dict.content?.filter(
                          (d) => d.code.slice(0, 1) === item.code
                        )" :key="dic.code">
                          <Col :span="24">
                          <Checkbox :label="dic.code">{{
                            dic.name
                          }}</Checkbox>
                          </Col>
                        </Row>
                      </CheckboxGroup>
                    </el-tab-pane>
                  </el-tabs>
                </FormItem>
                </Col>
                <Col :span="24">
                <FormItem label="具体评估理由" prop="specificAssmtReason">
                  <Input v-model="formData.specificAssmtReason" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                    placeholder="请填写具体评估理由"></Input>
                </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="handleCancel">返 回</Button>
      <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { Record, RiskIndexReport } from "./components/index.js";
import { mapActions } from "vuex";

export default {
  components: {
    Record,
    RiskIndexReport
  },
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
        jgryxm: "",
        id: "",
        type: '0'
      },
      formData: {
        riskType: "",
        riskLevel: 0,
        riskLevelName: "",
        specificAssmtReason: "",
        id: "",
        jgrybm: "",
        jgryxm: "",
        assmtReasons: [],
        assmtReason: "",
        oldRiskLevel: 0,
      },
      dict: {
        level: [],
        content: [],
      },
      dictCodeObj: {},
      ruleValidate: {
        assmtReasons: [
          {
            required: true,
            type: "array",
            message: "该项为必填项",
            trigger: "change",
          },
        ],
        riskType: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        specificAssmtReason: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    "formData.assmtReasons": {
      handler() {
        if (this.formData.assmtReasons.length === 0) {
          return;
        }
        const order = this.formData.assmtReasons
          .map((item) => item.slice(0, 1))
          .sort()[0];
        const result = this.dict.level.find((item) => item.code === order);
        this.formData.riskLevel = result.code;
        this.formData.riskLevelName = result.name;
      },
      deep: true,
    },
  },
  created() {
    this.getDict();
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    getDict() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_GJ_FXPG_LEVEL" },
      }).then((res) => {
        if (res.success) {
          this.dict.level = res.data;
        } else {
          this.$Message.error("风险评估-评估等级字典获取失败");
        }
      });
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "GJ_FXPG_KSS_REASON" },
      }).then((res) => {
        if (res.success) {
          this.dict.content = res.data;
          res.data.forEach((item) => {
            this.$set(this.dictCodeObj, item.code, item.name);
          });
        } else {
          this.$Message.error("风险评估-看守所-评估理由字典获取失败");
        }
      });
    },
    getDetail() {
      if (!this.routerData.id) {
        this.formData.jgrybm = this.routerData.jgrybm;
        this.formData.jgryxm = this.routerData.jgryxm;
        this.formData.riskType = this.routerData?.riskType;
        return;
      }
      this.loading = true;
      this.authGetRequest({
        url: api.needDetail,
        params: {
          id: this.routerData.id,
        },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.formData.jgrybm = res.data?.jgrybm;
          this.formData.jgryxm = res.data?.jgryxm;
          this.formData.riskType = res.data?.riskType;
        }
      });
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$router.replace(this.routerData.type === '1' ? { name: "transJailRoomList" } : { name: "riskRankList" });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.formData.assmtReason = this.formData.assmtReasons
            .map((item) => item)
            .join(",");
          const params = { ...this.formData };
          params.riskAssmtTodoId = this.routerData.id;
          this.authPostRequest({
            url: api.create,
            params,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("已成功登记");
              this.$router.replace(this.routerData.type === '1' ? { name: "transJailRoomList" } : { name: "riskRankList" });
            } else {
              this.errorModal({ content: res.msg || "登记失败" });
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}

/deep/ .ivu-select-dropdown {
  z-index: 1000;
}

.fixStyle {
  /deep/ .ivu-form-item-label {
    float: none;
    display: inline-block;
  }

  /deep/ .ivu-form-item-content {
    margin-left: 0 !important;
  }

  /deep/ .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }

  /deep/ .el-tabs__item.is-left {
    padding-left: 5px;
  }

  .tab-item-level_1 {
    background-color: #ff2222;
  }

  .tab-item-level_2 {
    background-color: #ffae00;
  }

  .tab-item-level_3 {
    background-color: #44ca13;
  }

  .tab-item-level_4 {
    background-color: #1a6aff;
  }

  /deep/ .el-tabs__content {
    background: #f5f9fc;
    padding: 10px;
    height: 100%;
  }
}
</style>
