<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
        text-align: left;
      ">
      风险评估登记
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <ChoosePeople @selectUser="selectUser" :selectedIds="choosenList.map(item => { return item.jgrybm }).join(',')">
        </ChoosePeople>
        <Table style="margin-top: 15px" :columns="[
          { title: '姓名', key: 'jgryxm' },
          { title: '监室', key: 'roomName' },
          { title: '操作', key: 'action', slot: 'action', width: '150px' },
        ]" :data="choosenList" :row-class-name="rowClassName">
          <template slot-scope="{ row, index }" slot="action">
            <span style="cursor: pointer;color: #e60012; font-size: 14px;" @click="removeChoosenPeople(index)">删除</span>
            <span style="cursor: pointer;color: #2b5fd9; font-size: 14px;margin-left: 10px;"
              @click="jgrybm = row.jgrybm">查看报告{{ jgrybm === row.jgrybm ? ' >' : '' }}</span>
          </template>
        </Table>
      </div>
      </Col>
      <Col :span="9" style="border-left: 1px solid #efefef;">
      <RiskIndexReport :key="jgrybm" :jgrybm="jgrybm" />
      </Col>
      <Col :span="9" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
        <div class="form-title" style="
              text-align: center;
              background: #f5f7fa;
              line-height: 2.2;
              border: 1px solid #d7d7d7;
              border-top-left-radius: 0.2em;
              border-top-right-radius: 0.2em;
            ">
          风险评估登记
        </div>
        <div class="form-content" style="
              flex: 1;
              padding: 15px;
              border: 1px solid #d7d7d7;
              border-top: unset;
              border-bottom-left-radius: 0.2em;
              border-bottom-right-radius: 0.2em;
            ">
          <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" label-position="left"
            @submit.native.prevent>
            <Row>
              <Col :span="12">
              <FormItem label="评估类型" prop="riskType">
                <s-dicgrid v-model="formData.riskType" dicName="ZD_GJ_FXPG_TYPE" :isSearch="false" :multiple="false" />
              </FormItem>
              </Col>
              <Col :span="12">
              <FormItem style="margin-left: 15px" label="评估风险等级" prop="useSituation">
                {{ formData.riskLevelName }}
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem class="fixStyle" label="评估理由" prop="assmtReasons">
                <el-tabs tab-position="left" style="
                            height: 350px;
                            border: 1px solid #efefef;
                            border-radius: 0.5em;
                          ">
                  <el-tab-pane v-for="item in dict.level" :key="item.name">
                    <div style="display: flex; align-items: center; justify-content: space-between;gap:10px"
                      slot="label">
                      <div :class="'tab-item-level_' + item.code"
                        style="line-height: 24px; padding: 5px; font-size: 12px; color: #fff; transform: scale(0.8);border-radius: 0.2em">
                        {{ item.name.substring(0, 2) }}</div>{{ item.name }}
                    </div>
                    <CheckboxGroup v-model="formData.assmtReasons">
                      <Row v-for="dic in dict.content?.filter(
                        (d) => d.code.slice(0, 1) === item.code
                      )" :key="dic.code">
                        <Col :span="24">
                        <Checkbox :label="dic.code">{{
                          dic.name
                        }}</Checkbox>
                        </Col>
                      </Row>
                    </CheckboxGroup>
                  </el-tab-pane>
                </el-tabs>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="具体评估理由" prop="specificAssmtReason">
                <Input v-model="formData.specificAssmtReason" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写具体评估理由"></Input>
              </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div style="display: flex; justify-content: flex-end; padding: 10px 0">
          <Button style="margin-right: 15px" @click="handleCancel">取 消</Button>
          <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
        </div>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import api from "./api.js";
import { ChoosePeople, RiskIndexReport } from "./components";
import { mapActions } from "vuex";
export default {
  components: {
    ChoosePeople,
    RiskIndexReport
  },
  data() {
    return {
      formData: {
        riskType: "",
        riskLevel: 0,
        riskLevelName: "",
        specificAssmtReason: "",
        id: "",
        jgrybm: "",
        jgryxm: "",
        assmtReasons: [],
        assmtReason: "",
        oldRiskLevel: 0,
      },
      choosenList: [],
      jgrybm: "",
      dict: {
        level: [],
        content: [],
      },
      dictCodeObj: {},
      ruleValidate: {
        assmtReasons: [
          {
            required: true,
            type: "array",
            message: "该项为必填项",
            trigger: "change",
          },
        ],
        riskType: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        specificAssmtReason: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
      },
      loading: false,
    };
  },
  watch: {
    "formData.assmtReasons": {
      handler() {
        if (this.formData.assmtReasons.length === 0) {
          return;
        }
        const order = this.formData.assmtReasons
          .map((item) => item.slice(0, 1))
          .sort()[0];
        const result = this.dict.level.find((item) => item.code === order);
        this.formData.riskLevel = result.code;
        this.formData.riskLevelName = result.name;
      },
      deep: true,
    },
  },
  created() {
    this.getDict();
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    getDict() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_GJ_FXPG_LEVEL" },
      }).then((res) => {
        if (res.success) {
          this.dict.level = res.data;
        } else {
          this.$Message.error("风险评估-评估等级字典获取失败");
        }
      });
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "GJ_FXPG_KSS_REASON" },
      }).then((res) => {
        if (res.success) {
          this.dict.content = res.data;
          res.data.forEach((item) => {
            this.$set(this.dictCodeObj, item.code, item.name);
          });
        } else {
          this.$Message.error("风险评估-看守所-评估理由字典获取失败");
        }
      });
    },
    selectUser(data) {
      const uniqueArray = new Set(data.map(item => JSON.stringify(item)));
      const resultArray = Array.from(uniqueArray).map(item => JSON.parse(item));
      this.choosenList = resultArray;
      this.jgrybm = this.choosenList[0]?.jgrybm;
    },
    removeChoosenPeople(index) {
      this.choosenList.splice(index, 1);
      this.jgrybm = this.choosenList[0]?.jgrybm;
    },
    rowClassName(row) {
      if (row.jgrybm === this.jgrybm) {
        return 'table-select-row';
      }
      return '';
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$router.replace({ name: "riskRankList" });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid && this.choosenList.length) {
          this.loading = true;
          this.formData.assmtReason = this.formData.assmtReasons
            .map((item) => item)
            .join(",");
          let params = this.choosenList.map((item) => {
            return {
              ...this.formData,
              ...item,
            };
          });

          this.authPostRequest({
            url: api.createBatch,
            params,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("登记成功");
              this.$refs.form.resetFields();
              this.$router.replace({ name: "riskRankList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.fixStyle {
  /deep/ .ivu-form-item-label {
    float: none;
    display: inline-block;
  }

  /deep/ .ivu-form-item-content {
    margin-left: 0 !important;
  }

  /deep/ .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }

  /deep/ .el-tabs__item.is-left {
    padding-left: 5px;
  }

  .tab-item-level_1 {
    background-color: #ff2222;
  }

  .tab-item-level_2 {
    background-color: #ffae00;
  }

  .tab-item-level_3 {
    background-color: #44ca13;
  }

  .tab-item-level_4 {
    background-color: #1a6aff;
  }

  /deep/ .el-tabs__content {
    background: #f5f9fc;
    padding: 10px;
    height: 100%;
  }
}

/deep/ .table-select-row td {
  background: #1a6aff10;
}
</style>
