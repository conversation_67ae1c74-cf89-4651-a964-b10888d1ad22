import { acpCom, bspUacRoot } from "@/path/base.js";
export default {
  // 风险评估-获取详情
  detail: acpCom + "/acp/gj/gjRiskAssmt/get",
  // 风险评估-获取待评估详情
  needDetail: acpCom + "/acp/gj/gjRiskAssmt/getTodoDetailById",
  // 风险评估-新增风险评估
  create: acpCom + "/acp/gj/gjRiskAssmt/create",
  // 风险评估-评估登记
  update: acpCom + "/acp/gj/gjRiskAssmt/update",
  // 风险评估-批量新增风险评估
  createBatch: acpCom + "/acp/gj/gjRiskAssmt/createBatch",
  // 风险评估-风险评估记录
  historyList: acpCom + "/acp/gj/gjRiskAssmt/getGjRiskAssmtRespVOByJgrybm",
  // 风险评估-获取列表
  needToCheckList: bspUacRoot + "/com/datagrid/getQueryPageData",
  // 风险评估-获取报告
  getReport: acpCom + "/acp/gj/riskIndicator/getReportByJgrybm",
  // 【领导岗】风险评估-单个审批
  approve: acpCom + "/acp/gj/gjRiskAssmt/approve",
  // 【领导岗】风险评估-批量审批
  batchApprove: acpCom + "/acp/gj/gjRiskAssmt/batchApprove",
};
