<!--表单  -->
<template>
    <div class="fm-content-wrap" style="width: 100%;">
        <p class="title-blue" >{{ modelTitle }}</p>
        <fm :showSave="false" :ref="fmParams.tagId" :parameter="fmParams" v-if="fmParams.load && rowData.type!='detail'"
            :formMountId="fmParams.tagId" />
        <FmDeatil :showSave="false" :ref="fmParams.tagId" :parameter="fmParams" v-if="fmParams.load && rowData.type=='detail'" 
            :formMountId="fmParams.tagId" />
        <div class='bsp-base-fotter'>
            <Button @click='goBack'>返 回</Button>
            <Button @click="saveData" type="primary" v-if="rowData.type!='detail'" :loading='loading'>保 存</Button>
        </div>
    </div>
</template>
<script>

import fm from "@/components/fm/component/fm.vue";
import FmDeatil from "@/components/fm/component/fmDeatil.vue";
import { removeEmptyFields } from '@/libs/util'
export default {
    props: {
        modelTitle: String,
        rowData: Array,
        indicatorTypeCode: String,
        params: Object,
        rowData:Object,
    },
    components: { fm,FmDeatil },
    data() {
        return {
            loading: false,
            formData: {},
            fmParams: {
                mark: serverConfig.APP_CODE + ':fxpg-fxxsxmbbd',//'1953283412538298368',//'1899019819911614464',
                operType: this.rowData && this.rowData.id ?(this.rowData.type=='detail'?'2':'1')  : '0',  //0为新增 1为修改
                businessId: this.rowData && this.rowData.id ? this.rowData.id : '',
                load: true,
                tagId: 'fxpg',
                indicatorTypeCode: this.params.itemCode,
                isEnabled: 1
            }

        }

    },
    methods: {
        async saveData() {
            let childVue = this.$refs.fxpg.childVue
            const valid = await childVue.$refs.formData.validate();
            if (valid) {
                Object.assign(this.formData, removeEmptyFields(childVue.formData))
                this.submit()
            }
            console.log(childVue, this.formData, valid)
        },
        submit() {
            this.$store.dispatch('authPostRequest', {
                url:  this.formData.id?this.$path.acp_riskIndicator_update:this.$path.acp_riskIndicator_create,
                params: this.formData
            }).then(resp => {
                if (resp.code == 0) {
                    this.$Message.success('保存成功!');
                    this.goBack();
                } else {
                    this.$Message.error(resp.msg);
                }
            })
        },
        goBack() {
            this.$emit('goBack')
        }
    }
}
</script>