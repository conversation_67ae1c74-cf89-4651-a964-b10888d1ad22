<template>
    <div class="riskConfiguration-wrap">
        <div style="width: 280px;margin:0 10px;" v-if="!showData">
            <configItemTree @selectChange="handleListChange" :treeData="treeData" @addNode="addNode"
                @editNode="editNode" @deleteNode="deleteNode"></configItemTree>
        </div>
        <rs-DataGrid ref="grid" funcMark="fxxpz" :customFunc="false" :params="params" v-if="!showData && showTab"
            style="width: calc(100% - 300px); padding: 10px 10px 0px 10px">
            <template v-slot:fxxpz:add="{ oper }">
                <Button type="primary" class="header-button" @click.native="handleAdd('add')">
                    <Icon :type="oper.iconPath ? oper.iconPath : 'md-add'" size="20" />
                    {{ oper.name }}
                </Button>
            </template>
            <template v-slot:fxxpz:edit="{ oper, row, index }">
                <Button type="primary" class="row-button" @click="handleEdit(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:fxxpz:info="{ oper, row, index }">
                <Button type="primary" size="small" class="row-button" @click="handleDetails(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:fxxpz:delete="{ oper, row, index }">
                <Button type="error" size="small" class="row-button" @click="deleteNode(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
        </rs-DataGrid>

        <component :is="components" :params="params" @goBack="goBack" :rowData="rowData" v-if="showData"
            :modelTitle="modelTitle" />
    </div>
</template>
<script>
import configItemTree from './configItem.vue';
import addForm from './addForm.vue';
export default {
    components: { configItemTree, addForm },
    data() {
        return {
            treeData: [],
            showData: true,
            showTab: false,
            rowData: {},
            params: {},
            components: null,
            modelTitle: '新增指标模型'
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        goBack() {
            this.showData = false
            this.showTab=true
            this.on_refresh_table()
        },
        on_refresh_table() {
            setTimeout(() => {
                this.$refs.grid.query_grid_data(1)
            },300)
        },
        handleListChange(item, index) {
            this.showTab = false
            this.$set(this, 'params', index[0])
            setTimeout(() => {
                this.showTab = true
            }, 200)

        },
        handleAdd() {
            this.rowData ={}
            this.components = 'addForm'
            this.showData = true
            this.$set(this.rowData,'type','add')
            this.modelTitle = '新增指标模型'

        },
        handleEdit(row) {
            this.rowData = row
            this.modelTitle = '编辑指标模型'
            this.components = 'addForm'
            this.$set(this.rowData,'type','edit')
            this.showData = true

        },
        handleDetails(row) { 
            this.rowData = row
            this.$set(this.rowData,'type','detail')
            this.modelTitle = '指标模型详情'
            this.components = 'addForm'
            this.showData = true
        },
        addNode(node) {
            // this.showIndicatorForm = true
            // this.indicatorForm.riskModelId = this.params.riskModelId
            // this.indicatorForm.parentId = this.params.typeId ? this.params.typeId : ''
        },
        editNode(node) {
            // this.showIndicatorForm = true
            this.authGetRequest({
                url: this.$path.fjcy_indicator_get,
                params: { id: this.params.id }
            }).then(resp => {
                if (resp.success && resp.data) {
                    // this.indicatorForm = resp.data
                }
            })
        },
        deleteNode(node) {
            this.$Modal.confirm({
                title: '是否确认删除？',
                loading: true,
                onOk: async () => {
                    this.authGetRequest({
                        url: this.$path.acp_riskIndicator_delete,
                        params: { ids: this.params.id }
                    }).then(resp => {
                        if (resp.success && resp.data) {
                            this.$Modal.remove()
                            this.on_refresh_table()
                        }
                    })
                }
            })
        },
        getData() {
            this.$store.dispatch('authGetRequest', {
                url: this.$path.acp_riskIndicator_getBjgryfxzbmo,
                params: {}
            }).then(resp => {
                if (resp.success && resp.data) {
                    this.treeData = resp.data
                    if (this.treeData && this.treeData.length > 0) {
                        this.$set(this, 'params', this.treeData[0])
                    }
                    this.showTab = true
                    this.showData = false
                    console.log(this.treeData, 'this.treeDatathis.treeData')

                }
            })
            // 
        }
    }

}
</script>
<style scoped lang="less">
.riskConfiguration-wrap {
    width: 100%;
    display: flex;
}
</style>