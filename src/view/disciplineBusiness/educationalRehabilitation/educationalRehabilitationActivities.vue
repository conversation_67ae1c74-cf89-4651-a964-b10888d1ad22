<template>
    <div>
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid" funcMark="kfjyhd" :customFunc="true">
                <!-- 设备报修登记 -->
                <template slot="customHeadFunc" slot-scope="{ func }">
                    <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':kfjyhd:add')"
                        @click.native="handleAddhd('add')">新增活动</Button>
                </template>
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button type="primary" style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':kfjyhd:bj')"
                        @click.native="handleBjhd(index, row)">编辑</Button>
                    <Button style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':kfjyhd:xq')"
                        @click.native="handleXqhd(index, row)">详情</Button>
                </template>
            </s-DataGrid>
        </div>
        <div v-if="addhdContainer">
            <Form ref="form" :model="form" :label-width="150">
                <Row>
                    <Col span="23">
                    <FormItem label="活动时间" prop="activityTime"
                        :rules="{ required: true, type: 'date', message: '请选择活动时间', trigger: 'change', validator: timeValidator }">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss" :value="form.activityTime"
                            style="width: auto;" />
                    </FormItem>
                    </Col>
                    <Col span="23">
                    <FormItem label="康复民警" prop="rehabPoliceSfzh"
                        :rules="{ required: true, message: '检查民警不能为空', trigger: 'blur, change' }">
                        <user-selector v-model="form.rehabPoliceSfzh" tit="用户选择" :text.sync="form.rehabPolice"
                            returnField="idCard" numExp='num==1' msg="至少选中1人"></user-selector>
                    </FormItem>
                    </Col>

                    <Col span="23">
                    <FormItem label="活动主题" prop="activityTopic"
                        :rules="{ required: true, message: '活动主题不能为空', trigger: 'blur, change' }">
                        <Select v-model="form.activityTopic">
                            <Option v-for="item in isAddKbList" :value="item.code" :key="item.code">{{
                                item.name }}
                            </Option>
                        </Select>
                    </FormItem>
                    </Col>

                    <Col span="23">
                    <FormItem label="参加人员" prop="jgryxm	"
                        :rules="{ required: true, message: '参加人员不能为空', trigger: 'blur, change', type: 'string', validator: handleValidator }">
                        <Input v-model="form.jgryxm" @on-focus="openModal = true" />
                    </FormItem>
                    </Col>
                    <Col span="23" v-if="jgryList.length > 0">
                    <p class="chioced-people">已选：{{ jgryList.length }}人</p>
                    <div class="max-cover-jgryInfos">
                        <div v-for="(item, index) in jgryList" :key="index" class="cover-jgryInfos">
                            <Icon type="ios-close" style="position: absolute;right: -7px;top: -11px;font-size: 24px;"
                                @click="handleDel(index)" />
                            <div class="img-front">
                                <img v-if="item.frontPhoto" :src="item.frontPhoto">
                                <img v-else src="../../../assets/images/main.png" alt="">
                            </div>
                            <div class="title-container">
                                <p class="xm-info">
                                    <span>{{ item.xm }}</span>
                                    <Icon v-if="item.xb == 1" type="ios-male" style="color: #5cadff;" />
                                    <Icon v-if="item.xb == 2" type="ios-female" style="color: #FFB6C1;" />
                                </p>
                                <p class="">
                                    <span>{{ item.roomName }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    </Col>
                    <Col span="23">
                    <FormItem label="活动详情" prop="activityDetails"
                        :rules="{ required: false, message: '请输入情况记录', trigger: 'blur' }">
                        <Input v-model="form.activityDetails" placeholder="请输入活动详情" type="textarea"
                            :autosize="{ minRows: 2, maxRows: 5 }"></Input>
                    </FormItem>
                    </Col>

                    <Col span="23">

                    <FormItem label="附件" prop="attUrl">
                        <div v-if="this.updateId">
                            <!-- <file-upload :defaultList="xpscdzUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                                :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                                @fileComplete="fileCompleteFileCertUrl" :isDetail="true" /> -->

                            <file-upload :defaultList="evidenceUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                                :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                                @fileComplete="fileCompleteFileCertUrl" />
                        </div>
                        <div v-else>
                            <file-upload :defaultList="evidenceUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                                :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                                @fileComplete="fileCompleteFileCertUrl" />
                        </div>
                    </FormItem>
                    </Col>
                </Row>
            </Form>
            <div class="bsp-base-fotter">
                <Button @click="handleReset('form')" style="margin-right: 10px;">取消</Button>
                <Button type="primary" @click="handleSubmit('form')">确认</Button>
            </div>
            <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal"
                width="1360" title="人员列表">
                <div class="select-use">
                    <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple="true"
                        :selectUseIds="selectUseIds" />
                </div>
                <div slot="footer">
                    <Button type="primary" @click="useSelect" class="save">确 定</Button>
                    <Button @click="openModal = false" class="save">关 闭</Button>
                </div>
            </Modal>
        </div>
        <div v-if="detailContainer">
            <Form ref="formData" inline>
                <div class="fm-content-box">
                    <p class="fm-content-wrap-title">
                        <Icon type="md-list-box" size="24" color="#2b5fda" />康复活动详情
                    </p>
                    <Row>
                        <Col span="3" class="col-title"><span>活动名称</span></Col>
                        <Col span="9"><span>
                            {{ detailInfo.activityTopicName }}
                        </span>
                        </Col>
                        <Col span="3" class="col-title"><span>活动时间</span></Col>
                        <Col span="9"><span>
                            {{ detailInfo.activityTime }}
                        </span></Col>
                    </Row>

                    <Row>
                        <Col span="3" class="col-title"><span>康复民警</span></Col>
                        <Col span="9"><span>
                            {{ detailInfo.rehabPolice }}
                        </span>
                        </Col>
                        <Col span="3" class="col-title"><span></span></Col>
                        <Col span="9"><span>

                        </span></Col>
                    </Row>

                    <Row>
                        <Col span="3" class="col-title"><span>活动详情</span></Col>
                        <Col span="21"><span>
                            {{ detailInfo.activityDetails }}
                        </span>
                        </Col>
                    </Row>


                    <Row>
                        <Col span="3" class="col-title"><span>参加人员</span></Col>
                        <Col span="21"><span>
                            <div class="max-cover-jgryInfos" style="margin-left: 10px;margin-top: 10px;">
                                <div v-for="(item, index) in jgryList" :key="index" class="cover-jgryInfos">
                                    <div class="img-front">
                                        <img v-if="item.frontPhoto" :src="item.frontPhoto">
                                        <img v-else src="../../../assets/images/main.png" alt="">
                                    </div>
                                    <div class="title-container">
                                        <p class="xm-info">
                                            <span>{{ item.xm }}</span>
                                            <Icon v-if="item.xb == 1" type="ios-male" style="color: #5cadff;" />
                                            <Icon v-if="item.xb == 2" type="ios-female" style="color: #FFB6C1;" />
                                        </p>
                                        <p class="">
                                            <span>{{ item.roomName }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </span>
                        </Col>
                    </Row>

                </div>
            </Form>
            <div class="bsp-base-fotter">
                <Button @click="handleBack" style="margin-right: 10px;">返回</Button>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex'
import { fileUpload } from 'sd-minio-upfile'
import { sDataGrid } from 'sd-data-grid'
import { userSelector } from 'sd-user-selector'
import { prisonSelect } from "sd-prison-select";
import { getCurrentTimeFormatted } from '@/libs/util'
export default {
    name: "educationalRehabilitationActivities",
    data() {

        return {
            tableContainer: true,
            addhdContainer: false,
            detailContainer: false,
            evidenceUrl: [],
            xpscdzUrl: [],
            form: {
                activityDetails: "",
                activityTime: "",
                rehabPoliceSfzh: "",
                rehabPolice: "",
                activityTopic: "",
                jgrybm: "",
                jgryxm: "",
                attUrl: "",
                participantCount: 0
            },
            selectUseIds: "",
            openModal: false,
            isAddKbList: [],
            jgryList: [],
            jgryxmArr: [],
            jgrybmArr: [],
            updateId: "",
            detailInfo: {},
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
        }

    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleAddhd() {
            this.addhdContainer = true
            this.tableContainer = false
        },
        handleBjhd(idx, { id }) {
            this.authGetRequest({ url: this.$path.edurehabActivity_get, params: { id } }).then(res => {
                if (res.success) {
                    this.form = res.data
                    this.jgryList = res.data.jgryxxList
                    this.form.participantCount = res.data.jgryxxList.length
                    if (this.form.attUrl) {
                        this.xpscdzUrl = JSON.parse(this.formData.attUrl)
                    }
                    this.tableContainer = false
                    this.addhdContainer = true
                    this.updateId = res.data.id
                }
            })
        },
        handleValidator(rule, value, callback) {
            const trimmed = this.form.jgryxm ? this.form.jgryxm.trim() : '';
            if (!trimmed) {
                callback(new Error('参加人员不能为空'));
            } else if (trimmed.split(',').every(name => !name.trim())) {
                callback(new Error('至少输入一个有效姓名'));
            } else {
                callback();
            }
        },
        timeValidator(rule, value, callback) {
            const valStr = typeof value === 'string'
                ? value
                : this.dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            if (!valStr) callback(new Error("时间不能为空"));
            else callback();
        },
        handleXqhd() { },
        handleReset(name) {
            this.handleCommRest(name)
        },
        beforeUpload() { },
        fileSuccessFile() { },
        fileRemoveFile() { },
        fileCompleteFileCertUrl(data) {
            this.evidenceUrl = data
            this.$set(this.formData, 'attUrl', JSON.stringify(data))
        },
        handleCommRest(name) {
            this.addhdContainer = false
            this.tableContainer = true
            this.jgryList = []
            this.updateId = ""
            this.$refs[name].resetFields();
            this.form = {}
        },
        handleSubmit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.form.participantCount = this.jgryList.length
                    this.form.activityTime = this.dayjs(this.form.activityTime).format('YYYY-MM-DD HH:mm:ss')
                    let url = ""
                    let msg = ""
                    if (this.updateId) {
                        url = this.$path.edurehabActivity_update
                        msg = "更新成功"
                        delete this.form.jgryxxList
                    } else {
                        url = this.$path.edurehabActivity_createa
                        msg = "添加成功"
                    }
                    this.authPostRequest({ url, params: { ...this.form } }).then(res => {
                        if (res.success) {
                            this.handleCommRest(name)
                            this.$Message.success(msg)

                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        fileSuccess() {

        },
        useSelect() {
            let userInfos = this.$refs.prisonSelect.checkedUse
            this.jgryList = userInfos;

            this.form.jgryxm = userInfos.map(item => { return item.xm }).join(',')
            this.form.jgrybm = userInfos.map(item => { return item.jgrybm }).join(',')

            this.jgryxmArr = userInfos.map(item => { return item.xm })
            this.jgrybmArr = userInfos.map(item => { return item.jgrybm })
            this.openModal = false
        },
        handleGetZD_PSY_ZQLX() {
            this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_KFJYHD_HDZT.js" }).then(res => {
                let timeType = eval('(' + res + ')')
                this.isAddKbList = timeType()
            })
        },
        handleDel(idx) {
            this.jgryList.splice(idx, 1)
            this.jgryxmArr.splice(idx, 1)
            this.jgrybmArr.splice(idx, 1)
            this.$set(this.form, 'jgryxm', this.jgryxmArr.join(','))
            this.$set(this.form, 'jgrybm', this.jgrybmArr.join(','))
        },
        handleXqhd(idx, { id }) {
            this.authGetRequest({ url: this.$path.edurehabActivity_get, params: { id } }).then(res => {
                if (res.success) {
                    this.detailInfo = res.data
                    this.jgryList = res.data.jgryxxList
                    this.tableContainer = false
                    this.detailContainer = true
                }
            })
        },
        handleBack() {
            this.tableContainer = true
            this.detailContainer = false
        },
    },

    components: {
        fileUpload,
        sDataGrid,
        userSelector,
        prisonSelect
    },

    created() {
        this.handleGetZD_PSY_ZQLX()
        this.form.activityTime = getCurrentTimeFormatted()
    },
    mounted() {
        // 使用 Day.js 生成格式化字符串
        // this.form.activityTime = this.dayjs().format("YYYY-MM-DD HH:mm:ss");
    },

    computed: {},

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

//
/deep/.fm-content-wrap-title {
    border-bottom: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 16px;
    color: #00244A;
    /* margin-bottom: 16px; */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #CEE0F0;
}

.chioced-people {
    margin-left: 75px;
    margin-bottom: 10px;
}

.max-cover-jgryInfos {
    margin-left: 75px;
    margin-bottom: 10px;
    display: flex;
    flex-flow: wrap;

    .cover-jgryInfos {
        border: solid 1px #dcdee2;
        padding: 15px;
        position: relative;
        display: flex;
        margin-right: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
        justify-content: space-between;

        .img-front {
            img {
                width: 80px;
                height: 100px;
            }
        }

        .title-container {
            margin-left: 10px;

            .xm-info {
                position: relative;

                span {
                    font-size: larger;
                    font-weight: 700;
                }

                .ivu-icon {
                    position: absolute;
                    right: -12px;
                    top: -2px;
                }
            }
        }
    }
}
</style>
