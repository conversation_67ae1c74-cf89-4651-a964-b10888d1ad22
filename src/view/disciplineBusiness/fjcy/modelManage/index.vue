<template>
  <div>
    <div class="bsp-base-form">
      <div class="wrap-cont">
        <div style="width: 280px;margin-right: 10px;">
          <modelTypeTree @selectChange="handleListChange" :treeData="treeData" @addNode="addNode" @editNode="editNode" @deleteNode="deleteNode"></modelTypeTree>
        </div>
        <rs-DataGrid ref="grid" funcMark="jfmx:list" :customFunc="true" :params="params" v-if="!showData"
                     style="width: calc(100% - 280px); padding: 10px 10px 0px 10px">
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" :disabled="addDisabled" v-if="func.includes(globalAppCode + ':jfmx:list:add')"
                    @click.native="addRegion('add')">新增
            </Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':jfmx:list:edit')"
                    @click.native="addRegion('edit',row)">编辑
            </Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':jfmx:list:detail')"
                    @click.native="addRegion('detail', row)">详情
            </Button>
            <Button type="error" v-if="func.includes(globalAppCode + ':jfmx:list:delete')"
                    @click.native="deleleChange(row)">删除
            </Button>
          </template>
        </rs-DataGrid>
        
        <!--指标新增修改-->
        <Modal v-model="showIndicatorForm" class-name="select--modal" width="35%" :title="indicatorFormTitle">
          <div class="addOffice-wrap">
            <div class="fm-content-wrap">
              <Form ref="addIndicatorForm" :model="indicatorForm" :label-width="180" :label-colon="true">
                <div>
                  <Row>
                    <Col span="24">
                      <FormItem label="指标名称" prop="name"
                                :rules="[  { trigger: 'change', message: '请填写指标名称', required: true}, ]"
                                style="width: 100%">
                        <Input v-model="indicatorForm.name" maxlength="50"/>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="24">
                      <FormItem label="序号" prop="orderId" >
                        <InputNumber :max="10" :min="1" v-model="indicatorForm.orderId" />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </Form>
            </div>
          </div>
          <div slot="footer" style="text-align: center">
            <Button @click="closeIndicatorEvent">取消</Button>
            <Button type="primary" @click="handleIndicatorAdd()" :loading="loading">保存
            </Button>
          </div>
        </Modal>

        
        <!--模型新增修改-->
        <Modal v-model="showForm" class-name="select--modal" width="60%" :title="formTitle" class="model-form">
          <div class="addOffice-wrap">
            <div class="fm-content-wrap" style="text-align: center;">
              <Form ref="addModelForm" :model="modelForm" :label-width="180" :label-colon="true">
                <div>
                  <Row>
                    <Col span="12">
                      <FormItem label="模型名称" prop="name"
                                :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]"
                                style="width: 100%">
                        <Input v-model="modelForm.name" :disabled="saveType === 'detail'" maxlength="50"/>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="计分类型" prop="scoreType"
                                :rules="[{ trigger: 'blur, change', message: '请选择计分类型', required: true }]">
                        <s-dicgrid v-model="modelForm.scoreType" :disabled="saveType === 'detail'"
                                   style="width: 100%;" dicName="ZD_FJCY_FZLX"/>
                      </FormItem>
                    </Col>
                    <Col span="12" style="text-align: left;">
                      <FormItem label="分值" prop="score"
                                :rules="[{ trigger: 'blur, change', type: 'number', message: '请选择分值', required: true}]">
                        <el-input-number v-model.number="modelForm.score" :disabled="saveType === 'detail'" :min="0"/>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="应用数据源" prop="dbId" style="text-align: left"
                                :rules="[{ trigger: 'blur,change', message: '请选择应用数据源', required: true}]">
                        <Select @on-change="dataSourceChange(modelForm.dbId, modelForm.resType)"
                                placeholder="选择应用数据源"
                                v-model="modelForm.dbId" :disabled="saveType === 'detail'">
                          <Option v-for="item in dataSourceList" :value="item.id" :key="item.id">
                            {{ item.dsName }}
                          </Option>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="资源类型" prop="resType"
                                :rules="[{ trigger: 'blur,change', message: '请选择资源类型', required: true}]">
                        <s-dicgrid v-model="modelForm.resType" style="width: 100%;" dicName="ZD_FJCY_ZYLX"
                                   :disabled="saveType === 'detail'"
                                   @change="dataSourceChange(modelForm.dbId, modelForm.resType)"/>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="资源名称" prop="resId"
                                :rules="[{ trigger: 'blur,change', message: '请选择资源名称', required: true }]">
                        <Select filterable placeholder="选择数据资源" :disabled="saveType === 'detail'"
                                @on-change="handleResChange(modelForm.dbId, modelForm.resId)"
                                v-model="modelForm.resId">
                          <Option v-for="item in resources" :value="item.id" :label="item.resCname" :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="业务主键" prop="keyCol" style="text-align: left"
                                :rules="[{ trigger: 'blur,change', message: '请选择业务主键', required: true }]">
                        <Select placeholder="选择业务主键字段" v-model="modelForm.keyCol"
                                :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="增量字段" prop="incCol" style="text-align: left"
                                :rules="[{ trigger: 'blur,change', message: '请选择增量字段', required: true }]">
                        <Select placeholder="选择增量字段" v-model="modelForm.incCol" :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="增量初始值" prop="incValueDefault"
                                :rules="[{ trigger: 'blur,change', message: '请选择增量初始值', required: true }]">
                        <Input v-model="modelForm.incValueDefault" :disabled="saveType === 'detail'"/>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="业务时间字段" prop="businessDateCol" style="text-align: left"
                                :rules="[{ trigger: 'blur,change', message: '请选择业务时间字段', required: true}]">
                        <Select placeholder="选择业务时间字段" v-model="modelForm.businessDateCol"
                                :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="监所编号字段" prop="orgCodeCol" style="text-align: left"
                                :rules="[{ trigger: 'blur,change', message: '请选择监所编号字段', required: true }]">
                        <Select placeholder="选择监所编号字段" v-model="modelForm.orgCodeCol"
                                :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="12">
                      <FormItem label="监室编号字段" prop="roomCodeCol"
                                :rules="[{ trigger: 'blur,change', message: '请选监室编号字段', required: true}]">
                        <Select filterable placeholder="选择监室编号字段" v-model="modelForm.roomCodeCol"
                                :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="监管人员编号字段" prop="prisonerCodeCol"
                                :rules="[{ trigger: 'blur,change', message: '请选择监管人员编号字段', required: true }]">
                        <Select filterable placeholder="选择监管人员编号字段"
                                v-model="modelForm.prisonerCodeCol" :disabled="saveType === 'detail'">
                          <Option v-for="item in fieldList" :value="item.fieldName" :label="item.fieldName"
                                  :key="item.id"/>
                        </Select>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row>
                    <Col span="24">
                      <FormItem label="规则条件" prop="conditionSql"
                                :rules="[{ trigger: 'blur,change', message: '请填写规则条件', required: true }]"
                                style="align-items: flex-start;margin-inline-start: -21px;width: 107.5%;">
                        <Input v-model="modelForm.conditionSql" type="textarea" :disabled="saveType === 'detail'"/>
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </Form>
            </div>
          </div>
          <div slot="footer" style="text-align: center">
            <Button @click="closeEvent">取消</Button>
            <Button type="primary" v-if="saveType === 'add' || saveType === 'edit'" @click="handleAdd()"
                    :loading="loading">保存
            </Button>
          </div>
        </Modal>
      </div>
    </div>
  </div>
</template>

<script>
import {mapActions} from 'vuex'
import modelTypeTree from '../components/model-type-tree.vue'

export default {
  components: {
    modelTypeTree
  },
  data() {
    return {
      treeData: [],
      showData: false,
      dataTable: [],
      loading: false,
      addDisabled: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        orgCode: this.$store.state.common.orgCode
      },
      total: 0,
      params: {},
      formTitle: '新增模型',
      showForm: false,
      modelForm: {},
      indicatorFormTitle: '新增指标',
      showIndicatorForm: false,
      indicatorForm: {
        orderId: 1
      },
      saveType: '',
      dataSourceList: [],
      resources: [],
      fieldList: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    addRegion(type, row) {
      this.saveType = type
      if (type === 'add') {
        this.modelForm = {}
        this.$set(this.modelForm, 'riskModelId', this.params.riskModelId)
        this.$set(this.modelForm, 'indicatorTypeId', this.params.typeId)
        this.$set(this.modelForm, 'indicatorId', this.params.indicatorId)
        this.getDataSourceList()
        this.resources = []
        this.fieldList = []
        this.resId = true
        this.showForm = true
      } else if (type === 'edit' || type === 'detail') {
        if (type === 'detail') {
          this.formTitle = '详情'
        } else {
          this.formTitle = '编辑模型'
        }
        this.getDataSourceList()
        this.getModelData(row.id)
        this.getResourcesList(this.modelForm.dbId, this.modelForm.resType)
        let count = 0;
        let dbId = ''
        let resId = ''
        const execute = () => {
          if (count < 3 && !resId) {
            dbId = this.modelForm.dbId
            resId = this.modelForm.resId
            this.getFields(dbId, resId)
            this.showForm = true
            count++;
            setTimeout(execute, 100);
          }
        };
        setTimeout(execute, 300);
      }

    },
    handleAdd() {
      this.loading = true
      this.resources.forEach(item => {
        if (item.id === this.modelForm.resId) {
          this.modelForm.resName = item.resCname
        }
      })
      this.$refs['addModelForm'].validate((valid) => {
        if (valid) {
          this.authPostRequest({
            url: this.$path.fjcy_model_create,
            params: this.modelForm
          }).then(res => {
            if (res.success) {
              this.closeEvent()
              this.$Message.success('保存成功')
              this.on_refresh_table()
            }
          })
          this.loading = false
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    handleIndicatorAdd() {
      this.loading = true
      this.$refs['addIndicatorForm'].validate((valid) => {
        if (valid) {
          this.authPostRequest({
            url: this.$path.fjcy_indicator_create,
            params: this.indicatorForm
          }).then(res => {
            if (res.success) {
              this.closeIndicatorEvent()
              this.$Message.success('保存成功')
              this.getList()
              this.indicatorForm = {
                orderId: 1
              }
            }
          })
          this.loading = false
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    handleResChange(dbId, resId) {
      this.getFields(dbId, resId)
    },
    dataSourceChange(dbId, resType) {
      this.getResourcesList(dbId, resType)
    },
    closeEvent() {
      this.showForm = false
    },
    closeIndicatorEvent() {
      this.showIndicatorForm = false
      this.indicatorForm = {
        orderId: 1
      }
    },
    // 获取列表
    getList() {
      let orgCode = this.$store.state.common.orgCode
      this.authGetRequest({
        url: this.$path.bsp_pam_integral_get_tree,
        params: {orgCode: orgCode}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.treeData = resp.datatreeData
        }
      })
    },
    handleListChange(item, data) {
      this.$set(this.params, 'riskModelId', item.modelId)
      this.$set(this.params, 'typeId', item.typeId)
      this.$set(this.params, 'indicatorId', item.indicatorId)
      this.$set(this.params, 'id', item.id)
      this.$set(this.modelForm, 'riskModelId', item.modelId)
      this.$set(this.modelForm, 'indicatorTypeId', item.typeId)
      this.$set(this.modelForm, 'indicatorId', item.indicatorId)
      if (this.modelForm.indicatorId) {
        this.addDisabled = false
      } else {
        this.addDisabled = true
      }
      setTimeout(() => {
        this.on_refresh_table()
      }, 100)
    },
    deleleChange(row) {
    },
    getRegionDetail(row) {
    },

    on_refresh_table() {
      this.$refs.grid.query_grid_data(1)
    },
    getResourcesList(dbId, resType) {
      if (!resType) {
        return
      }
      this.authGetRequest({
        url: this.$path.fjcy_getResourcesByDbId,
        params: {
          dbId: dbId,
          resType: resType
        }
      }).then(resp => {
        if (resp.success && resp.data) {
          this.resources = resp.data
        }
      })
    },
    getFields(dbId, resId) {
      if(!dbId){
        return
      }
      this.authGetRequest({
        url: this.$path.fjcy_getFieldAllData,
        params: {
          dbId: dbId,
          resId: resId,
        }
      }).then(resp => {
        if (resp.success && resp.data) {
          this.fieldList = resp.data
        }
      })
    },
    getModelData(modelId) {
      this.authGetRequest({
        url: this.$path.fjcy_model_get,
        params: {id: modelId}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.modelForm = resp.data
        }
      })
    },
    getDataSourceList() {
      this.authGetRequest({
        url: this.$path.fjcy_getDbByAppId,
        params: {systemMark: serverConfig.APP_MARK}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.dataSourceList = resp.data
        }
      })
    },
    addNode(node) {
      this.showIndicatorForm = true
      this.indicatorForm.riskModelId = this.params.riskModelId
      this.indicatorForm.parentId = this.params.typeId ? this.params.typeId : ''
    },
    editNode(node) {
      this.showIndicatorForm = true
      this.authGetRequest({
        url: this.$path.fjcy_indicator_get,
        params: {id: this.params.id}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.indicatorForm = resp.data
        }
      })
    },
    deleteNode(node){
      this.$Modal.confirm({
        title: '是否确认删除？',
        loading: true,
        onOk: async () => {
          this.authGetRequest({
            url: this.$path.fjcy_indicator_delete,
            params: {ids: this.params.id}
          }).then(resp => {
            if (resp.success && resp.data) {
              this.$Modal.remove()
              this.getList()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-btn:nth-of-type(n+1) {
  margin-left: 10px;
}

.dbxx-wrap-search {
  background: rgba(247, 249, 252, .9);
  padding: 10px 15px;
  /* margin-bottom: 10px; */
  overflow: hidden;
  border: 1px solid #cee0f0;
  margin: 16px 10px;
}

.pageWrap {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 6px 0;
  margin-top: 6px;
  // border-top: 1px solid #f1f9fa;
  width: 100%;
}

.addBtn {
  margin-bottom: 10px;
}

.wrap-cont {
  display: flex;
  // width: 70px;
}

.tips {
  // display: flex;
  // flex-direction: column;
}

// /deep/.ivu-modal-header-inner{
//   // background: none !important;
//   // color: #444 !important
// }
///deep/ .ivu-modal-body {
//  min-height: 540px !important;
//}

.title {
  position: relative;
  font-size: 16px;
  color: #000;
  // font-weight: 600;
  line-height: 40px;
  height: 40px;
  padding-left: 15px;
  margin-left: 10px;
}

.title::before {
  position: absolute;
  content: "";
  background-color: #087eff;
  width: 4px;
  height: 20px;
  left: 0;
  top: 50%;
  margin-top: -10px;
  // -webkit-border-radius: 3px;
  // -moz-border-radius: 3px;
  // border-radius: 3px;
}

.djxx-title {
  position: relative;
  font-size: 16px;
  color: #000;
  // font-weight: 600;
  line-height: 50px;
  padding-left: 10px;
  // margin-left: 10px;
}

.djxx-title::before {
  position: absolute;
  content: "";
  background-color: #087eff;
  width: 4px;
  height: 20px;
  left: 0;
  top: 50%;
  margin-top: -10px;
}

.djxx-wrap {
  // margin:20px 10%;
  // border:1px solid #eff6ff;
  .ivu-col {
    border-right: 1px solid #eff6ff;
    border-bottom: 1px solid #eff6ff;
    line-height: 36px;
  }

  .ivu-col:nth-of-type(2n+1) {
    font-weight: 700;
    text-align: right;
    padding-right: 16px;
    background: #e4eefc;
    // border-right: 2px solid #fff;
    border: 2px solid #fff;
    border-bottom: none;
  }

  .ivu-col:nth-of-type(2n) {
    padding-left: 16px;
    background: #f5f7fa;
    height: 100%;
    border-top: 2px solid #fff;
    // border-bottom: 2px solid #fff;
    // border-left: none;
  }
}

// /deep/.ivu-btn-primary{
//   background: none !important;
// }
.input-container {
  position: relative;
  display: inline-block;
}

.unit {
  position: absolute;
  right: 1px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
  text-align: center;
  width: 25px;
  height: 90%;
  border-radius: 0px 4px 4px 0px;
  background: #e6f0ff;
}

// .cancle_btn {
//     min-width: 60px;
//     height: 30px;
//     background: #ffffff;
//     border: 1px solid #2b5fd9;
//     color: #2b5fd9;
//     border-radius: 2px;
// }
// .sure_btn {
//     min-width: 60px;
//     height: 30px;
//     background: #2b5fd9;
//     border-radius: 2px;
// }
</style>
