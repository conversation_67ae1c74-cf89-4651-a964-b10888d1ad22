<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      权利义务告知详情
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
        <div class="form-item" v-if="routerData?.id">
          <Row>
            <Col :span="6" class="label">下发时间:</Col>
            <Col :span="14">{{routerData.info.addTime}}</Col>
            <Col :span="6" class="label">签名时间:</Col>
            <Col :span="14">{{detail.signTime}}</Col>
            <Col :span="6" class="label">签名方式:</Col>
            <Col :span="14">{{detail.signTypeName}}</Col>
            <Col :span="6" class="label">签名状态:</Col>
            <Col :span="14">{{routerData.info.sfqm == '0' ? '未签名' : '已签名'}}</Col>
          </Row>
        </div>
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="display: flex; flex-direction: column; flex: 1">
        <SinglePDFViewer ref="pdfViewer" title="在押人员权利和义务告知书"
          :key="pdfKey" formId="1940312854863417344" :businessId="businessId" :fileInfo="fileInfo">
          <template slot="action">
            <Button @click="goList()">返回</Button>
            <Button :loading="loading" style="margin-left: 10px;" type="primary" @click="handlePrint()" v-if="detail?.signType == '1'">打印</Button>
            <Button style="margin-left: 10px;" type="primary" @click="handleUpload()">导出</Button>
          </template>
        </SinglePDFViewer>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
// import { SinglePDFViewer } from "@/components/index.js";
import SinglePDFViewer from "./components/singlePDFViewer.vue";
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {
    SinglePDFViewer
  },
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
      },
      detail: {},
      businessId: '',
      fileInfo: {},
      pdfKey:''
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.routerData.info = JSON.parse(this.routerData.info);
    this.getBusinessId();
    this.getDetail();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    getBusinessId() {
      this.loading = true;
      this.authGetRequest({
        url: api.getBusinessId,
        params: { jgrybm: this.routerData.jgrybm },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.pdfKey = res.data.id;
          if (res.data.signType == '2') {
            this.fileInfo = res.data.offlineUrl ? JSON.parse(res.data.offlineUrl) : {};
          }else{
            this.businessId = res.data.businessId;
          }
          
        }
      });
    },
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { jgrybm: this.routerData.jgrybm },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "noticeOfRightsAndObligationsKssList" });
    },
    handlePrint() {
      this.$refs.pdfViewer.print();
    },
    handleUpload() {
       this.$refs.pdfViewer.downLoad(this.detail.jgryxm)
    }
  },
};
</script>

<style lang="less" scoped>
.fixPersonelStyle {
  /deep/ .flex-box {
    margin-bottom: 16px;

    .detail-title {
      margin-bottom: unset;
      line-height: 1.8;
      height: unset;
    }

    .ivu-btn {
      margin-top: 0 !important;
    }
  }
}

.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}

.form-item {
  .ivu-col {
    text-align: center;
    font-size: 14px;
    margin-bottom: 5px;
  }
  .label {
    color: #666;
  }
}
</style>
