<template>
  <div>
    <rs-DataGrid ref="grid1" funcMark="kss-qlywgzs" :customFunc="false">
      <template #kss-qlywgzs:dyqlywgzs="{ oper }">
        <Button type="primary" @click.native="printNotice" :loading="loading">
          <Icon v-if="oper.iconPath" :type="oper.iconPath" />
          {{ oper.name }}
        </Button>
      </template>
      <template #kss-qlywgzs:xq="{ oper, row }">
        <Button type="primary" @click="toDetail(row)">
          <Icon v-if="oper.iconPath" :type="oper.iconPath" />
          {{ oper.name }}
        </Button>
      </template>
      <template #kss-qlywgzs:sc="{ oper, row }">
        <Button type="primary" @click="handleUpload(row)" style="margin-left: 10px;" v-if="row.sfqm == '0'">
          <Icon v-if="oper.iconPath" :type="oper.iconPath" />{{ oper.name }}
        </Button>
      </template>
    </rs-DataGrid>
    <Modal v-model="uploadModal" width="50%" title="上传签名文书">
      <Form ref="formData" :model="formData" :label-width="280" :label-colon=true>
        <FormItem label="上传签名权利义务告知书" prop="offlineUrl"
          :rules="[{ trigger: 'change', message: '上传签名权利义务告知书必填', required: true }]">
          <uploadList :fileList="fileList" :photo="false" :importUrl="importUrl"
            @handleSuccess="(data) => handleSuccess(data)" @removeItem="removeItem" :maxFiles="1"
            :format="['pdf', 'png', 'jpg', 'jpeg']">
          </uploadList>
        </FormItem>
        <FormItem label="签名时间" prop="signTime">
          <DatePicker v-model="formData.signTime" type="datetime" placeholder="签名时间" style="width: 200px;"></DatePicker>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleSubmit" class="save">确 定</Button>
        <Button @click="uploadModal = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import api from "./api.js";
import printJS from 'print-js';
import { getToken } from '@/libs/util';
import uploadList from "@/components/upload/upload-list.vue";
export default {
  components: {
    uploadList
  },
  data() {
    return {
      checkedItems: [],
      uploadModal: false,
      formData: {
        offlineUrl: '',
      },
      // 文件上传
      showFile: true,
      fileList: [],
      loading: false,
      pdfUrl: `${this.$path.pdf_getPdf}?plugIns=MultipleRowTableRenderPolicy&formId=1940312854863417344&access_token=${getToken()}#toolbar=0`,
      importUrl: this.$path.upload_fj,
      funcMark: 'kss-qlywgzs',
    };
  },
  created() {
    console.log(serverConfig.APP_MARK, 'created');
    let mark = serverConfig.APP_MARK;
    if (mark === 'bjsdskss') {
      this.funcMark = 'kss-qlywgzs';
    } else if (mark === 'bjsjls') {
      this.funcMark = 'jls-qlywgzs';
    } else if (mark === 'bjsqzgljds') {
      this.funcMark = 'jds-qlywgzs';
    }

  },
  methods: {
    printNotice() {
      this.loading = true;
      const _this = this;
      printJS({
        printable: this.pdfUrl,
        type: 'pdf',
        header: '打印',
        onLoadingEnd: async () => {
          _this.loading = false;
        }
      })
    },

    handleUpload(row) {
      this.uploadModal = true
      this.formData.jgrybm = row.jgrybm
      this.formData.jgryxm = row.xm

    },
    toDetail(row) {
      // let info = 
      const info = encodeURIComponent(JSON.stringify({
        addTime: row.add_time,
        sfqm: row.sfqm
      }));
      this.$router.replace({
        path: `/discipline/noticeOfRightsAndObligations-kss/detail?jgrybm=${row.jgrybm}&id=${row.id ? row.id : ''}&info=${info}`,
      });
    },
    handleSubmit() {
      this.formData.signTime = this.dayjs(this.formData.signTime).format('YYYY-MM-DD HH:mm:ss')
      this.$refs.formData.validate(valid => {
        if (valid) {
          let params = { ...this.formData }
          this.$store.dispatch('authPostRequest', { url: api.acp_create, params: params }).then(res => {
            if (res.success) {
              this.$Message.success('上传成功')
              this.refreshform()
              this.$refs.grid1.query_grid_data()
            } else {
              this.$Message.error(res.message)
            }
          })
        }
      })
    },
    refreshform() {
      this.fileList = []
      this.formData = {
        offlineUrl: '',
      }
      this.uploadModal = false
    },
    // 上传组件回调方法
    handleSuccess(res) {
      console.log(res, 'res, index')
      this.fileList.push(res.data);
      this.formData.offlineUrl = res.data ? JSON.stringify(res.data) : ''
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.formData.offlineUrl = '';
          this.fileList.splice(e.index, 1);
        },
      });
    }
  },
};
</script>

<style scoped lang="less"></style>
