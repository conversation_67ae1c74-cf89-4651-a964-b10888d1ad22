<template>
    <div style="display: flex; flex-direction: column; flex: 1;">
        <div
            style="line-height: 2.8;background: #eff6ff;border:1px solid #efefef; padding: 0 10px; display: flex; flex-direction: row; justify-content: space-between;">
            <span style="font-size: 16px; font-weight: bold;">{{ title }}</span>
            <div>
                <slot name="action"></slot>
            </div>
        </div>
        <div v-loading="loading" style="border: 1px solid #eaeaea; padding:10px; background-color: #f5f7fa; flex:1" v-if="businessId">
            <embed width="100%" height="100%" name="plugin" id="plugin" :src="pdfUrl" type="application/pdf"
                javascript="allow" />
        </div>
        <div v-loading="loading" style="border: 1px solid #eaeaea; padding:10px; background-color: #f5f7fa; flex:1" class="pdf-viewer" v-else>
            <div class="img-container" v-if="imgType.indexOf(fileInfo.contentType) != '-1'">
                    <img :src="pdfUrl" style="width: 100%; height: 100%;" alt="">
            </div>
            <embed width="100%" height="100%" name="plugin" id="plugin" :src="pdfUrl"
                javascript="allow" v-else/>
        </div>
    </div>
</template>

<script>
import { getToken } from '@/libs/util'
import printJS from 'print-js';
export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        formId: {
            type: String,
            default: ''
        },
        businessId: {
            type: String,
            default: ''
        },
        fileInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            //   pdfUrl: `${this.$path.pdf_getPdf}?plugIns=MultipleRowTableRenderPolicy&formId=${this.formId}&businessId=${this.businessId}&access_token=${getToken()}#toolbar=0`,
            pdfUrl: '',
            loading: false,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            imgType: ['image/jpeg','image/png'],
        }
    },
    mounted() {
        console.log(this.fileInfo,'pdfUrl');
        
        if (this.businessId) {
            this.pdfUrl = `${this.$path.pdf_getPdf}?plugIns=MultipleRowTableRenderPolicy&formId=${this.formId}&businessId=${this.businessId}&access_token=${getToken()}#toolbar=0`
        } else {
           this.pdfUrl = this.fileInfo.url
        }
    },
    methods: {
        print() {
            this.loading = true;
            const _this = this;
            printJS({
                printable: this.pdfUrl,
                type: 'pdf',
                header: '打印',
                onLoadingEnd: async () => {
                    _this.loading = false;
                }
            })
        },
        downLoad(name) {
            if (this.pdfUrl) {
                fetch(this.pdfUrl).then(res => {res.blob().then(resp => {
                            const link = document.createElement('a');
                            link.href = window.URL.createObjectURL(resp);
                            document.body.appendChild(link)
                            link.download = `在押人员权利和义务告知书-${name}`;
                            link.click();
                            window.URL.revokeObjectURL(link.href);
                            document.body.removeChild(link)
                        })
                    })
            }
        },

    }
}
</script>

<style lang="less" scoped>
.pdf-viewer {
    .img-container {
        width: 50%;
        margin: auto;
    }
}
</style>
