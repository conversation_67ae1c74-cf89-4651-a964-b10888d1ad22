<template>
    <div class="patrolControlAdd">
        <div class="content">
            <div class="show-card">
                <div class="icon" @click="leftShow = !leftShow">
                    <Icon v-if="!leftShow" type="ios-arrow-forward" />
                    <Icon v-else type="ios-arrow-back" />
                </div>
                <div class="left-card" v-show="leftShow">
                    <p class="detail-title">重点人员</p>
                    <Tabs @on-click="tabSwitch">
                        <TabPane v-for="(item, index) in roomList" :key="item.roomId" :label="item.roomName"
                            :name="item.roomId">
                            <div class="prisoner-list">
                                <swiper :options="swiperOption" ref="mySwiper">
                                    <swiper-slide class="item" v-for="(ele, index) in item.prisonerList" :key="ele.id"
                                        @click.native="swiperSelectClick(ele)">
                                        <div class="item-top">
                                            <img class="item-img" :src="ele.frontPhoto" />
                                            <span class="item-name">{{ ele.jgryxm }}</span>
                                        </div>
                                        <p class="ms">{{ ele.attentionReason }}</p>
                                    </swiper-slide>
                                    <!-- <div class="swiper-pagination" slot="pagination"></div> -->
                                    <div class="swiper-button-prev" slot="button-prev"></div>
                                    <div class="swiper-button-next" slot="button-next"></div>
                                </swiper>
                            </div>
                        </TabPane>
                    </Tabs>
                    <div style="margin-top: 30px;">
                        <p class="detail-title">一日生活</p>
                        <div class="day-life">
                            <Table border :columns="columns1" :data="timeData" :show-header="false">
                                <template slot-scope="{ row }" slot="time">
                                    <div>{{ row.startTime }} - {{ row.endTime }}</div>
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right-card" :class="leftShow ? '' : 'right-card-width'">
                <Form ref="formData" :model="formData" :label-width="130" :rules="rules">
                    <div class="fm-content-box" v-if="patroStatus == 'details'" style="margin-bottom: 20px;">
                        <Row>
                            <Col span="4"><span>代办来源</span></Col>
                            <Col span="8"><span>{{ info.todo_sourceName }}</span></Col>
                            <Col span="4"><span>待办推送时间</span></Col>
                            <Col span="8"><span>{{ info.todo_push_time }}</span></Col>
                            <Col span="4"><span>岗位</span></Col>
                            <Col span="8"><span>{{ info.todo_post }}</span></Col>
                            <Col span="4"><span>待办推送人</span></Col>
                            <Col span="8"><span>{{ info.todo_person }}</span></Col>
                            <Col span="4"><span>待办事由</span></Col>
                            <Col span="20"><span>{{ info.todo_reason }}</span></Col>
                        </Row>
                    </div>
                    <Row>
                        <Col span="8">
                        <FormItem label="巡控室" prop="patrolRoomId">
                            <Select v-model="formData.patrolRoomId" @on-change="patrolChange" :label-in-value="true">
                                <Option v-for="item in patrolRoomList" :value="item.id" :key="item.id">{{ item.areaName
                                }}
                                </Option>
                            </Select>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label="监室号" :label-width="80">
                            <Input v-model="formData.roomName" readonly placeholder="请选择监室号"
                                @on-focus="openModal = true">
                            <template slot="append">
                                <Button @click="openModal = true">选择</Button>
                            </template>
                            </Input>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label="被监管人员姓名">
                            <Input v-model="formData.jgryxm" readonly placeholder="请输入被监管人员姓名"
                                @on-focus="openPrison = true">
                            <template slot="append">
                                <Button @click="openPrison = true">选择</Button>
                            </template>
                            </Input>
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="是否异常" prop="isAbnormal">
                            <RadioGroup v-model="formData.isAbnormal" @on-change="changeIsAbnormal">
                                <Radio label="0">正常</Radio>
                                <Radio label="1">异常</Radio>
                            </RadioGroup>
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="登记内容" prop="recordContent">
                            <Poptip placement="bottom" style="width: 100%" :word-wrap="true" ref="poptip">
                                <editor-vue class="editor" ref="editorVue" :content="content"
                                    @changeData="hChangeData" />
                                <div slot="content" class="tipContent">
                                    <div class="tipContent-item" v-for="(item, index) in tipText" :key="item.code"
                                        @click="tipClick(item.name)">{{ item.name }}
                                    </div>
                                </div>
                            </Poptip>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="24">
                        <postCollaboration ref="postCollaboration" @on-pushText="pushText" :isShowModal="false">
                        </postCollaboration>
                        <!-- <FormItem label="是否岗位协同" prop="isPostCoordination">
                            <RadioGroup v-model="formData.isPostCoordination" @on-change="changeIsPostCoordination">
                                <Radio label="1">是</Radio>
                                <Radio label="0">否</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="岗位协同" v-if="formData.isPostCoordination == '1'">
                            <CheckboxGroup v-model="formData.coordinationPosts" @on-change="changecoordinationPosts">
                                <Checkbox v-for="(item, index) in coordinationPostsList" :key="item.code"
                                    :label="item.code">{{
                                        item.name }}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <FormItem label="推送对象"
                            v-if="formData.coordinationPosts.includes('06') && formData.isPostCoordination == '1'">
                            <user-selector v-model="formData.pushTargetIdCards" tit="用户选择" @onSelect="onSelectUs"
                                :text.sync="formData.pushTarget" returnField="id">
                            </user-selector>
                        </FormItem>
                        <FormItem label="推送内容" v-if="formData.isPostCoordination == '1'">
                            <Input placeholder="请输入推送内容" v-model="formData.pushContent"></Input>
                        </FormItem> -->
                        </Col>
                        <Col span="10">
                        <FormItem label="登记人">
                            <Input disabled v-model="formData.operatorXm"></Input>
                        </FormItem>
                        </Col>
                        <Col span="10">
                        <FormItem label="登记时间">
                            <DatePicker v-model="formData.operatorTime" type="datetime" placeholder="请选择"></DatePicker>
                        </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
        </div>
        <div class="bsp-base-fotter">
            <Button @click="onCancel">返 回</Button>
            <Button type="primary" :loading="loading" @click="handleSubmit">提 交</Button>
        </div>


        <Modal v-model="openPrison" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
            title="人员列表">
            <div class="select-use">
                <prisonSelect ref="prisonSelect" ryzt="ALL" :isMultiple='true' :selectUseIds="formData.jgrybm"
                    :roomCode="formData.roomId" />
            </div>
            <div slot="footer">
                <Button type="primary" @click="useSelect" class="save">确 定</Button>
                <Button @click="openPrison = false" class="save">关 闭</Button>
            </div>
        </Modal>

        <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-room-modal" width="1360"
            title="监室列表">
            <div class="select-room">
                <roomSelect ref="roomSelect" ryzt="ALL" :isMultiple="true" :selectRoomIds="formData.roomId" />
            </div>
            <div slot="footer">
                <Button type="primary" @click="useSelectRoom" class="save">确 定</Button>
                <Button @click="openModal = false" class="save">关 闭</Button>

            </div>
        </Modal>
    </div>

</template>

<script>
import { mapActions } from 'vuex'
import { userSelector } from 'sd-user-selector'
import { formatDateparseTime, getUserCache } from "@/libs/util.js"
import { prisonSelect } from "sd-prison-select"
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/dist/css/swiper.css";
import editorVue from "@/components/wangEditor/index.vue";
import { roomSelect } from "sd-room-select";
import postCollaboration from "@/components/postCollaboration/index.vue";
export default {
    name: 'patrolControlAdd',
    components: {
        userSelector,
        prisonSelect,
        swiper,
        swiperSlide,
        editorVue,
        roomSelect,
        postCollaboration
    },
    props: {
        patroStatus: {
            type: String,
            default: 'add'
        },
        info: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            roomList: [],
            loading: false,
            columns1: [
                {
                    title: 'time',
                    slot: 'time',
                    align: 'center',
                },
                {
                    title: 'eventName',
                    key: 'eventName',
                    align: 'center',
                },
            ],
            timeData: [],
            formData: {
                operatorTime: new Date(),
                isPostCoordination: '0',
                operatorXm: getUserCache.getUserName(),
                operatorSfzh: getUserCache.getIdCard(),
                patrolStaff: getUserCache.getUserName(),
                patrolStaffSfzh: getUserCache.getIdCard(),
                patrolRoomId: '',
                patrolRoomName: '',
                pushContent: '',
                recordContent: '正常',
                // coordinationPostsName: '',
                // coordinationPosts: [],
                jgrybm: '',
                jgryxm: '',
                // pushTarget: '',
                // pushTargetIdCard: '',
                // pushTargetIdCards: '',
                isAbnormal: '0'
            },
            openPrison: false,
            coordinationPostsList: [],
            roomName: '',
            patrolRoomList: [],
            rules: {
                recordContent: [
                    { required: true, message: '请输入登记内容', trigger: 'blur' }
                ],
                isPostCoordination: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                isAbnormal: [
                    { required: true, message: '请选择是否异常', trigger: 'change' }
                ],
                patrolRoomId: [
                    { required: true, message: '请选择巡控室', trigger: 'change' }
                ]
            },
            // swiper配置
            swiperOption: {
                // loop: true,
                observer: true,//修改swiper自己或子元素时，自动初始化swiper
                observeParents: true,//修改swiper的父元素时，自动初始化swiper
                // autoplay: {
                //     delay: 3000,
                //     stopOnLastSlide: false,
                //     disableOnInteraction: false
                // },
                slidesPerView: 'auto',
                spaceBetween: 20,
                // 显示分页
                // pagination: {
                //     el: ".swiper-pagination",
                //     clickable: true //允许分页点击跳转
                // },
                // 设置点击箭头
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev"
                }
            },
            leftShow: true,
            tipText: [],
            contentItems: [],
            content: '正常',
            openModal: false,
            selectedInmates: [], // 存储选中的人员
        }
    },
    mounted() {
        this.getRoomData()
        this.handleGetZD_XSGKXTGW()
        this.getAreaByAreaType()
        this.handleGetZD_XKDJDJNR()

    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        onCancel() {
            this.$emit('on_show_table')
        },
        // 获取监室
        getRoomData() {
            this.authGetRequest({ url: this.$path.represent_attention, params: { orgCode: getUserCache.getOrgCode() } }).then(res => {
                if (res.success) {
                    this.roomList = res.data
                    if (res.data.length) {
                        this.tabSwitch(res.data[0].roomId)
                    }

                }
            })
        },
        tabSwitch(e) {
            console.log(e);
            this.authGetRequest({ url: this.$path.represent_daily, params: { roomId: e } }).then(res => {
                if (res.success) {
                    this.timeData = res.data || []
                }
            })
        },
        onSelectUs(data) {

            const res = data.map(item => {
                return {
                    orgCode: item.orgCode,
                    idCard: item.idCard
                }
            })
            this.formData.pushTargetIdCard = res;
            this.pushText()
        },
        handleSubmit() {
            let form = this.$refs.postCollaboration.formData
            if (!this.$refs.postCollaboration.$refs.formData.validate()) {
                return
            }
            this.formData.isPostCoordination = form.isPostCoordination
            this.formData.pushContent = form.pushContent
            this.formData.pushTarget = JSON.stringify(form.coordinationPostsList)
            this.formData.coordinationPosts = form.coordinationPosts.join(",")
            this.formData.coordinationPostsName = form.coordinationPostsList.filter(item => item.selectStatus == 1).map(item => item.postName).join(",")
            this.$refs.formData.validate(valid => {
                if (valid) {
                    const params = { ...this.formData };
                    // delete params.pushTargetIdCards;
                    // const result = this.coordinationPostsList.filter(item => this.formData.coordinationPosts.includes(item.code)).map(item => item.name);
                    // params.coordinationPostsName = result.join(",");
                    // params.coordinationPosts = this.formData.coordinationPosts.join(",");
                    params.operatorTime = formatDateparseTime(this.formData.operatorTime)
                    // params.pushTargetIdCard = JSON.stringify(this.formData.pushTargetIdCard)
                    if (this.patroStatus == 'details') {
                        params.id = this.info.id
                        this.authPostRequest({ url: this.$path.represent_regSave, params: params }).then(res => {
                            if (res.success) {
                                this.$Message.success('添加成功')
                                this.onCancel()
                            } else {
                                this.$Message.error(res.message)
                            }

                        })
                    } else {
                        this.authPostRequest({ url: this.$path.represent_createPatrolRecord, params: params }).then(res => {
                            if (res.success) {
                                this.$Message.success('添加成功')
                                this.onCancel()
                            } else {
                                this.$Message.error(res.message)
                            }

                        })
                    }
                }
            });
        },
        // openPrison() {
        //     this.openPrison = true
        // },
        useSelect() {
            if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
                let people = this.$refs.prisonSelect.checkedUse

                this.formData.jgryxm = people.map(item => item.xm).join(',')
                this.formData.jgrybm = people.map(item => item.jgrybm).join(',')
                // this.formData.roomId = people.jsh
                //      this.formData.roomName = people.roomName
                this.openPrison = false
            } else {
                this.$Notice.warning({
                    title: '提示',
                    desc: '请选择人员!'
                })
            }
        },
        handleGetZD_XSGKXTGW() {
            this.$store
                .dispatch("authGetRequest", {
                    url: "/bsp-com/static/dic/acp/ZD_XSGKXTGW.js",
                })
                .then((res) => {
                    let scales = eval("(" + res + ")");
                    this.coordinationPostsList = scales();
                });
        },
        // 获取巡控室
        getAreaByAreaType() {
            this.authPostRequest({ url: this.$path.represent_getAreaByAreaType, params: {} }).then(res => {
                if (res.success) {
                    this.patrolRoomList = res.data
                }
            })
        },
        patrolChange(e) {
            this.formData.patrolRoomName = e.label
        },
        pushText() {
            let value = this.$refs.postCollaboration.formData
            if (this.formData.recordContent && value.isPostCoordination == '1' && this.formData.isAbnormal == '1') {
                // const result = this.coordinationPostsList.filter(item => this.formData.coordinationPosts.includes(item.code)).map(item => item.name);
                const result = value.coordinationPostsList.filter(item => item.selectStatus == 1).map(item => item.postName)
                let pushContent = (this.formData.roomName || '') + (this.formData.jgryxm || '') + (this.formData.recordContent || '') + ',请' + (result ? result.join(",") : '') + '查看情况'
                this.$refs.postCollaboration.pushText(pushContent)
            } else if (this.formData.recordContent && value.isPostCoordination == '1' && this.formData.isAbnormal == '0') {
                let pushContent = (this.formData.roomName || '') + (this.formData.jgryxm || '') + (this.formData.recordContent || '')
                this.$refs.postCollaboration.pushText(pushContent)
            }

        },
        changeIsPostCoordination(val) {
            console.log(val);
            this.formData.coordinationPosts = []
            this.formData.coordinationPostsName = ''
            this.formData.pushTarget = ''
            this.formData.pushTargetIdCard = ''
            this.formData.pushContent = ''
        },

        changecoordinationPosts() {
            this.pushText()
        },
        changeIsAbnormal(val) {
            console.log(val);
            if (val == '0') {
                this.formData.recordContent = '正常'
                this.content = '正常'
            } else {
                this.formData.recordContent = ''
                this.content = ''
            }
        },
        handleGetZD_XKDJDJNR() {
            this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_XKDJDJNR.js" }).then(res => {
                let scales = eval('(' + res + ')')
                this.tipText = scales()
            })
        },
        tipClick(item) {
            this.contentItems.push(item);
            this.content = this.contentHtml()
        },
        contentHtml() {
            return `<p>${this.contentItems.join('，')}</p>`;
        },
        hChangeData(editDataHtml, editData) {
            this.formData.recordContentHtml = editDataHtml
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
            const pureText = tempDiv.innerText || tempDiv.textContent; // 提取纯文本
            // 将纯文本赋值给 infoContentText
            this.formData.recordContent = pureText;
        },
        useSelectRoom() {
            if (this.$refs.roomSelect.checkedRoom && this.$refs.roomSelect.checkedRoom.length > 0) {
                let roomInfo = this.$refs.roomSelect.checkedRoom;
                this.formData.roomId = roomInfo.map(item => item.roomCode).join(',')
                this.formData.roomName = roomInfo.map(item => item.roomName).join(',')
                this.openModal = false;
            } else {
                this.$Notice.warning({
                    title: '提示',
                    desc: '请选择监室!'
                })
            }
        },
        // 快速选人
        swiperSelectClick(ele) {
            console.log(ele);
            const index = this.selectedInmates.findIndex(i => i.jgrybm === ele.jgrybm)
            if (index === -1) {
                // 不存在则添加
                this.selectedInmates.push(ele)

            } else {
                // 存在则移除
                this.selectedInmates.splice(index, 1)
            }

            this.formData.jgryxm = this.selectedInmates.map(item => item.jgryxm).join(',')
            this.formData.jgrybm = this.selectedInmates.map(item => item.jgrybm).join(',')
            this.formData.roomId = [...new Set(this.selectedInmates.map(item => item.roomId))].join(',')
            this.formData.roomName = [...new Set(this.selectedInmates.map(item => item.roomName))].join(',')

        }
    }
}

</script>

<style scoped lang="less">
.patrolControlAdd {
    .content {
        display: flex;
        gap: 20px;

        .left-card {
            width: 450px;
            padding: 10px;
            border: 1px solid #dcdfe6;
            min-height: calc(100vh - 25vh);

            .prisoner-list::-webkit-scrollbar {
                // width: 1px;
                height: 5px;
            }

            .prisoner-list::-webkit-scrollbar-thumb {
                background-color: #ddd;
                border-radius: 5px;
            }

            .prisoner-list {
                // display: flex;
                // overflow: auto;
                // gap: 15px;
                padding: 10px 0;
                // margin: 0 10px;

                .item {
                    width: 82px;

                    .item-top {
                        width: 82px;
                        height: 110px;
                        // position: relative;
                        box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
                        cursor: pointer;

                        .item-img {
                            width: 82px;
                            height: 110px;
                            border-radius: 4px;
                            // display: block;
                        }

                        .item-name {
                            position: relative;
                            top: -24px;
                            height: 24px;
                            display: inline-block;
                            background: #e9f0ff;
                            border-radius: 0 0 4px 4px;
                            color: #4b81ff;
                            width: 100%;
                            text-align: center;
                        }
                    }

                    .ms {
                        text-align: center;
                        font-size: 12px;
                        margin-top: 5px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .swiper-button-next {
                    right: 0;
                    height: 24px;
                    margin-top: -12px;
                }

                .swiper-button-prev {
                    left: 0;
                    height: 24px;
                    margin-top: -12px;
                }

                .swiper-container {
                    padding: 0 20px;
                }
            }

            .day-life {
                padding: 0 20px;
            }

        }

        .right-card {
            flex-grow: 1;
            min-height: calc(100vh - 25vh);

        }

        .right-card-width {
            width: 60%;
            flex-grow: unset;
            margin: auto;
        }

        .show-card {
            position: relative;

            .icon {
                width: 30px;
                height: 30px;
                background: #f5f8ff;
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                right: -30px;
                border-radius: 0 30px 30px 0;
                border: 1px solid #dcdfe6;
                cursor: pointer;
                top: 45%;

                z-index: 11;
                // box-shadow: 0 2px 4px 0 rgba(0, 34, 84, .12);
            }
        }

    }

    /deep/.ivu-poptip-popper {
        left: 0 !important;
    }

    /deep/.ivu-poptip-body-content {
        max-height: 160px;
    }

    /deep/.tipContent {
        display: flex;
        flex-wrap: wrap;

        .tipContent-item {
            margin-right: 20px;
            margin-bottom: 10px;
            height: 30px;
            cursor: pointer;
            line-height: 30px;
            padding: 0 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;

            &:hover {
                box-shadow: 0px 1px 4px 0px #cdd0d5;
            }
        }

    }

    /deep/.ivu-input-group-append {
        color: #fff;
        background-color: #2b5fd9;
        border: 1px solid #2b5fd9;
    }
}
</style>