<template>
  <div class="detail-box">
    <div class="detail-flex"><span>姓名：{{rowData.jgryxm}}</span><span>出库时间：{{rowData.addTime}}</span><span>出库人：{{rowData.addUserName}}</span></div>
    <div>
      <Table :columns="columns" :data="dataTable" style="margin-bottom: 20px;" height="500" v-if="showTable">
        <template slot-scope="{ row ,index}" slot="oneDosageNum">
                                    {{ getDCYL(row) }}
                                </template>
                                <template slot-scope="{ row,index }" slot="useDay">
                                    {{ row.useDay }}天
                                </template>
                                <template slot-scope="{ row,index }" slot="numMessage">
                                    {{ getZYL(row) }}
                                </template>
                                <template slot-scope="{ row,index }" slot="outNum">
                                    {{ row.outNum + ''+row.measurementUnitName }}
                                </template>

      </Table>
    </div>
    
  </div>
</template>

<script>

export default {
   props:{
      modalTitle:{
         type:String,
         default:'药品出库详情',
      },
      showFormCompnent:{
        type:Boolean,
        default:false,
      },
      ryId:String
   },
   data(){
    return{
      rowData:{
        xm:'夏友善',
        ckTime:'2025-03-31 10:06:05',
        ckr:'陈XX'
      },
      columns:[
      {
                    title: '序号',
                    type: 'index',
                    width: "80",
                    align: "center"
                },
                {
                    title: '药品名称',
                    key: 'medicineName',
                    width: "300",
                    align: "center",
                },
                {
                    title: '用法',
                    width: "200",
                    align: "center",
                    key: "useMedicineMethodName",
                },
                {
                    title: '用药频率',
                    key: 'useFrequencyName',
                    width: "150",
                    align: "center"
                },
                {
                    title: '单次用量',
                    slot: 'oneDosageNum',
                    width: "350",
                    align: "center"
                },
                {
                    title: '用药天数',
                    slot: 'useDay',
                    width: "150",
                    align: "center"
                },
                {
                    title: '总药量',
                    slot: 'numMessage',
                    align: "center",
                    width: "200",
                },
                {
                    title: '规格',
                    key: 'specs',
                    align: "center",
                    width: "300",
                },
                {
                    title: '嘱托',
                    key: 'entrust',
                    align: "center",
                    width: "300",
                },
                {
                    title: '出库数量',
                    slot: 'outNum',
                    align: "center",
                    width: "300",
                },
      ],
      dataTable:[],
      dicData:[],
      showTable:false
    }
   },
   mounted(){
    this.$dicKit.getDataRows("ZD_MEASUREMENT_UNIT").then(res => {
            this.dicData = res
        })
    this.getData()
   },
   methods:{
         filterName(e) {
            if (this.dicData && this.dicData.length>0) {
                if (e && e != '') {
                    let nameArr= this.dicData.filter(i => {
                        return i.code == e
                    })
                    // console.log(nameArr)
                    if(nameArr && nameArr.length>0){
                        return nameArr[0].name
                    }else{
                         return "-"
                    }
                    
                } else {
                    return "-"
                }
            }
        },
        
        getDCYL(row) {
            return parseFloat((row.oneDosageNum / row.unitConversionRatio).toFixed(5)) + this.filterName(row.measurementUnit) + "/" + row.oneDosageNum + this.filterName(row.minMeasurementUnit)
        },
        getZYL(row) {
            return parseFloat((row.oneDosageNum / row.unitConversionRatio * row.useFrequency * row.useDay).toFixed(5)) + this.filterName(row.measurementUnit) + "/" + row.oneDosageNum * row.useFrequency * row.useDay + this.filterName(row.minMeasurementUnit)
        },
    getData(){
      if(!this.ryId) return
      this.$store.dispatch('authGetRequest', {
                        url: this.$path.get_prescribeOut,
                        params: {
                            id: this.ryId,
                        }
                        }).then(resp => {
                        if (resp.success) {
                          this.rowData=resp.data
                          this.dataTable=resp.data.prescribeOutMedicines
                          this.showTable=true
                        } else {
                            this.showTable=true
                            this.$Notice.error({
                            title: '错误提示',
                            desc:"请求失败"
                            })
                        }
                        })
    }
   }

}
</script>

<style>
.detail-box{
  width: 100%;
}
.detail-flex{
  display: flex;
  align-items: center;
  justify-content:space-between;
  font-size: 18px;
  line-height: 40px;
}
</style>