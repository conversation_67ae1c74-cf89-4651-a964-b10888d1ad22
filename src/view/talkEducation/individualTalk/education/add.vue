<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div style="padding-bottom: 10px;border-bottom: 1px solid #efefef;">
      <div class="title" style="
        font-weight: bold;
      ">
        个别谈话教育
      </div>
    </div>
    <Row style="flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector @change="selectUser" :mode="routerData.id ? 'detail' : 'edit'" :value="formData.jgrybm"
          :showCaseInfo="false" :show-scan-tip="false" />
        <div>
          <div class="title">
            谈话录音录像
          </div>
          <div class="bl-left-content" :key="whereIsVideo === 'left'">
            <div v-show="whereIsVideo === 'left'">
              <VideoPlayer ref="leftVideo" :containerStyleObj="{ marginTop: '12px' }"
                :playerStyleObj="{ width: '100%', height: '200px' }" :options="{ controls: 2, zoom: false }"
                @handleZoom="moveVideoTo('right')" />
            </div>
          </div>
        </div>
      </div>
      </Col>
      <Col :span="13" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="display: flex; flex-direction: column; flex: 1" v-loading="loading">
        <div style="padding: 15px 15px 0;">
          <div class="title">
            谈话业务登记
          </div>
        </div>
        <div class="form-content" style="flex: 1;">
          <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="110" @submit.native.prevent>
            <Row style="padding: 15px 15px 8px">
              <Col span="8">
              <FormItem label="谈话方式" prop="recordType">
                <s-dicgrid v-model="formData.recordType" @change="handleChangeTalkWay" dicName="ZD_THJY_THFS"
                  :isSearch="false" :disabled="isDisabledState || isTalkingState" />
              </FormItem>
              </Col>
              <Col span="8" v-if="talkMethod === '01'">
              <FormItem label="谈话室" prop="talkRoomId">
                <Select v-model="formData.talkRoomId" :clearable="true" filterable label-in-value
                  :loading="roomListLoading" style="width: 100%"
                  :disabled="formData.recordType !== '01' || isDisabledState || isTalkingState"
                  @on-change="handleChangeTalkRoom">
                  <Option v-for="item in talkRoomList" :key="item.areaCode" :tag="item.videoDevice"
                    :label="item.areaName" :value="item.areaCode">
                  </Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="谈话管教" prop="txgj">
                <span style="font-weight: 600;">{{ formData.txgj }}</span>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="谈话原因" prop="thyy">
                <el-cascader :options="talkReasonList" :show-all-levels="false" filterable
                  :disabled="isDisabledState || isTalkingState" popper-class="areaClassName" :clearable="true"
                  v-model="formData.thyy" style="width: 100%" :props="{
                    multiple: true,
                    value: 'code',
                    label: 'name',
                    children: 'subTreeData'
                  }"></el-cascader>
              </FormItem>
              </Col>
            </Row>
            <div
              style="display: flex; flex-direction: row;align-content: center;justify-content: space-between;padding: 8px 15px;border: 1px solid #efefef">
              <div>
                <Button type="success" @click="handleStartTalk"
                  :disabled="isDisabledState || isTalkingState">开始谈话</Button>
                <Button style="margin-left: 15px;" type="error" @click="handleStopTalk"
                  :disabled="!isTalkingState">结束谈话</Button>
              </div>
              <Tag v-if="isTalkingState" style="
                font-size: 15px;
                margin: 0;
                height: 30px;
                line-height: 30px
              " color="volcano">
                <Icon type="ios-information-circle" />谈话进行中，请注意文明用语
              </Tag>
              <div style="display: flex;flex-direction: row;">
                <p style="font-size: 16px;margin-right: 15px;font-weight: bold;">谈话用时：<span style="color: #237acc">{{
                  formData.timer
                    }}</span></p>
                <Icon v-if="isTalkingState" style="cursor: pointer;" class="fixColor" title="点击放大" type="md-expand"
                  size="26" @click="handleZoomRecord" />
              </div>
            </div>
            <div style="height: 445px;">
              <div v-if="whereIsVideo === 'right'" style="height: 445px; position: relative;">
                <VideoPlayer ref="rightVideo" :containerStyleObj="{ height: '100%' }" :playerStyleObj="{
                  width: '100%',
                  height: '100%',
                  margin: '0 auto',
                }" :options="{ controls: 2, zoom: true }" @handleZoom="moveVideoTo('left')" />
                <div v-show="sipInstance" style="
                  position: absolute;
                  width: 200px;
                  height: 150px;
                  right: 20px;
                  bottom: 20px;
                ">
                  <VideoPlayer ref="subVideo" :options="{ controls: 2 }" :containerStyleObj="{ height: '100%' }"
                    :playerStyleObj="{
                      width: '100%',
                      height: '100%',
                      margin: '0 auto',
                    }" />
                </div>
              </div>
              <RecordArea v-if="talkMethod === '01' && whereIsVideo === 'left'" v-model="records" ref="RecordArea" />
            </div>
          </Form>
        </div>
      </div>
      </Col>
      <Col :span="5" style="border-left: 1px solid #efefef;">
      <TalkTemplate :props="{ thyy: formData.thyy[0], isTalkingState, talkReasonList,talkReasonListTree, jgrybm: formData.jgrybm }"
        @importSingle="handleImportTemplate" @importAll="handleImportAll" />
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="goList" :disabled="isTalkingState">返 回</Button>
    </div>
    <SubmitModal ref="SubmitModal" />
    <RecordAreaModal ref="RecordAreaModal" @shrink="handleShrinkRecord" />
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import { VideoPlayer } from "@/components/index.js";
import { TalkTemplate, SubmitModal, RecordArea, RecordAreaModal } from "./components";
export default {
  components: {
    VideoPlayer,
    TalkTemplate,
    SubmitModal,
    RecordArea,
    RecordAreaModal
  },
  data() {
    return {
      routerData: {
        id: "",
        jgrybm: ""
      },
      formData: {
        id: "",
        thyy: [],
        talkReason: "",
        txgj: this.$store.state.common.userName,
        talkRoomId: "",
        talkContent: "",
        jgrybm: "",
        jgryxm: "",
        jgryxx: "",
        jsh: "",
        timer: "00:00:00",
        startTime: "",
        recordType: "",
      },
      ruleValidate: {
        thyy: [
          { required: true, type: "array", message: "谈话原因不能为空", trigger: "change" },
        ],
        txgj: [{ required: true, message: "人员不能为空", trigger: "change" }],
        talkRoomId: [{ required: true, message: "谈话室不能为空", trigger: "change" }],
        recordType: [
          { required: true, message: "请选择谈话方式", trigger: "change" },
        ],
      },
      talkReasonList: [],
      talkReasonListTree: [],
      talkRoomList: [],
      records: [],
      seconds: 0,
      taskId: "",
      timerInterval: null,
      heartInterval: null,
      loading: false,
      roomListLoading: false,
      isDisabledState: true,
      isTalkingState: false,
      talkMethod: "",  // 根据字典值 01面对面谈话 03远程谈话
      whereIsVideo: '',
      videoUrl: null,
      sipInstance: null,
    };
  },
  computed: {},
  created() {
    // 修改的逻辑
    if (this.$route.query.id) {
      this.routerData = { ...this.$route.query };
      this.formData.id = this.routerData.id;
      this.formData.jgrybm = this.routerData.jgrybm;
      this.getTalkBusinessDetail(this.routerData.id);
    }
    this.getTalkReasonList();
  },
  mounted() {
    this.bindCascaderClickEvent();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    // 拦截 cascader 组件点击事件以满足用户需求
    bindCascaderClickEvent() {
      this.$nextTick(() => {
        const panel = document.querySelector('.el-cascader-panel');
        if (!panel) return;

        panel.addEventListener('click', (e) => {
          const label = e.target.closest('.el-cascader-node__label');
          if (!label) return;

          const node = label.closest('.el-cascader-node');
          if (!node) return;

          // 检查是否有子节点
          const hasChildren = node.classList.contains('in-active-path');

          if (!hasChildren) {
            e.stopPropagation();
            e.preventDefault();

            // 找到复选框并触发点击
            const checkbox = node.querySelector('.el-checkbox input');
            if (checkbox) {
              checkbox.click();
            }
          }
        });
      });
    },
    // 选中监管人员
    selectUser(data) {
      if (this.routerData.id) return;
      this.formData.jgrybm = data.jgrybm;
      this.formData.jgryxm = data.xm;
      this.formData.jsh = data.jsh;
      this.formData.jgryxx = data;
      this.setTalkBusinessId();
    },
    // 获取谈话教育业务ID
    setTalkBusinessId() {
      this.authPostRequest({
        url: this.$path.bsp_test_tem_grTalk_add,
        params: {
          jsh: this.formData.jsh,
          jgrybm: this.formData.jgrybm,
          // 创建时必须传且不能为空
          talkReason: ",",
        },
      }).then((res) => {
        if (res.success) {
          this.getTalkBusinessDetail(res.data);
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '预新增个别谈话教育操作失败！'
          })
        }
      });
    },
    // 通过获取到的谈话教育业务ID获取对应详情
    getTalkBusinessDetail(id) {
      this.loading = true;
      this.authGetRequest({
        url: this.$path.bsp_test_tem_grTalk_get,
        params: { id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.isDisabledState = false;
          Object.assign(this.formData, res.data);
        } else {
          this.$Message.error({
            title: "温馨提示",
            content: res.msg || "获取谈话教育详情失败!",
          });
        }
      });
    },
    // 获取谈话室列表
    getTalkRoomList() {
      this.roomListLoading = true;
      this.authGetRequest({
        url: this.$path.bsp_talk_getThs,
        params: {
          id: this.formData.id,
        },
      }).then((res) => {
        this.roomListLoading = false;
        if (res.success) {
          this.talkRoomList = res.data;
        } else {
          this.$Message.error(res.msg || "谈话室获取失败");
        }
      });
    },
    // 获取谈话原因列表
    getTalkReasonList() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: {
          dicName: "ZD_THJY_THYY",
        },
      }).then((res) => {
        function convertData(array) {
          return array.map((item) => {
            if (item.subTreeData && item.subTreeData.length === 0) {
              delete item.subTreeData;
            } else {
              convertData(item.subTreeData); // 递归处理子节点
            }

            return item;
          });
        }
        if (res.success) {
          if (Array.isArray(res.data)) {
            this.talkReasonList = convertData(res.data);
            this.initTemp(this.talkReasonList);
          }
        } else {
          this.$Message.error("获取谈话原因失败，请重试")
        }
      });
    },
    initTemp(newVal) {
      this.authPostRequest({
        url: this.$path.bsp_talk_listThyyTree,
        params: {
          json: JSON.stringify(newVal),
        },
      }).then((resp) => {
        if (resp.success) {
          this.talkReasonListTree = resp.data;
        }
      });
    },

    // 监听谈话方式变化
    handleChangeTalkWay(value) {
      this.talkMethod = value;
      if (value === "01") {
        this.whereIsVideo = "left";
        this.getTalkRoomList();
      } else if (value === "03") {
        this.whereIsVideo = "right";
        this.authGetRequest({
          url: this.$path.bsp_talk_sipconfig,
          params: {
            businessId: this.formData.id,
            jgrybm: this.formData.jgrybm,
          },
        }).then((res) => {
          if (res.success) {
            const {
              kyIp,
              kyPassword,
              kyPort,
              virtualAccount,
              targetAccount,
            } = res.data;
            this.sipConfig = {
              sipService: `${kyIp}:${kyPort}`,
              userName: virtualAccount,
              password: kyPassword,
              targetAccount,
            };

            this.sip(this.sipConfig).then((sip) => {
              if (sip.code === 200) {
                this.sipInstance = sip.data;
                this.sipInstance.sipTargetAccount =
                  this.sipConfig.targetAccount;
                this.videoUrl = sip.data.localStream;
                this.$refs.subVideo.createPlayer(this.videoUrl);
              } else {
                this.$Message.error(sip.msg);
                // 为了保证没有通话设备的情况下仍能进行谈话
                this.videoUrl = "no ip";
                this.sipInstance = {
                  noDevice: true
                };
              }
            });
          } else {
            // 为了保证没有通话设备的情况下仍能进行谈话
            this.videoUrl = "no ip";
            this.sipInstance = {
              noDevice: true
            };
            this.$Message.error(res.msg || "SIP账号获取失败");
          }
        });
      }
    },
    // 监听谈话室变化
    handleChangeTalkRoom(val) {
      this.updateVideoUrl(val.tag);
    },
    // 更新视频 Url
    updateVideoUrl(data) {
      if (data && data.prisonerDeviceIp) {
        const ip = data.prisonerDeviceIp;
        this.authGetRequest({
          url: this.$path.bsp_talk_device_getUrl,
          params: {
            ip,
          },
        }).then((res) => {
          if (res.success) {
            this.videoUrl = res.data;
            this.$refs[this.whereIsVideo + 'Video']?.createPlayer(this.videoUrl);
          } else {
            this.$Message.error(res.msg || "流地址获取失败");
            // 为了保证没有通话设备的情况下仍能进行谈话
            this.videoUrl = "no ip";
            this.$refs[this.whereIsVideo + 'Video']?.destroyPlayer();
          }
        });
      } else {
        this.$Message.error("未获取到面对面谈话设备地址，将在没有录像的情况下完成谈话");
        this.videoUrl = "no ip";
        this.$refs[this.whereIsVideo + 'Video']?.destroyPlayer();
      }
    },
    // 开始谈话
    handleStartTalk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 如果没有获取到设备
          if (this.videoUrl === "no ip" || this.sipInstance?.noDevice) {
            this.commonMethod("noDevice");
          } else {
            // 如果不是远程通话
            if (!this.sipInstance) {
              if (!this.videoUrl) {
                return this.$Message.error("面对面谈话设备ip不能为空");
              }
              // 调后端接口开始谈话
              this.authPostRequest({
                url: this.$path.bsp_talk_startRecord,
                params: {
                  inputUrl: this.videoUrl,
                  talkId: this.formData.id,
                },
              }).then((res) => {
                if (res.success) {
                  this.taskId = res.data.taskId;
                  this.commonMethod();

                  // 心跳
                  this.heartInterval = setInterval(() => {
                    this.sendHeartBeat();
                  }, 3000);
                }
              });
            } else {
              this.commonMethod();
              this.sipInstance
                .call(this.sipInstance.sipTargetAccount)
                .then((res) => {
                  if (res.code === 200) {
                    this.$refs.rightVideo.createPlayer(res.data.remoteStream);
                  } else {
                    this.$Message.error(res.msg);
                  }
                })
                .catch();
            }
          }
        } else {
          this.$Message.error("请选择填写必填表单后开始谈话")
        }
      });
    },
    // 结束谈话
    handleStopTalk() {
      // 如果是远程通话就挂断
      if (this.sipInstance?.hangup) {
        this.sipInstance.hangup();
      }
      // 暂停计时器
      if (this.timerInterval && this.heartInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        clearInterval(this.heartInterval);
        this.heartInterval = null;
      }
      // 调后端接口结束谈话
      this.authGetRequest({
        url: this.$path.bsp_talk_stopRecord + this.taskId,
      }).then((res) => {
        this.isTalkingState = false;
        if (res.success) {
          if (this.videoUrl !== "no ip" || this.sipInstance?.noDevice !== true) {
            this.$Message.success("视频已结束录制。");
          }

          this.getCurrentTime().then((resTime) => {
            this.formData.endTime = resTime.data || new Date();
            this.formData.talkContent = JSON.stringify(this.records);
            this.$refs.SubmitModal.open(this.formData);
          });
        } else {
          this.$Message.error("视频保存失败，请联系管理员。");
        }
      });
    },
    // 放大笔录
    handleZoomRecord() {
      this.$refs.RecordAreaModal.open(this.records);
    },
    handleShrinkRecord(records) {
      this.records = records;
      this.$refs.RecordArea.setRecord(records);
    },
    // 发送心跳
    sendHeartBeat() {
      this.authPostRequest({
        url: this.$path.bsp_talk_sendHeartBeat + this.taskId,
      });
    },
    // 通用方法
    commonMethod(type) {
      !type && this.$Message.success("视频已开始录制。");
      this.isTalkingState = true;
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }

      if (this.heartInterval) {
        clearInterval(this.heartInterval);
      }

      this.seconds = 0;
      this.formData.timer = "00:00:00";

      this.getCurrentTime().then((timer) => {
        if (timer) {
          this.formData.startTime = timer.data;
          this.$refs.RecordArea.handleStartRecord(timer.data);
          this.timerInterval = setInterval(() => {
            this.seconds++;
            this.formData.timer = this.dayjs.unix(this.seconds).utc().format('HH:mm:ss');
          }, 1000);
        } else {
          this.$Message.error("获取当前时间接口未返回，谈话无法正常开始。")
        }
      });
    },
    // 获取当前时间
    getCurrentTime() {
      return this.authGetRequest({
        url: this.$path.bsp_talk_tem_grTalk_time,
      }).then((res) => {
        return res;
      });
    },
    moveVideoTo(type) {
      this.whereIsVideo = type;
      this.$nextTick(() => {
        this.$refs[type + 'Video'].createPlayer(this.videoUrl);
      });
    },
    handleImportTemplate(template) {
      this.$refs.RecordArea.setSingleQA(template.question, template.answer);
    },
    handleImportAll(templates) {
      this.$refs.RecordArea.setAllQA(templates);
    },
    goList() {
      this.$refs.form.resetFields();
      this.$router.replace({ name: "individualTalkEducationList" });
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  line-height: 1.4;
  padding-left: 3px;
  border-left: 3px solid #0088ff;
}

/deep/ .fixColor.ivu-icon-md-expand::before {
  color: #333
}

/deep/ .ivu-form-item {
  margin-bottom: 15px;
}

/deep/ .ivu-form-item-content {
  margin-top: 3px;
}

.areaClassName .el-scrollbar__wrap {
  height: 390px !important;
}
</style>
