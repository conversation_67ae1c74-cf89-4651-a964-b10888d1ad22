<template>
    <div>
        <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />
            防误放终端信息
        </p>
        <div class="person-info-box" style="margin-bottom: 10px">
            <div style="width: 100%">
                <Row>
                    <Col span="5" class="col-title"><span>防误放终端核验结果</span></Col>
                    <Col span="19"><span :class="getCheckResultClass(fwfInfoData.checkResult)">{{
                        fwfInfoData.checkResultName
                        || '-'
                        }}</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title">强制类型</Col>
                    <Col span="19">{{ fwfInfoData.mandatoryTypeName || '-' }}</Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>强制原因</span></Col>
                    <Col span="19">{{ fwfInfoData.mandatoryReason || '-' }}</Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>经办民警</span></Col>
                    <Col span="7">{{ fwfInfoData.operatePolice || '-' }}</Col>
                    <Col span="5" class="col-title"><span>经办时间</span></Col>
                    <Col span="7">{{ fwfInfoData.operateTime || '-' }}</Col>
                </Row>
            </div>
        </div>
    </div>

</template>
<script>

export default {
  name: 'fwfDetail',
  props: {
    rowData: {
      type: [Array, Object],
      default: () => ({})
    }
  },
  data() {
    return {
      fwfInfoData: {},
    }
  },
  methods: {
    // 获取审批结果样式类
    getCheckResultClass(result) {
      if (result === '01') return 'timeline-badge pending'
      if (result === '02') return 'timeline-badge success'
      if (result === '03') return 'timeline-badge failed'
      return ''
    },
    getFwfInfo() {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getFwfInfoByOutRecordId,
        params: {
          businessId: this.rowData.id
        }
      }).then(resp => {
        this.loading = false
        if (resp.code == 0) {
          this.fwfInfoData = resp.data
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
  },
  mounted() {
    this.getFwfInfo()
  }
}
</script>
