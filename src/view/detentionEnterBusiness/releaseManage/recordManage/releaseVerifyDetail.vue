<template>
  <div class="detail-content" style="">
    <div class="fm-content-info bsp-base-content">
      <div class="fm-content-box">
        <p class="fm-content-info-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
        </p>
        <div class="person-info-box">
          <div class="">
            <img v-if="combineInfoData.frontPhoto" :src="combineInfoData.frontPhoto" class="personImg-box" alt="">
            <img v-else src="@/assets/images/detentionEnter/person.png" class="personImg-box" alt="">
          </div>
          <div style="width: 100%">
            <Row>
              <Col span="5" class="col-title"><span>姓名</span></Col>
              <Col span="7"><span>{{ combineInfoData.xm }}</span></Col>
              <Col span="5" class="col-title"><span>性别</span></Col>
              <Col span="7"><span>{{ combineInfoData.xb }}</span></Col>

            </Row>
            <Row>
              <Col span="5" class="col-title"><span>曾用名/别名/绰号</span></Col>
              <Col span="7"><span>{{ combineInfoData.bm }}</span></Col>
              <Col span="5" class="col-title"><span>监室号</span></Col>
              <Col span="7"><span>{{ rowData.room_name }}</span></Col>

            </Row>
            <Row>
              <Col span="5" class="col-title"><span>出生日期</span></Col>
              <Col span="7"><span>{{ combineInfoData.csrq }}</span></Col>
              <Col span="5" class="col-title"><span>涉嫌罪名</span></Col>
              <Col span="7"><span>{{ combineInfoData.sxzm }}</span></Col>
            </Row>
            <Row>
              <Col span="5" class="col-title"><span>入所时间</span></Col>
              <Col span="7"><span>{{ combineInfoData.rssj }}</span></Col>
              <Col span="5" class="col-title"><span>关押期限</span></Col>
              <Col span="7"><span>{{ combineInfoData.gyqx }}</span></Col>
            </Row>
            <Row>
              <Col span="5" class="col-title">
              </Col>
              <Col span="7">
              <Button style="margin: 0 13px 0 16px" type="primary"
                @click.native="showBaseInfoDialog()">查看入所登记详情</Button>
              </Col>
            </Row>
          </div>
        </div>
        <p class="fm-content-info-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />手环设备解绑信息
        </p>
        <Row>
          <Col span="3" class="col-title"><span>手环ID</span></Col>
          <Col span="5"><span>{{ formData.shid }}</span></Col>
          <Col span="3" class="col-title"><span>绑定状态</span></Col>
          <Col span="5"><span>{{ formData.shbdztName }}</span></Col>
          <Col span="3" class="col-title"><span>解除绑定时间</span></Col>
          <Col span="5"><span>{{ formData.sdjcbdsj }}</span></Col>
        </Row>
        <p class="fm-content-info-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />信息采集详情
        </p>
        <div class="man-model-content">
          <div style="width: 100%;">
            <Table :columns="columns" :data="dataTable" border>
              <template slot-scope="{ row, index }" slot="picture">

              </template>
            </Table>
          </div>
        </div>
        <fwfDetail :rowData="rowData"></fwfDetail>
        <p class="fm-content-info-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />经办信息
        </p>
        <Row>
          <Col span="3" class="col-title"><span>经办人</span></Col>
          <Col span="5"><span>{{ formData.jbr }}</span></Col>
          <Col span="3" class="col-title"><span>经办时间</span></Col>
          <Col span="5"><span>{{ formData.jbsj }}</span></Col>
        </Row>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
    </div>

    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="80%"
      title="查看详细信息">
      <div style="height: 75vh;overflow: auto;">
        <div class="fm-content-info bsp-base-content" style="padding:0px !important;">
          <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import baseInfoDetail from '../../detentionEnterManage/recordManage/detail'
import fwfDetail from "./components/fwfDetail.vue";
export default {
  components: {
    baseInfoDetail,
    fwfDetail
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
  },
  data() {
    return {
      formData: {
      },
      combineInfoData: {

      },
      openModal: false,
      columns: [
        {
          type: 'index',
          width: 70,
          align: 'center',
          title: '序号'
        },
        {
          title: '采集项目',
          key: 'cjxm',
          align: 'center',
        },
        {
          title: '详细信息',
          key: 'xxxx',
          align: 'center',
        },
        {
          title: '备注',
          key: 'bz',
          align: 'center',
        },
        {
          title: '核验结果',
          key: 'jg',
          align: 'center'
        }
      ],
      dataTable: [{
        cjxm: '指掌纹信息',
        xxxx: '',
        bz: '',
        jg: '',
      }, {
        cjxm: '虹膜信息',
        xxxx: '',
        bz: '',
        jg: '',
      }, {
        cjxm: '人像信息',
        xxxx: '',
        bz: '',
        jg: '',
      }, {
        cjxm: 'DNA信息',
        xxxx: '',
        bz: '',
        jg: '',
      }],
      fwfInfoData: {}

    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleClose() {
      this.$emit('close', false)
    },
    showBaseInfoDialog() {
      this.openModal = true;
    },
    getPrisonerSelectCompomenOne(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getPrisonerSelectCompomenOne,
        params: {
          jgrybm: rybh,
          ryzt: 'ALL'
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.combineInfoData = resp.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },

    getDetail(id) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_outRecordKssGet,
        params: {
          id: id
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.rowData.rybh = this.rowData.jgrybm
    this.getPrisonerSelectCompomenOne(this.rowData.jgrybm)
    this.getDetail(this.rowData.id)
  }
}
</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

.detail-content {
  height: 95%;
  overflow: hidden;
}

.person-info-box {
  display: flex;
  width: 100%;
  justify-content: center;

  .personImg-box {
    width: 180px;
    height: 207px;
    /*margin-top:10px;*/
  }
}

.man-model-content {
  display: flex;
  width: 100%;
  margin-top: 5px;
  margin-bottom: 5px;

  ::v-deep(.bsp-base-form .ivu-table th) {
    background: unset !important;
  }
}
</style>
