<template>
  <div class="detail-content"  >
    <div class="fm-content-info bsp-base-content" style="top: 55px !important;">
      <div class="fm-content-box">
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基本信息</p>
        <div class="person-info-box">
          <div class="">
            <img v-if="combineInfoData.frontPhoto" :src="combineInfoData.frontPhoto" class="personImg-box" alt="">
            <img v-else src="@/assets/images/detentionEnter/person.png" class="personImg-box" alt="">
          </div>
          <div style="width: 100%">
            <Row>
              <Col span="5" class="col-title"><span>姓名</span></Col>
              <Col span="7"><span>{{combineInfoData.xm}}</span></Col>
              <Col span="5" class="col-title"><span>性别</span></Col>
              <Col span="7"><span>{{combineInfoData.xb}}</span></Col>

            </Row>
            <Row>
              <Col span="5" class="col-title"><span>曾用名/别名/绰号</span></Col>
              <Col span="7"><span>{{combineInfoData.bm}}</span></Col>
              <Col span="5" class="col-title"><span>监室号</span></Col>
              <Col span="7"><span>{{combineInfoData.jsh}}</span></Col>

            </Row>
            <Row>
              <Col span="5" class="col-title"><span>出生日期</span></Col>
              <Col span="7"><span>{{combineInfoData.csrq}}</span></Col>
              <Col span="5" class="col-title"><span>涉嫌罪名</span></Col>
              <Col span="7"><span>{{combineInfoData.sxzm}}</span></Col>
            </Row>
            <Row>
              <Col span="5" class="col-title"><span>入所时间</span></Col>
              <Col span="7"><span>{{combineInfoData.rssj}}</span></Col>
              <Col span="5" class="col-title"><span>关押期限</span></Col>
              <Col span="7"><span>{{combineInfoData.gyqx}}</span></Col>
            </Row>
            <Row>
              <Col span="5" class="col-title"></Col>
              <Col span="7">
                <Button style="margin: 0 13px 0 16px" type="primary" @click.native="showBaseInfoDialog()">查看入所登记详情</Button>
              </Col>
            </Row>
          </div>
        </div>
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />出所登记信息</p>
        <Row>
          <Col span="3" class="col-title"><span>批准/执行机关</span></Col>
          <Col span="5"><span>{{formData.pzzxjg}}</span></Col>
          <Col span="3" class="col-title"><span>批准/执行人</span></Col>
          <Col span="5"><span>{{formData.pzzxr}}</span></Col>
          <Col span="3" class="col-title"><span>批准日期</span></Col>
          <Col span="5"><span>{{formData.pzrq}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>出所日期</span></Col>
          <Col span="5"><span>{{formData.cssj}}</span></Col>
          <Col span="3" class="col-title"><span>出所原因</span></Col>
          <Col span="5"><span>{{formData.csyyName}}</span></Col>
          <Col span="3" class="col-title"><span>出所凭证</span></Col>
          <Col span="5"><span>{{formData.cspzName}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>出所凭证文书</span></Col>
          <Col span="5">
            <file-upload
              :defaultList="cspzwsdzUrl"
              :serviceMark="serviceMark"
              :bucketName="bucketName"
              :beforeUpload="beforeUpload"
              v-if="cspzwsdzShowFile"
              :isDetail="true"
              @fileSuccess="fileSuccessFile"
              @fileRemove="fileRemoveFile"
              @fileComplete="fileCompleteFile" />
          </Col>
          <Col span="3" class="col-title"><span>法律文书号</span></Col>
          <Col span="5"><span>{{formData.cspzwsh}}</span></Col>
          <Col span="3" class="col-title"><span>出所去向</span></Col>
          <Col span="5"><span>{{formData.csqx}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>档案材料移交情况</span></Col>
          <Col span="5"><span>{{formData.daclyjqk}}</span></Col>
          <Col span="3" class="col-title"><span>档案材料</span></Col>
          <Col span="5">
            <file-upload
              :defaultList="daclyjstUrl"
              :serviceMark="serviceMark"
              :bucketName="bucketName"
              :beforeUpload="beforeUpload"
              v-if="daclyjstShowFile"
              :isDetail="true"
              @fileSuccess="fileSuccessFile"
              @fileRemove="fileRemoveFile"
              @fileComplete="fileCompleteFile" />
          </Col>
          <Col span="3" class="col-title"><span>转去公安监所编码</span></Col>
          <Col span="5"><span>{{formData.zqgajsbm}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>转去公安监所名称</span></Col>
          <Col span="5"><span>{{formData.zqgajsmcName}}</span></Col>
          <Col span="3" class="col-title"><span>经办人</span></Col>
          <Col span="5"><span>{{formData.jbr}}</span></Col>
          <Col span="3" class="col-title"><span>经办时间</span></Col>
          <Col span="5"><span>{{formData.jbsj}}</span></Col>
        </Row>
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />出所检查信息</p>
        <Row>
          <Col span="3" class="col-title"><span>是否为他人捎带物品</span></Col>
          <Col span="5"><span>{{formData.sfwtrsdwpName}}</span></Col>
          <Col span="3" class="col-title"><span>物品类型</span></Col>
          <Col span="5"><span>{{formData.sdwplxName}}</span></Col>
          <Col span="3" class="col-title"><span>是否有帮助他人串供行为</span></Col>
          <Col span="5"><span>{{formData.sfybzqtrcgxwName}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>详细情况记录</span></Col>
          <Col span="5"><span>{{formData.xxqkjl}}</span></Col>
          <Col span="3" class="col-title"><span>是否发现其他违法犯罪行为</span></Col>
          <Col span="5"><span>{{formData.sffxqtwffzxsName}}</span></Col>
          <Col span="3" class="col-title"><span>其他违法犯罪行为</span></Col>
          <Col span="5"><span>{{formData.qtwffzxw}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>经办人</span></Col>
          <Col span="5"><span>{{formData.jbr}}</span></Col>
          <Col span="3" class="col-title"><span>经办时间</span></Col>
          <Col span="5"><span>{{formData.jbsj}}</span></Col>
        </Row>
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />财务交接信息</p>
        <Row>
          <Col span="3" class="col-title"><span>财物交接情况</span></Col>
          <Col span="5"><span>{{formData.cwjjqk}}</span></Col>
          <Col span="3" class="col-title"><span>财物交接手续</span></Col>
          <Col span="5">
            <file-upload
              :defaultList="cwjjstUrl"
              :serviceMark="serviceMark"
              :bucketName="bucketName"
              :beforeUpload="beforeUpload"
              v-if="cwjjstShowFile"
              :isDetail="true"
              @fileSuccess="fileSuccessFile"
              @fileRemove="fileRemoveFile"
              @fileComplete="fileCompleteFile" />
          </Col>
        </Row>


        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />物品取出信息</p>
        <div class="man-model-content">
          <div style="width: 100%;">
            <Table :columns="belongingsColumns" :data="belongingsDataTable" border>
              <template slot-scope="{ row, index }" slot="wpzp">
                <div v-if="row.zpList && row.zpList.length > 0">
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="row.zpList[0]"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="row.zpList"
                    fit="cover"
                  />
                </div>
              </template>
            </Table>
          </div>
        </div>

        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />取出登记信息</p>
        <Row>
          <Col span="3" class="col-title"><span>取出原因</span></Col>
          <Col span="5"><span>{{formData.qcyyName}}</span></Col>
          <Col span="3" class="col-title"><span>取出方式</span></Col>
          <Col span="5"><span>{{formData.qcfsName}}</span></Col>
          <Col span="3" class="col-title"><span>备注</span></Col>
          <Col span="5"><span>{{formData.bz}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>经办人</span></Col>
          <Col span="5"><span>{{formData.jbr}}</span></Col>
          <Col span="3" class="col-title"><span>经办时间</span></Col>
          <Col span="5"><span>{{formData.jbsj}}</span></Col>
        </Row>

        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />领导审批结论</p>
          <div class="form">
            <approvalInfo :actInstId="rowData.act_inst_id"></approvalInfo>
          </div>
        </div>
<!--        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />领导审批信息</p>-->
<!--        <Row>-->
<!--          <Col span="3" class="col-title"><span>审批结果</span></Col>-->
<!--          <Col span="5"><span>{{formData.spztName}}</span></Col>-->
<!--          <Col span="3" class="col-title"><span>审批意见</span></Col>-->
<!--          <Col span="5"><span>{{formData.approvalResult}}</span></Col>-->
<!--          <Col span="3" class="col-title"><span>经办人</span></Col>-->
<!--          <Col span="5"><span>{{formData.jbr}}</span></Col>-->
<!--        </Row>-->
<!--        <Row>-->
<!--          <Col span="3" class="col-title"><span>经办时间</span></Col>-->
<!--          <Col span="5"><span>{{formData.jbsj}}</span></Col>-->
<!--        </Row>-->

        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />手环设备解绑信息</p>
        <Row>
          <Col span="3" class="col-title"><span>手环ID</span></Col>
          <Col span="5"><span>{{formData.shid}}</span></Col>
          <Col span="3" class="col-title"><span>绑定状态</span></Col>
          <Col span="5"><span>{{formData.shbdztName}}</span></Col>
          <Col span="3" class="col-title"><span>解除绑定时间</span></Col>
          <Col span="5"><span>{{formData.sdjcbdsj}}</span></Col>
        </Row>
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />信息采集详情</p>
        <div class="man-model-content">
          <div style="width: 100%;">
            <Table :columns="columns" :data="dataTable" border>
              <template slot-scope="{ row, index }" slot="picture">

              </template>
            </Table>
          </div>
        </div>
        <fwfDetail :rowData="rowData"></fwfDetail>
        <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />离所确认登记信息</p>
        <Row>
          <Col span="3" class="col-title"><span>信息确认</span></Col>
          <Col span="5"><span>{{formData.lsqrztName}}</span></Col>
          <Col span="3" class="col-title"><span>出所去向</span></Col>
          <Col span="5"><span>{{formData.lsqrqx}}</span></Col>
          <Col span="3" class="col-title"><span>备注</span></Col>
          <Col span="5"><span>{{formData.lsqrbz}}</span></Col>
        </Row>
        <Row>
          <Col span="3" class="col-title"><span>经办人</span></Col>
          <Col span="5"><span>{{formData.lsqrjbr}}</span></Col>
          <Col span="3" class="col-title"><span>经办时间</span></Col>
          <Col span="5"><span>{{formData.lsqrjbsj}}</span></Col>
        </Row>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
    </div>

    <Modal
      v-model="openModal"
      :mask-closable="false"
      :closable="true"
      class-name="select-sy-modal"
      width="80%"
      title="查看详细信息"
    >
      <div style="height: 75vh;overflow: auto;">
        <div class="fm-content-info bsp-base-content" style="padding:0px !important;">
          <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
  import {mapActions} from "vuex";
  import baseInfoDetail from '../../detentionEnterManage/recordManage/detail'
  import { fileUpload } from 'sd-minio-upfile'
  import approvalInfo from "../../releaseApprovalManage/registrationManage/approvalInfo"
  import fwfDetail from "./components/fwfDetail.vue";
  export default {
    components:{
      baseInfoDetail,fileUpload,approvalInfo,fwfDetail
    },
    props:{
      rowData:{
        type: [Array,Object],
        default: () => ({})
      },
    },
    data(){
      return{
        formData: {
        },
        combineInfoData:{

        },
        cspzwsdzUrl:[],
        cspzwsdzShowFile:false,
        daclyjstUrl:[],
        daclyjstShowFile:false,
        cwjjstUrl:[],
        cwjjstShowFile:false,
        serviceMark: serverConfig.OSS_SERVICE_MARK,
        bucketName: serverConfig.bucketName,
        openModal:false,
        belongingsColumns:[
          {
            type: 'index',
            width: 70,
            align: 'center',
            title:'序号'
          },
          {
            title: '物品名称',
            key: 'wpmcName',
            align: 'center',
          },
          {
            title: '类型',
            key: 'zlName',
            align: 'center',
          },
          {
            title: '单位',
            key: 'hbzlName',
            align: 'center',
          },
          {
            title: '数量',
            key: 'sl',
            align: 'center',
          },
          {
            title: '特征',
            key: 'wptz',
            align: 'center',
          },
          {
            title: '位置',
            key: 'wz',
            align: 'center',
          },
          {
            title: '备注',
            key: 'bz',
            align: 'center',
          },
          {
            title: '照片',
            slot: 'wpzp',
            align: 'center',
          },
          {
            title: '取出物品',
            key: 'qcwp',
            align: 'center',
          },
        ],
        belongingsDataTable:[],
        columns:[
          {
            type: 'index',
            width: 70,
            align: 'center',
            title:'序号'
          },
          {
            title: '采集项目',
            key: 'cjxm',
            align: 'center',
          },
          {
            title: '详细信息',
            key: 'xxxx',
            align: 'center',
          },
          {
            title: '备注',
            key: 'bz',
            align: 'center',
          },
          {
            title: '核验结果',
            key: 'jg',
            align: 'center'
          }
        ],
        dataTable:[{
          cjxm:'指掌纹信息',
          xxxx:'',
          bz:'',
          jg:'',
        },{
          cjxm:'虹膜信息',
          xxxx:'',
          bz:'',
          jg:'',
        },{
          cjxm:'人像信息',
          xxxx:'',
          bz:'',
          jg:'',
        },{
          cjxm:'DNA信息',
          xxxx:'',
          bz:'',
          jg:'',
        }],

      }
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      handleClose(){
        this.$emit('close',false)
      },
      showBaseInfoDialog(){
        this.openModal = true;
      },
      getPrisonerSelectCompomenOne(rybh){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_getPrisonerSelectCompomenOne,
          params: {
            jgrybm:rybh,
            ryzt:'ALL'
          }
        }).then(resp => {
          if(resp.code == 0){
            this.combineInfoData = resp.data
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      getBelongingsDetail(rybh){
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_personalEffectsSubGetList,
          params: {
            rybh:rybh
          }
        }).then(resp => {
          if(resp.code == 0 && resp.data){
            this.belongingsDataTable = []

            if(resp.data && resp.data.length > 0){
              for(let i = 0; i < resp.data.length; i++){
                let data = resp.data[i]
                data.zpList = data.wpzp ? JSON.parse(data.wpzp): []

                let count = 0
                if(data.sl){
                  const num = parseInt(data.sl, 10);
                  count = isNaN(num) ? 0 : num;
                }
                data.qcwp = count
                this.belongingsDataTable.push(data)
              }
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      getDetail(id){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_outRecordKssGet,
          params: {
            id:id
          }
        }).then(resp =>{
          if(resp.code == 0){
            this.formData = resp.data
            if(this.formData.cspzwsdz){
              this.cspzwsdzUrl = JSON.parse(this.formData.cspzwsdz)
              this.cspzwsdzShowFile = true
            }
            if(this.formData.daclyjst){
              this.daclyjstUrl = JSON.parse(this.formData.daclyjst)
              this.daclyjstShowFile = true
            }
            if(this.formData.cwjjst){
              this.cwjjstUrl = JSON.parse(this.formData.cwjjst)
              this.cwjjstShowFile = true
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      beforeUpload(){},
      fileSuccessFile(){},
      fileRemoveFile(){},
      fileCompleteFile(data,index){
      },
    },
    mounted(){
      this.rowData.rybh = this.rowData.jgrybm
      this.getPrisonerSelectCompomenOne(this.rowData.jgrybm)
      this.getDetail(this.rowData.id)
      this.getBelongingsDetail(this.rowData.jgrybm)
      // this.getBelongingsDetail("5834453663440390943519")
    }
  }
</script>

<style scoped lang="less">
  @import "~@/assets/style/formInfo.css";
  .bsp-imgminio-container{
    width:100% !important;
  }
  .detail-content{
    height: 95%;
    overflow: hidden;
  }
  .person-info-box{
    display: flex;
    width: 100%;
    justify-content: center;
    .personImg-box{
      width: 180px;
      height: 207px;
      /*margin-top:10px;*/
    }
  }
  .man-model-content{
    display: flex;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 5px;
    ::v-deep(.bsp-base-form .ivu-table th){
      background: unset !important;
    }
  }
</style>
