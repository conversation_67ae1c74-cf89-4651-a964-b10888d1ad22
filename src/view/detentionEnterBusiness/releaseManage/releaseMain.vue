<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <el-steps :space="110" :active="current" direction="vertical" class="steps-style">
      <el-step v-for="(step, index) in steps" :key="index">
        <template #title>
          <div :class="index==current ? 'detention-step detention-step-active' :'detention-step detention-step-unActive'" @click="skipStep(step,index)">
            <span>{{ step.title }}</span>
          </div>
        </template>
      </el-step>
<!--      <el-step title="出所登记"></el-step>-->
<!--      <el-step title="出所检查"></el-step>-->
<!--      <el-step title="财物交接与物品取出"></el-step>-->
<!--      <el-step title="领导审批"></el-step>-->
<!--      <el-step title="防误放验证"></el-step>-->
<!--      <el-step title="离所确认"></el-step>-->
    </el-steps>
    <div v-if="current === 0" class="release-step-box">
      <releaseForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></releaseForm>
      <releaseRegistrationDetail v-else :rowData="rowData" @close="handleClose"></releaseRegistrationDetail>
    </div>
    <div v-if="current === 1" class="release-step-box">
      <releaseCheckForm v-if="steps[current].status != '03'" ref="releaseCheckForm" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></releaseCheckForm>
      <releaseCheckDetail v-else :rowData="rowData" @close="handleClose"></releaseCheckDetail>
    </div>
    <div v-if="current === 2" class="release-step-box">
      <financialHandoverForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></financialHandoverForm>
      <financialHandoverDetail v-else :rowData="rowData" @close="handleClose"></financialHandoverDetail>
    </div>
    <div v-if="current === 3" class="release-step-box">
      <releaseApprovalForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></releaseApprovalForm>
      <releaseApprovalDetail v-else :rowData="rowData" @close="handleClose"></releaseApprovalDetail>
    </div>
    <div v-if="current === 4" class="release-step-box">
      <releaseVerifyForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></releaseVerifyForm>
      <releaseVerifyDetail v-else :rowData="rowData" @close="handleClose"></releaseVerifyDetail>
    </div>
    <div v-if="current === 5" class="release-step-box">
      <releaseConfirmForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :rowData="rowData"></releaseConfirmForm>
      <releaseConfirmDetail v-else :rowData="rowData" @close="handleClose"></releaseConfirmDetail>
    </div>

  </div>
</template>

<script>
  import releaseForm from "./registrationManage/releaseForm";
  import releaseCheckForm from "../releaseCheckManage/registrationManage/addForm"
  import financialHandoverForm from "../financialHandoverManage/registrationManage/addForm"
  import releaseApprovalForm from "../releaseApprovalManage/registrationManage/addForm"
  import releaseVerifyForm from "../releaseVerifyManage/registrationManage/addForm"
  import releaseConfirmForm from "../releaseConfirmManage/registrationManage/addForm"

  import releaseRegistrationDetail from "./recordManage/releaseRegistrationDetail";
  import releaseCheckDetail from "./recordManage/releaseCheckDetail";
  import financialHandoverDetail from "./recordManage/financialHandoverDetail";
  import releaseApprovalDetail from "./recordManage/releaseApprovalDetail";
  import releaseVerifyDetail from "./recordManage/releaseVerifyDetail";
  import releaseConfirmDetail from "./recordManage/releaseConfirmDetail";

  export default {
    components:{
      releaseForm,
      releaseCheckForm,
      financialHandoverForm,
      releaseApprovalForm,
      releaseVerifyForm,
      releaseConfirmForm,

      releaseRegistrationDetail,
      releaseCheckDetail,
      financialHandoverDetail,
      releaseApprovalDetail,
      releaseVerifyDetail,
      releaseConfirmDetail
    },
    props:{
      rowData:{
        type: [Array,Object],
        default: () => ({})
      },
    },
    data(){
      return{
        realityStep:0,  // 实际已经到的流程
        current:0,
        steps: [
          { title: '出所登记', status: '01' },
          { title: '出所检查', status: '01' },
          { title: '财物交接与物品取出', status: '01' },
          { title: '领导审批', status: '01' },
          { title: '防误放验证', status: '01'},
          { title: '离所确认', status: '01' },
        ]
      }
    },
    methods:{
      handleClose(){
        this.$emit('close',false)
      },
      skipStep(step,index){
        if(this.realityStep< index){
          this.$Message.error('请先完成当前流程!!');
          return
        }
        this.current = index;
      },
      handleNextStep(){
        this.current++
        this.realityStep = this.current
        if(this.current == 6){
          this.handleClose()
        }
        if(this.current == 1){
          // this.$refs.releaseCheckForm.getDetail(this.rowData.jgrybm)
        }
      }
    },
    mounted() {
      let status =  Number(this.rowData.current_step)
      if(isNaN(status)){
        this.current = 0
      }
      if(status > 0){
        this.current = status - 1;
      }else{
        this.current = 0
      }
      this.realityStep = this.current
      this.steps.forEach((item,index)=>{
        if(index < this.current){
          item.status = '03'
        }
        // if(index = this.current){
        //   item.status = this.rowData.status
        // }
      })
    }
  }
</script>

<style scoped lang="less">
  .steps-style{
    float: left;
    width:15%;
    margin-left: 20px;
    margin-top: 30px;
  }
  .release-step-box{
    height: 100%;
    width: 80%;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .detention-step{
    border-radius: 6px;
    border: 1px solid rgba(187,187,187,1);
    height: 79px;
    width: 214px;
    text-align: left;
    display: flex;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 20px;
      margin-left: 20px;
    }
  }
  .detention-step-active{
    background-color: rgba(49,127,245,1);
    span{
      color: rgba(255,255,255,1);

    }
  }
  .detention-step-unActive{
    background-color: rgba(232,232,232,1);
    span{
      color: rgba(16,16,16,1);
    }
  }
</style>
