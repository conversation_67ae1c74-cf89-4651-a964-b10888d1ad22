<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;padding:unset !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon
        style="padding: 0 .625rem;position: relative;">
        <div class="fm-content-box">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{ combineInfoData.roomName }}</span></Col>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{ combineInfoData.xm }}</span></Col>
            <Col span="3" class="col-title"><span>曾用名/别名/绰号</span></Col>
            <Col span="5"><span>{{ combineInfoData.bm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{ combineInfoData.xbName }}</span></Col>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{ combineInfoData.csrq }}</span></Col>
            <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
            <Col span="5"><span>{{ combineInfoData.sxzm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>收押登记时间</span></Col>
            <Col span="5"><span>{{ rowData.add_time }}</span></Col>
            <Col span="0" class="col-title"><span></span></Col>
            <Col span="8">
            <Button style="margin: 0 13px 0 16px" type="primary" @click.native="showBaseInfoDialog()">查看入所登记详情</Button>
            </Col>
          </Row>
        </div>
        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />信息采集详情
          </p>
          <div class="form">
            <Row></Row>
            <Button type="primary" @click.native="getGatherStatus()" style="margin-bottom: 10px;">获取采集状态</Button>
            <Row>
              <Col span="24">
              <Table :columns="columns" :data="dataTable" border>
                <template slot-scope="{ row, index }" slot="biometric">
                  <div class="img-box-lsit">
                    <!-- <template v-for="(item,index) in row.biometric" >
                      <img :src="item.url"   v-if="item.url !== ''">
                    </template> -->
                    <template v-for="(item, index) in row.biometric">
                      <Tooltip :content="contentText(row, item)" placement="top" :key="index" v-if="item.url !== ''">
                        <el-image style="width: 50px; height: 50px" :src="item.url"
                          :preview-src-list="getPreviewList(row.biometric)">
                        </el-image>
                      </Tooltip>
                    </template>


                  </div>
                </template>
                <template slot-scope="{ row, index }" slot="bz">
                  <Input type="text" v-model="row.bz" @on-blur="bzChange(row, index)" />
                </template>

                <template slot-scope="{ row, index }" slot="action">
                  <div class="action-gather">
                    <Button type="primary" style="margin-right: 5px" @click="startGather(row.itemName)"
                      v-if="row.cjxmlx != '04'" :loading="gatherLoading[row.itemName]">开始采集</Button>
                    <!--                    <Button type="primary"  @click="upload(index)">手动上传</Button>-->
                    <file-upload :defaultList="row.swtzUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                      :beforeUpload="beforeUpload" v-if="showFile" @fileSuccess="fileSuccessFile"
                      @fileRemove="fileRemoveFile" @fileComplete="(data) => fileCompleteFile(data, index)" />
                  </div>

                </template>
              </Table>
              </Col>
            </Row>
          </div>
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />手环设备绑定
          </p>
          <div class="form">
            <Row>
              <Col span="8">
              <FormItem label="手环ID" prop="shid">
                <Input type="text" v-model="formData.shid" placeholder="请填写" />
              </FormItem>
              </Col>
              <!-- <Col span="8">
              <FormItem label="绑定状态" prop="shbdzt">
                <s-dicgrid v-model="formData.shbdzt" @change="$refs.formData.validateField('shbdzt')" :isSearch="true"
                  dicName="ZD_SYRSSHBDZT" :disabled="true" />
              </FormItem>
              </Col> -->
              <!-- <Col span="8">
              <Button type="primary" style="margin: 0 13px 0 16px" @click="wristbandBinding()">开始绑定</Button>
              </Col> -->
              <!-- </Row>
            <Row> -->
              <Col span="8">
              <FormItem label="绑定时间" prop="sdbdsj">
                <div class="ivu-form-item-label">{{ formData.sdbdsj }}</div>
              </FormItem>
              </Col>

            </Row>

          </div>
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />经办信息
          </p>
          <div class="form">
            <Row>
              <Col span="8">
              <FormItem label="经办人" prop="jbr" :rules="[{ trigger: 'blur', message: '经办人为必填', required: true }]">
                <Input type="text" v-model="formData.jbr" placeholder="系统自动获取登录人信息" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="经办时间" prop="jbsj">
                <div class="ivu-form-item-label">{{ formData.jbsj }}</div>
              </FormItem>
              </Col>

            </Row>
          </div>
        </div>
      </Form>

    </div>
    <Spin size="large" fix v-if="spinShow"></Spin>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>

    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="80%"
      title="入所登记详情">
      <div style="height: 75vh;overflow: auto;">
        <div class="fm-content-info bsp-base-content" style="padding:0px !important;">
          <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
        </div>
      </div>
    </Modal>

    <start-approval ref="approval" :assigneeUserId="approvalData.assigneeUserId"
      :assigneeUserName="approvalData.assigneeUserName" :assigneeOrgId="approvalData.assigneeOrgId"
      :assigneeOrgName="approvalData.assigneeOrgName" :assigneeAreaId="approvalData.assigneeAreaId"
      :assigneeAreaName="approvalData.assigneeAreaName" :definition="approvalData.definition" :bindEvent="false"
      :showcc="false" :error="startError" :businessId="approvalData.businessId" :variables="approvalData.variables"
      :startUpSuccess="startUpSuccess" :beforeOpen="beforeOpen" :msgUrl="msgUrl" :msgTit="msgTit"
      :module="module"></start-approval>
  </div>
</template>

<script>
import HisignPICS, { getCurrentTime } from '@/assets/js/device/inforCollection/pics.js';
import { startApproval } from 'gs-start-approval'
import { fileUpload } from 'sd-minio-upfile'
import { mapActions } from "vuex";
import { getUserCache, getCurrentTimeFormatted } from '@/libs/util'
import baseInfoDetail from '../../detentionEnterManage/recordManage/detail'
import { Row } from 'view-design';
export default {
  components: {
    baseInfoDetail, fileUpload, startApproval
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
    entireProcess: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      combineInfoData: {

      },
      formData: {
        // shbdzt: "01"
      },
      loading: false,
      loadingSave: false,
      ruleValidate: {},
      openModal: false,
      showFile: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      columns: [
        {
          type: 'index',
          width: 70,
          align: 'center',
          title: '序号'
        },
        {
          title: '采集项目',
          key: 'cjxm',
          align: 'center',
          width: 200,
        },
        {
          title: '详细信息',
          slot: 'biometric',
          align: 'center',
        },
        {
          title: '备注',
          slot: 'bz',
          align: 'center',
          width: 200,
        },
        {
          title: '操作',
          slot: 'action',
          width: 300,
          align: 'center'
        }
      ],
      dataTable: [],
      msgUrl: '/#/detentionBusiness/detentionEnterRegister',
      msgTit: '【审批】收押入所',
      businessId: this.rowData.rybh,
      module: serverConfig.APP_MARK,
      approvalData: {
        definition: [
          {
            name: '入所审批流程',
            defKey: 'shouyarusuoshenpiliuchengkanshousuo'
          }
        ],
        assigneeOrgId: this.$store.state.common.orgCode,
        assigneeOrgName: this.$store.state.common.orgName,
        assigneeUserId: this.$store.state.common.idCard,
        assigneeUserName: this.$store.state.common.userName,
        businessId: this.rowData.rybh,
        fApp: serverConfig.APP_MARK,
        fXxpt: 'pc',
        variables: {
          eventCode: this.rowData.rybh,
          busType: '105_001'
        }
      },
      isTest: false,  //演示用
      mockData: {},
      gatherLoading: {}, // 用于存储每个采集项的loading状态
      spinShow: false
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleClose() {
      this.$emit('close', false)
    },
    handleNext() {
      this.$emit('nextStep', false)
    },
    showBaseInfoDialog() {
      this.openModal = true;
    },
    getCombineInfoDetail(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getCombineInfo,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          if (resp.data) {
            this.combineInfoData = resp.data
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    bzChange(row, index) {
      if (row.bz != this.dataTable[index].bz) {
        this.dataTable[index].bz = row.bz
        this.submitBiometricInfo(this.dataTable[index])
      }
    },
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.dataTable[index].swtzfj = JSON.stringify(data)
        this.submitBiometricInfo(this.dataTable[index])
      }
    },
    submitBiometricInfo(data) {
      data.status = "02"
      data.businessType = "kss"
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_biometricInfoCreate,
        params: data
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          // this.$Message.success('提交成功!');

        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    getBiometricInfo(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getBiometricInfoByRybh,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          console.log("生物信息采集信息---")
          if (resp.data && resp.data.length > 0) {
            for (let i = 0; i < this.dataTable.length; i++) {
              let data = resp.data.find(t => t.cjxmlx == this.dataTable[i].cjxmlx)
              if (data) {
                this.dataTable[i].id = data.id;
                this.dataTable[i].bz = data.bz;
                this.dataTable[i].swtz = data.swtz;
                this.dataTable[i].biometric = data.biometric;
                this.dataTable[i].swtzUrl = []
                if (data.swtzfj) {
                  this.dataTable[i].swtzUrl = JSON.parse(data.swtzfj)
                }


              }
            }
            this.showFile = true
            console.log(this.dataTable, "this.dataTable------")
          }
        } else {
          this.showFile = true
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    getPrisonerInfo(rybh) {
      this.spinShow = true
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getPrisonerInfo,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
          this.formData.jbsj = resp.data.jbsj ? resp.data.jbsj : getCurrentTimeFormatted();
          this.formData.sdbdsj = resp.data.sdbdsj ? resp.data.sdbdsj : getCurrentTimeFormatted();
          this.formData.shbdzt = resp.data.shbdzt ? resp.data.shbdzt : "01";
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
        this.spinShow = false
      })
    },
    startGather(itemName) {
      this.$set(this.gatherLoading, itemName, true)
      console.log("开始采集")
      setTimeout(() => {
        this.mockData.CJSJ = getCurrentTime(this.mockData.CJSJ)
        let resp = HisignPICS.StartItemCollect(this.rowData.rybh, this.mockData, itemName, this.callBack);
        console.log(JSON.stringify(resp))
        this.$set(this.gatherLoading, itemName, false)
      }, 500);
      // this.gatherLoading[itemName] = false





    },
    getGatherStatus() {
      this.authGetRequest({ url: this.$path.db_getInRecordStatusKss, params: { rybh: this.rowData.rybh } }).then(res => {
        if (res.success) {
          if (res.data.xxcj == '01') {
            this.$Message.error('未采集数据');
          } else if (res.data.xxcj == '03') {
            this.$Message.success('已采集数据');
          } else if (res.data.xxcj == '02') {
            this.$Message.error('正在采集数据');
          }

        }
      })
    },
    callBack(re) {
      console.error(re)
      this.getBiometricInfo(this.rowData.rybh)
      // if (res.code) {
      //   console.log('-----------------------采集-----------------------------');
      //   console.log(res);

      // }
    },
    getCollectedPersonDetail(rybh) {
      let params = {
        jgrybm: rybh,
        businessType: 'kss',
        orgCode: getUserCache.getOrgCode()

      }
      this.$store.dispatch('authGetRequest', {
        url: this.$path.db_getCollectedPersonDetail,
        params: params
      }).then(resp => {
        if (resp.success) {
          this.mockData = resp.data
          console.log("------获取采集人员信息---", resp)
        } else {
          this.$Modal.error({
          })
        }
      })
    },
    upload() {

    },
    wristbandBinding() {
      if (!this.formData.shid) return this.$Message.error('请填写腕带ID')
      let params = {
        bindPersonId: this.combineInfoData.rybh,
        bindPersonName: this.combineInfoData.xm,
        bindTime: this.dayjs().format('YYYY-MM-DD HH:mm:ss'),
        tagId: this.formData.shid

      }
      console.log(params);
      this.$store.dispatch('authPostRequest', { url: this.$path.pm_rsCreate, params: params }).then(res => {
        if (res.success) {
          this.$Message.success('绑定成功')
          this.getCombineInfoDetail(this.rowData.rybh)
        } else {
          this.$Message.error(res.message)
        }
      })

    },
    handleSubmit(tag) {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.saveData(tag)
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!');
        }
      })
    },
    saveData(tag) {
      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      // let params=this.formData
      let params = {}
      params.rybh = this.rowData.rybh
      params.ryxm = this.combineInfoData.xm
      params.sdbdsj = this.formData.sdbdsj
      // params.shbdzt = this.formData.shbdzt
      params.shid = this.formData.shid
      // params.sdbdsj = this.formData.sdbdsj
      params.businessType = "kss"
      if (tag) {
        this.loading = true
        params.status = "03"
      } else {
        this.loadingSave = true
        params.status = "02"
      }
      params.rslx = this.rowData.rslx
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_biometricInfoAddInformation,
        params: params
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          if (this.entireProcess && tag) {
            if (resp.data.isApproval) {
              if (this.rowData.rslx == "01") {
                this.businessId = this.rowData.rybh
                this.msgUrl = '/#/detentionBusiness/detentionEnterRegister?eventCode=' + this.rowData.rybh
                this.$refs['approval'].openStartApproval()
              }
              else {
                this.handleClose();
              }
            } else {
              this.handleClose();
            }
          } else {
            this.handleClose();
          }

        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    startError(data) {
      this.errorModal({ content: '流程启动失败。原因:' + data.msg }).then(() => {
        location.reload()
      })
    },
    startUpSuccess(data) {
      return new Promise((resolve, reject) => {
        let that = this
        setTimeout(() => {
          this.updateRegStatus(data)
        }, 500)
      })
    },
    updateRegStatus(data) {
      let params = {
        rybh: this.rowData.rybh,
        taskId: "",
        actInstId: data.actInstId
      }
      //调用接口
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_detainRegKssUpdateWorkflowInfo,
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          if (this.isTest) {
            let info = {
              rybh: this.rowData.rybh,
              rslx: this.rowData.rslx,
              act_inst_id: params.actInstId
            }
            this.handleNext(info);
          } else {
            this.handleClose();
          }
        } else {
          this.$Message.error(resp.msg);
        }
      })
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {
        this.$set(this.approvalData, 'businessId', this.rowData.rybh)
        this.msgUrl = '/#/detentionBusiness/detentionEnterRegister?eventCode=' + this.rowData.rybh
        this.msgTit = `【收押入所审批】民警：${this.formData.jbr}于${this.formData.jbsj}提交了对${this.combineInfoData.xm}收押入所的申请，请尽快审批！`
        resolve(true)
      })
    },
    contentText(row, ite) {
      if (row.cjxmlx == '01') {
        let name = row.biometric.find(item => item.zwdm == ite.zwdm)
        return name.zwdmName
      } else if (row.cjxmlx == '02') {
        let name = row.biometric.find(item => item.xxzldf == ite.xxzldf)
        return name.hmywdmName + name.hmqsqkdmName
      } else if (row.cjxmlx == '03') {
        let name = row.biometric.find(item => item.ryzplxdm == ite.ryzplxdm)
        return name.ryzplxdmName
      } else if (row.cjxmlx == '04') {
        return 'DNA信息'

      }
    },
    // 提取所有可预览的图片 URL
    getPreviewList(biometricList) {
      return biometricList
        .map(item => item.url)
        .filter(url => url); // 过滤掉空值
    },
  },
  mounted() {
    this.formData.jbsj = getCurrentTimeFormatted();
    this.formData.sdbdsj = getCurrentTimeFormatted();
    this.$set(this.formData, 'jbr', getUserCache.getUserName())
    this.dataTable = [{
      cjxmlx: '01',
      cjxm: '指掌纹信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'finger',
      swtz: ''
    }, {
      cjxmlx: '02',
      cjxm: '虹膜信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'iris',
      swtz: ''
    }, {
      cjxmlx: '03',
      cjxm: '人像信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'photo',
      swtz: ''
    }, {
      cjxmlx: '04',
      cjxm: 'DNA信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'dna',
      swtz: ''
    }]
    this.getCombineInfoDetail(this.rowData.rybh)
    this.getCollectedPersonDetail(this.rowData.rybh)
    if (this.entireProcess) {
      if (this.rowData.rybh) {
        this.getPrisonerInfo(this.rowData.rybh)
        this.showFile = false
        this.getBiometricInfo(this.rowData.rybh)
      } else {
        this.showFile = true
      }
    } else {
      if (this.rowData.status == "02") {
        this.getPrisonerInfo(this.rowData.rybh)
        this.showFile = false
        this.getBiometricInfo(this.rowData.rybh)
      } else {
        this.showFile = true
      }
    }



  }
}
</script>

<style scoped>
@import "~@/assets/style/formInfo.css";

.fm-content-info {
  padding: 26px 0 26px 0 !important;
}

.action-gather {
  display: flex;
}

.img-box-lsit {
  display: flex;
  gap: 10px;
  overflow: auto;
  padding: 3px 0;


}

.img-box-lsit::-webkit-scrollbar {
  height: 4px !important;
  /*  设置横轴（x轴）轴滚动条 */
}


/* .img-box-lsit img {
  width: 50px;
  height: 50px;

} */
</style>
