<template>
    <div>
        <div>
            <div class="bsp-list-container">
                <rs-DataGrid ref="grid" funcMark="znwdrylb" :customFunc="false" :beforeRender="beforeRender">
                    <template v-slot:zdwd:tzcjzqsz="{ oper }">
                        <Button type="primary" @click.native="handleSetting"
                            :disabled="$refs.grid.batch_select.length < 1">
                            <Icon v-if="oper.iconPath" :type="oper.iconPath"></Icon>
                            {{ oper.name }}
                        </Button>
                    </template>
                    <!--详情操作-->
                    <template v-slot:zdwd:bd="{ oper, row, index }">
                        <Button type="primary" v-if="!row.shid" @click.native="handleAdd(row)">
                            <Icon v-if="oper.iconPath" :type="oper.iconPath"></Icon>
                            {{ oper.name }}
                        </Button>
                    </template>
                    <template v-slot:zdwd:jb="{ oper, row, index }">
                        <Button v-if="row.shid" @click.native="handleTerm(row)">
                            <Icon v-if="oper.iconPath" :type="oper.iconPath"></Icon>
                            {{ oper.name }}
                        </Button>
                    </template>
                </rs-DataGrid>
            </div>
        </div>

        <Modal v-model="bindModal" width="30%" title="绑定智能腕带">
            <Form ref="formData" :model="formData" :label-width="130" :label-colon=true>
                <FormItem label="被监管人员姓名" prop="bindPersonName">
                    <div class="ivu-form-item-label">{{ formData.bindPersonName }}</div>
                </FormItem>
                <FormItem label="手环ID" prop="tagId"
                    :rules="[{ trigger: 'blur,change', message: '手环ID为必填', required: true }]">
                    <Input type="text" v-model="formData.tagId" placeholder="请填写" style="width: 200px;" />
                </FormItem>

            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSubmit" class="save">确 定</Button>
                <Button @click="handleClose" class="save">关 闭</Button>
            </div>
        </Modal>
        <Modal v-model="unbindModal" width="30%" title="解绑智能腕带">
            <Form ref="formDataUn" :model="formDataUn" :label-width="130" :label-colon=true>
                <FormItem label="被监管人员姓名" prop="bindPersonName">
                    <div class="ivu-form-item-label">{{ formData.bindPersonName }}</div>
                </FormItem>
                <FormItem label="手环ID" prop="tagId">
                    <div class="ivu-form-item-label">{{ formDataUn.tagId }}</div>
                </FormItem>
                <FormItem label="解绑原因" prop="unbindReason"
                    :rules="[{ trigger: 'blur,change', message: '解绑原因必填', required: true }]">
                    <Input type="textarea" v-model="formDataUn.unbindReason" placeholder="请填写"
                        :autosize="{ minRows: 2, maxRows: 5 }" />
                </FormItem>

            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSubmitUn" class="save">确 定</Button>
                <Button @click="handleClose" class="save">关 闭</Button>
            </div>
        </Modal>
        <Modal v-model="setModal" width="30%" title="体征采样周期设置">
            <Form ref="formDataSet" :model="formDataSet" :label-width="130" :label-colon=true>
                <FormItem label="已绑定人员姓名">
                    <Tag v-for="(item, index) in tagInfoList" :key="item.id" color="blue">{{ item.xm }}</Tag>
                </FormItem>
                <FormItem label="血氧" prop="bloodOxygenValue">
                    <Input v-model="formDataSet.bloodOxygenValue" placeholder="采样周期 单位s" type="number" />
                </FormItem>
                <FormItem label="心率" prop="heartRateValue">
                    <Input v-model="formDataSet.heartRateValue" placeholder="采样周期 单位s" type="number" />
                </FormItem>
                <FormItem label="体温" prop="temperatureValue">
                    <Input v-model="formDataSet.temperatureValue" placeholder="采样周期 单位s" type="number" />
                </FormItem>


            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSubmitSet" class="save">确 定</Button>
                <Button @click="setModal = false" class="save">关 闭</Button>
            </div>
        </Modal>

    </div>
</template>

<script>
import { mapActions } from 'vuex'
export default {
    name: 'personIndex',
    data() {
        return {
            showDetail: true,
            bindModal: false,
            unbindModal: false,
            formData: {
                bindPersonName: '',
                tagId: '',
                bindTime: ''

            },
            formDataUn: {
                bindPersonId: '',
                tagId: '',
                unbindReason: '',
                unbindTime: ''
            },
            batteryList: [],
            formDataSet: {},
            setModal: false,
            tagInfoList: []

        }
    },
    components: {
    },
    computed: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    },
    methods: {
        beforeRender(data) {     //请求案件的数据
            return new Promise((resolve, reject) => {
                //模拟异步方法，可处理data数据、请求后台数据
                if (data.success && data.rows != undefined && data.rows.length > 0) {
                    setTimeout(() => {
                        const tagIds = data.rows.map(item => item.shid).filter(shid => shid !== undefined).join(',');
                        this.$store.dispatch('authGetRequest', { url: this.$path.pm_getBatteryByPersonId, params: { tagIds } }).then(res => {
                            if (res.success) {
                                this.batteryList = res.data
                                data.rows = data.rows.map(item1 => ({

                                    ...item1,
                                    ...this.batteryList.find(item2 => item2.tagId == item1.shid)
                                }))
                                resolve(data)
                            } else {
                                this.$Message.error(res.message)
                            }
                        })
                    }, 500)
                } else {
                    resolve(data);
                }
            });
        },
        handleAdd(row) {
            this.formData.bindPersonId = row.jgrybm
            this.formData.bindPersonName = row.xm
            this.bindModal = true
        },
        handleClose() {
            this.bindModal = false
            this.unbindModal = false
            this.$refs.formData.resetFields()
            this.$refs.formDataUn.resetFields()
        },
        handleTerm(row) {
            this.formData.bindPersonName = row.xm
            this.formDataUn.bindPersonId = row.jgrybm
            this.formDataUn.tagId = row.shid
            this.unbindModal = true
        },
        handleSubmitUn() {
            this.formDataUn.unbindTime = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
            this.$refs.formDataUn.validate(valid => {
                if (valid) {
                    let params = { ...this.formDataUn }
                    this.$store.dispatch('authPostRequest', { url: this.$path.pm_znwdUnbind, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('解绑定成功')
                            this.handleClose()
                            this.on_refresh_table()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        },
        handleSubmit() {
            this.formData.bindTime = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let params = { ...this.formData }
                    this.$store.dispatch('authPostRequest', { url: this.$path.pm_znwdCreate, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('绑定成功')
                            this.handleClose()
                            this.$refs.tableRef.clearSelected();
                            this.on_refresh_table()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        },
        getBattery(tagIds) {
            this.$store.dispatch('authGetRequest', { url: this.$path.pm_getBatteryByPersonId, params: { tagIds } }).then(res => {
                if (res.success) {
                    this.batteryList = res.data
                } else {
                    this.$Message.error(res.message)
                }
            })
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data()
        },
        // 体征采样周期设置
        handleSetting() {
            this.tagInfoList = this.$refs.grid.batch_select.filter(item => item.shid)
            if (this.tagInfoList.length > 0) {
                this.setModal = true
            } else {
                this.$Message.warning('当前选择全未绑定智能手环!')
            }
        },
        handleSubmitSet() {
            let batch_select = this.tagInfoList.map(item => ({
                bindRecordId: item.bindrecordid || '',
                tagId: item.shid || '',
                bloodOxygenTaskId: item.bloodOxygenTaskId || null,
                temperatureTaskId: item.temperatureTaskId || null,
                heartRateTaskId: item.heartRateTaskId || null
            }))
            this.$refs.formDataSet.validate(valid => {
                if (valid) {
                    let params = { ...this.formDataSet }
                    params.tagInfoList = batch_select
                    this.$store.dispatch('authPostRequest', { url: this.$path.pm_setHandBandVitalSignPushInterval, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('操作成功')
                            this.setModal = false
                            this.on_refresh_table()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        }
    }
}
</script>