<template>
  <div class="content-defaulet">
    <div class="content-defaulet-main">
      <tabs ref="tabs" v-show="!showFormCompnent" mark="crbgl" :params="{}">

        <template slot="customHeadFunc" slot-scope="{ func,resetMethod,appendEqualFuncMark}">
          <Button type="primary" @click="changeAction('addAisdForm',null,resetMethod)"
                  v-if="appendEqualFuncMark('ylzxt-crbgl-azblb')">
            新增
          </Button>
          <Button type="primary" @click="changeAction('addOtherForm',null,resetMethod)"
                  v-if="appendEqualFuncMark('ylzxt-crbgl-qtcrbgl')">
            新增
          </Button>
        </template>

        <template slot="customRowFunc"
                  slot-scope="{ func,hasPermission, row, index, resetMethod, funcMark,appendEqualFuncMark }">
          <Button v-if="appendEqualFuncMark('ylzxt-crbgl-azblb')" type="primary"
                  @click="changeAction('addAisdRecord',row,resetMethod)">
            登记
          </Button>
          <Button v-if="appendEqualFuncMark('ylzxt-crbgl-qtcrbgl')" type="primary"
                  @click="changeAction('addOtherRecord',row,resetMethod)">
            登记
          </Button>
          <!-- 定期检测 -->
           
        </template>
        <template slot="slot_dqjc" slot-scope="{ row }">
                    <span style="color: #2c2cf9; text-decoration: none; border-bottom: 1px solid; cursor: pointer"
                        @click="handleRouterAll(row, true)" v-if="row.dqjc > 0">{{ row.dqjc }}</span>
                    <span v-else>{{ row.dqjc }}</span>
                </template>
      </tabs>
      <div v-if="showFormCompnent">
        <component v-bind:is='component' :pk="pk" @toback="onBack">

        </component>
      </div>

      <!-- 定期检测弹框 -->
       <Modal v-model="showFormCompnentTAble" :title="modalTitle" width="900">
            <div class="modal-content">
                <rs-DataGrid ref="grid" v-if='showFormCompnentTAble' funcMark="bjsdskss:azb-zdqjcjl" :customFunc="true"
                    :params='params'>
                    <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index }">
                        <Button type="primary" v-if="hasPermission('azbdqjcjl:xq')"
                            style="margin-right: 5px;" @click="prescribeDetails('regularTestingAidsDetail', row,resetMethod)">详情</Button>
                    </template>
                </rs-DataGrid>
            </div>
        </Modal>
      <!-- 定期检测列表里面的详情 -->
        <!-- <Modal v-model="ShowRegularTestingDetail" :title="modalTitle" width="900">
            <div class="modal-content">
              详情
                <rs-DataGrid ref="grid" v-if='showFormCompnentTAble' funcMark="bjsdskss:azb-zdqjcjl" :customFunc="true"
                    :params='params'>
                    <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index }">
                        <Button type="primary" v-if="hasPermission('azbdqjcjl:xq')"
                            style="margin-right: 5px;" @click="prescribeDetails('xq', row)">详情</Button>
                    </template>
                </rs-DataGrid>
                <regularTestingAidsDetail :pk="pk" @toback="onBackAdis"></regularTestingAidsDetail>
            </div>
        </Modal> -->
    </div>
  </div>
</template>

<script>
import tabs from "@/components/tabs/index.vue";
import {mapActions} from "vuex";
import addAisdRecord from '../aids-management/AddRecord.vue'
import addOtherRecord from '../other-infectious-disease/AddRecord.vue'
import regularTestingOtherDetail from '../other-infectious-disease/regularTestingOtherDetail.vue'
import regularTestingAidsDetail from '../aids-management/regularTestingAidsDetail.vue'


import addAisdForm from '../aids-management/AddForm.vue'
import addOtherForm from '../other-infectious-disease/AddForm.vue'


export default {
  components: {
    tabs,
    addAisdForm,
    addAisdRecord,

    addOtherForm,
    addOtherRecord,

    regularTestingOtherDetail,
    regularTestingAidsDetail


  },
  data() {
    return {
      modalTitle: '',

      pk: '',
      showFormCompnent: false,
      showFormCompnentTAble:false,
      ShowRegularTestingDetail:false,
      component: null,
      resetMethod: null,
       params: {
                infection_id: '',
            },

    };
  },
  methods: {
    ...mapActions(["authGetRequest"]),
      // 详情
      prescribeDetails(type, row, resetMethod){
              // console.log(resetMethod,'resetMethod111')

        // this.resetMethod = resetMethod
        // this.showFormCompnent = true
        // this.component = type;
        // if (row) {
        //   this.pk = row.id;
        // }
        // this.showFormCompnentTAble = false
      },
        // 定期检测
      handleRouterAll(row) {
        // console.log(row,'4444')
            this.modalTitle = row.room_name + ' '  + row.xm + ' ' + '定期登记次数'
            this.params.infection_id = row.id
            this.showFormCompnentTAble = true
        },
    onBack() {
      this.showFormCompnent = false
      this.component = null
      this.resetMethod()
    },
    onBackAdis(){
      this.ShowRegularTestingDetail = false
    },
    changeAction(type, row, resetMethod) {
      // console.log(resetMethod,'resetMethod')
      this.resetMethod = resetMethod
      this.showFormCompnent = true
      this.component = type;
      if (row) {
        this.pk = row.id;
      }
    },
  },
};
</script>


<style lang="less" scoped>
.modal-content {
  font-size: 16px;
  text-align: center;

  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px
  }

  .modal-bottom {
    margin-top: 10px;

    .modal-bottom-dr {
      color: rgb(63, 118, 219);
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.modal-contents {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-contents-title {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
