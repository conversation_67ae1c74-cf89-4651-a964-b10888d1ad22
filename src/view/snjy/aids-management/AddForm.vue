<template>
  <div class="aids-management-registration">
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :responsive="true"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
      @collapse-change="handleCollapseChange"
    >
      <!-- 左侧人员信息面板 -->
      <template #left>
        <personnel-selector
          v-model="personInfo.jgrybm"
          title="选择在押人员"
          :enableScan=false
          :personnelType=ryzt
          :show-case-info="false"
          placeholder="请选择需要操作的在押人员"
          @change="handleSelectPersonnel"
        />
      </template>

      <!-- 业务登记表单 -->
      <template #business-registration>
        <DynamicForm
          ref="businessForm"
          v-model="businessFormData"
          :config="businessFormConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleBusinessFieldChange"
          @validate="handleBusinessValidate"
        />
      </template>


    </DetailCardLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'

export default {
  name: 'AidsManagementRegistration',
  data() {
    return {
      ryzt: 'ZS',
      loading: false,
      formMode: FORM_MODES.CREATE,
      // 人员信息
      personInfo: {
        jgrybm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'business-registration',
          title: '登记',
          icon: 'ios-medical',
          iconColor: '#5b8ff9',
          slot: 'business-registration',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ],

      // 业务登记表单数据 - 基于传染病管理登记接口
      businessFormData: {
        // 基本信息
        jgrybm: '', // 监管人员编码
        jgryxm: '', // 监管人员姓名
        infectiousDiseaseType: '1', // 传染病类型

        // 艾滋病相关信息
        aidsFirstScreeningTime: '', // 艾滋病-初筛时间
        aidsFirstScreeningMethod: '', // 艾滋病-初筛操作方式
        aidsFirstScreeningResult: '', // 艾滋病-初筛结果
        aidsLabFeedbackTime: '', // 艾滋病-实验室反馈时间
        aidsLabFeedbackResult: '', // 艾滋病-实验室反馈结果
        aidsDiagnosisDate: '', // 艾滋病-确诊日期
        aidsInformInmateTime: '', // 艾滋病-告知在押人员时间
        aidsIsInformFamily: '', // 艾滋病-是否告知家属
        aidsInformFamilyTime: '', // 艾滋病-告知家属时间

        // 疾控机构相关
        jkjgrqmc: '', // 疾控机构名称
        gzjkjgrq: '', // 告知疾控机构日期
        sfjszrjkjgfkxx: '', // 是否接收转入疾控机构名称反馈信息
        jszrjkjgfkxxrq: '', // 接收转入疾控机构反馈信息日期
        sfxyzclxzrjkjg: '', // 是否需要再次联系转入疾控机构
        zclxzrjkjgrq: '', // 再次联系转入疾控机构日期

        // 其他信息
        submitter: '', // 送交人
        attUrl: '', // 附件地址

        // 经办信息（系统自动填充）
        operatePolice: '', // 登记民警
        operatePoliceSfzh: '', // 登记民警身份证号
        operateTime: '' // 登记时间
      }
    }
  },

  computed: {
    // 业务登记表单配置 - 基于传染病管理登记接口
    businessFormConfig() {
      return [
        {
          title: '业务登记',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showTitle: true,
          fields: [
            {
              key: 'infectiousDiseaseType',
              label: '传染病类型',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              disabled: true,
              span: 12,
              dicName: 'ZD_CYB_CRBLX',
              props: {
                placeholder: '传染病类型',
                multiple: false,
                defaultValue: '1'
              }
            },
            {
              key: 'aidsFirstScreeningTime',
              label: '初筛时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsFirstScreeningMethod',
              label: '初筛操作方式',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSCZFS', // 字典：初筛操作方式
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsFirstScreeningResult',
              label: '初筛结果',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX', // 字典：初筛结果
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsDiagnosisDate',
              label: '送实验室日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'submitter',
              label: '送交人',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入'
              }
            },
            {
              key: 'aidsLabFeedbackTime',
              label: '实验室反馈结果时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsLabFeedbackResult',
              label: '实验室检测结果',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX', // 字典：实验室反馈结果
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsInformInmateTime',
              label: '告知在押人员时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsIsInformFamily',
              label: '是否告知家属',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_TYSFDM',
              props: {
                placeholder: '请选择',
                multiple: false
              }
            }, {
              key: 'aidsInformFamilyTime',
              label: '告知家属时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              dependsOn: {
                key: 'aidsIsInformFamily',    // 依赖字段
                value: '1',               // 当值为'1'(是)时
                action: 'show'            // 显示该字段
              },
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'attUrl',
              label: '上传附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24, // 文件上传组件建议占满一行
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true
              },
              events: {
                'fileComplete': this.fileCompleteFileCertUrl,
              }
            }
          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),

    // 初始化数据
    initializeData() {

      // 处理路由参数
      const {mode, ryId, recordId} = this.$route.query

      if (mode) {
        this.formMode = mode === 'view' ? FORM_MODES.VIEW :
          mode === 'edit' ? FORM_MODES.EDIT :
            FORM_MODES.CREATE
      }

      if (ryId) {
        this.personInfo.jgrybm = ryId
        this.businessFormData.jgrybm = ryId
      }

      if (recordId && mode !== 'add') {
        this.loadRecordData(recordId)
      }
    },


    // 获取当前日期时间
    getCurrentDateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 获取当前用户
    getCurrentUser() {
      // 这里应该从用户状态或缓存中获取
      return "";
    },

    // 获取当前用户身份证号
    getCurrentUserIdCard() {
      // 这里应该从用户状态或缓存中获取
      return "";
    },

    // 加载人员信息
    loadPersonInfo(personId) {
      this.loading = true
      // 这里应该调用实际的API
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: this.$path.infection_get,
        params: {id: recordId}
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.businessFormData = {...this.businessFormData, ...res.data}
          this.personInfo.jgrybm = res.data.jgrybm || ''

          // 如果有人员姓名，也更新到人员信息中
          if (res.data.jgryxm) {
            this.businessFormData.jgryxm = res.data.jgryxm
          }
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },

    // 处理人员选择
    handleSelectPersonnel(personnelData, jgrybm) {
      console.log('选择人员:', personnelData, jgrybm)
      this.personInfo.jgrybm = jgrybm

      // 同时更新表单数据中的人员信息
      this.businessFormData.jgrybm = jgrybm
      if (personnelData && personnelData.xm) {
        this.businessFormData.jgryxm = personnelData.xm
      }
    },

    // 处理收缩变化
    handleCollapseChange(event) {
      console.log('面板收缩状态变化:', event)
    },

    // 处理卡片操作
    handleCardAction(event) {
      console.log('卡片操作:', event)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSubmit()
          break
      }
    },

    // 返回
    handleBack() {
      this.$emit('toback')
    },

    // 提交
    handleSubmit() {
      this.validateAndSave()
    },

    // 验证并保存
    validateAndSave() {
      console.log('开始表单验证...')

      // 验证必填字段
      if (!this.businessFormData.jgrybm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      if (!this.businessFormData.jgryxm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      // 验证表单
      this.$refs.businessForm?.validate().then(valid => {
        console.log('表单验证结果:', valid)

        if (valid) {
          console.log('验证通过，开始保存数据')
          this.saveData()
        } else {
          console.log('验证失败，阻止提交')
          this.$Message.error('请检查表单填写是否完整')
        }
      }).catch(error => {
        console.error('表单验证异常：', error)
        this.$Message.error('表单验证失败')
      })
    },

    // 保存数据
    saveData() {
      this.loading = true

      // 准备提交数据，过滤掉空值
      const submitData = this.prepareSubmitData()

      // 调用传染病管理登记创建接口
      this.authPostRequest({
        url: this.$path.infection_create,
        params: submitData
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          this.$Message.success('传染病管理登记成功')
          this.handleBack()
        } else {
          this.$Message.error(res.msg || res.message || '传染病管理登记失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('提交失败：', error)
      })
    },

    // 准备提交数据
    prepareSubmitData() {
      const data = {...this.businessFormData}
      // 过滤掉空值和undefined
      Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
          delete data[key]
        }
      })

      // 确保必填字段存在
      if (!data.jgrybm) {
        data.jgrybm = this.personInfo.jgrybm
      }

      // 设置操作时间为当前时间
      data.operateTime = this.getCurrentDateTime()

      return data
    },

    // 业务表单字段变化
    handleBusinessFieldChange(key, value, formData) {
      console.log('业务表单字段变化:', key, value, typeof value)

      // 特别关注日期时间字段的变化
      if (key.includes('Time') || key.includes('Date')) {
        console.log('日期时间字段变化详情:', {
          key,
          value,
          valueType: typeof value,
          isDate: value instanceof Date,
          formData: formData[key]
        })
      }

      // 确保数据同步到父组件的 businessFormData
      this.businessFormData = {...this.businessFormData, ...formData}
    },

    // 业务表单验证
    handleBusinessValidate(prop, valid, message) {
      console.log('业务表单验证:', prop, valid, message)
    },
    // 文件上传前回调
    beforeUpload(file) {
      console.log('准备上传文件:', file.name)

      // 文件大小检查
      if (file.size > 10 * 1024 * 1024) {
        this.$Message.error('文件大小不能超过10MB')
        return false
      }

      // 文件类型检查
      const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png']
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!allowedTypes.includes(fileExt)) {
        this.$Message.error('不支持的文件类型')
        return false
      }

      return true
    },

    // 文件上传成功回调
    fileSuccessFile(file) {
      console.log('文件上传成功:', file)
      this.$Message.success(`文件 ${file.name} 上传成功`)

      // 更新表单数据中的附件信息
      if (!this.businessFormData.attUrl) {
        this.businessFormData.attUrl = []
      }
      this.businessFormData.attUrl.push(file)
    },

    // 所有文件上传完成回调
    fileCompleteFileCertUrl(files) {
      debugger
      console.log('所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')

      // 更新表单数据
      this.businessFormData.attUrl = JSON.stringify(files);
    },

  }
}
</script>

<style lang="less" scoped>
.aids-management-registration {
  height: 100%;

  // 修复文件上传组件样式
  /deep/ .upload-box {
    width: 100%;

    .file-item {
      width: 100%;
      max-width: 500px;
      height: auto;
      min-height: 55px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(206, 224, 240, 1);
      border-radius: 4px;
      display: flex;
      padding: 10px;
      margin-bottom: 10px;
      box-sizing: border-box;
    }

    .file-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      min-width: 0; // 防止文本溢出
    }

    .file-text {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;

      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
      }

      .file-size {
        flex-shrink: 0;
        color: #999;
        font-size: 12px;
      }
    }

    .file-img {
      width: 32px;
      height: 32px;
      margin-right: 10px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .space-btw {
      display: flex;
      align-items: center;
      line-height: normal;

      span {
        margin: 0 3px;
      }
    }

    // 上传按钮样式
    .ivu-btn {
      margin-bottom: 15px;

      .ivu-icon {
        margin-right: 5px;
        position: relative;
        top: 2px;
      }
    }

    // 进度条样式
    .ivu-progress {
      margin-top: 5px;
    }
  }

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }

  // 文件上传字段特殊处理
  /deep/ .ivu-form-item {
    &:has(.upload-box) {
      .ivu-form-item-content {
        min-height: auto;
      }
    }
  }
}
</style>
