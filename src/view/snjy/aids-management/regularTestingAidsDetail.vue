<template>
  <div class="aids-management-registration">
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :responsive="true"
      @bottom-action="handleBottomAction"
    >
      <!-- 左侧人员信息面板 -->
      <template #left>
        <personnel-selector
          v-model="personInfo.jgrybm"
          title="选择在押人员"
          :enableScan=false
          :showCaseInfo=showCaseInfo
          :personnelType=ryzt
          mode="detail"
          placeholder="请选择需要操作的在押人员"
        />
        <Record :infectionId="pk"></Record>
      </template>

      <!-- 业务登记表单 -->
      <template #business-registration>
        <div class="fm-content-info">
        <Form ref="formData" :model="formData"  inline style="margin-top: 20px;margin-bottom: 20px;">
        <div class="fm-content-box">
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基本信息</p>
            <Row>
                <!--                 <Col span="5"><span>{{formData.medicine.medicineName}}</span></Col>
 -->
                <Col span="5" class="col-title"><span>传染病类型</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5"><span>初筛时间</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
            <Row>
                <Col span="5" class="col-title"><span>初筛操作方式</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>初筛结果</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
             <Row>
                <Col span="5" class="col-title"><span>送实验室日期</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>送交人</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
             <Row>
                <Col span="5" class="col-title"><span>实验室反馈结果时间</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>实验室检查结果</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
             <Row>
                <Col span="5" class="col-title"><span>告知在押人员时间</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>是否告知家属</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
             <Row>
                <Col span="5" class="col-title"><span>告知家属时间</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>附件</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
           
        </div>
       
      
        </Form>
        <!-- <div class="bsp-base-fotter">
          <Button @click="returnBack">返 回</Button>
      </div> -->
    </div>
        <!-- 9999 -->
        <!-- <DynamicForm
          ref="businessForm"
          v-model="businessFormData"
          :config="businessFormConfig"
          :mode="formMode"
          :loading="loading"
        /> -->
      </template>

      <!-- 检查情况表单 -->
      <template #inspection-record>
        <Form ref="formData" :model="formData"  inline style="margin-top: 20px;margin-bottom: 20px;">
        <div class="fm-content-box">
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />检查情况</p>
            <Row>
                <Col span="5" class="col-title"><span>检查日期</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5"><span>CD4T淋巴值</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
            <Row>
                <Col span="5" class="col-title"><span>病毒载量值</span></Col>
                <Col span="7"><span>2222</span></Col>
                <Col span="5" class="col-title"><span>附件</span></Col>
                <Col span="7"><span>2222</span></Col>
            </Row>
            <Row>
                <Col span="5" class="col-title"><span>管理情况</span></Col>
                <Col span="19"><span>2222</span></Col>
               
            </Row>
             <!-- <Row>
                <Col span="5" class="col-title"><span>附件</span></Col>
                <Col span="19"><span>2222</span></Col>
               
            </Row> -->
            
           
        </div>
       
      
        </Form>
        <!-- 222 -->
        <!-- <DynamicForm
          ref="inspectionForm"
          v-model="inspectionFormData"
          :config="inspectionFormConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleInspectionFieldChange"
          @validate="handleInspectionValidate"
        /> -->
      </template>

    </DetailCardLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'
import Record from "./Record.vue";

export default {
  components: {
    Record
  },
  name: 'AidsManagementRegistration',
  props: {
    pk: {
      type: String,
      default: () => ({})
    }
  },
  data() {
    return {
      formData:{},
      ryzt: 'ZS',
      detail: "true",
      showCaseInfo: false,
      loading: false,
      formMode: FORM_MODES.CREATE,
      // 人员信息
      personInfo: {
        jgrybm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'business-registration',
          title: '艾滋病管理登记',
          icon: 'ios-medical',
          iconColor: '#5b8ff9',
          slot: 'business-registration',
          showHeader: false
        },
        {
          name: 'inspection-record',
          title: '检查情况',
          icon: 'ios-analytics',
          iconColor: '#52c41a',
          slot: 'inspection-record',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        // {
        //   name: 'submit',
        //   label: '提交',
        //   type: 'primary',
        //   icon: 'ios-checkmark'
        // }
      ],

      // 业务登记表单数据 - 基于传染病管理登记接口
      businessFormData: {
        // 基本信息
        jgrybm: '', // 监管人员编码
        jgryxm: '', // 监管人员姓名
        infectiousDiseaseType: '1', // 传染病类型

        // 艾滋病相关信息
        aidsFirstScreeningTime: '', // 艾滋病-初筛时间
        aidsFirstScreeningMethod: '', // 艾滋病-初筛操作方式
        aidsFirstScreeningResult: '', // 艾滋病-初筛结果
        aidsLabFeedbackTime: '', // 艾滋病-实验室反馈时间
        aidsLabFeedbackResult: '', // 艾滋病-实验室反馈结果
        aidsDiagnosisDate: '', // 艾滋病-确诊日期
        aidsInformInmateTime: '', // 艾滋病-告知在押人员时间
        aidsIsInformFamily: '', // 艾滋病-是否告知家属
        aidsInformFamilyTime: '', // 艾滋病-告知家属时间

        // 疾控机构相关
        jkjgrqmc: '', // 疾控机构名称
        gzjkjgrq: '', // 告知疾控机构日期
        sfjszrjkjgfkxx: '', // 是否接收转入疾控机构名称反馈信息
        jszrjkjgfkxxrq: '', // 接收转入疾控机构反馈信息日期
        sfxyzclxzrjkjg: '', // 是否需要再次联系转入疾控机构
        zclxzrjkjgrq: '', // 再次联系转入疾控机构日期

        // 其他信息
        submitter: '', // 送交人
        attUrl: '', // 附件地址

        // 经办信息（系统自动填充）
        operatePolice: '', // 登记民警
        operatePoliceSfzh: '', // 登记民警身份证号
        operateTime: '' // 登记时间
      },

      // 检查情况表单数据 - 基于定期检查登记接口
      inspectionFormData: {
        id: '', // 主键
        infectionId: '', // 传染病登记ID
        jgrybm: '', // 监管人员编码
        checkDate: '', // 检查日期
        cd4tlbz: '', // CD4T淋巴值
        viralLoadValue: '', // 病毒载量值
        managementSituation: '', // 管理情况
        controlPersonnel: '', // 管控人
        attUrl: '' // 附件地址
      }
    }
  },

  computed: {
    // 业务登记表单配置 - 基于传染病管理登记接口
    businessFormConfig() {
      return [
        {
          title: '基本信息',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: true,
          collapsed: false,
          showHeader: false,
          readonly: true,
          fields: [
            {
              key: 'infectiousDiseaseType',
              label: '传染病类型',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              disabled: true,
              span: 12,
              dicName: 'ZD_CYB_CRBLX',
              props: {
                placeholder: '传染病类型',
                multiple: false,
                defaultValue: '1'
              }
            },
            {
              key: 'aidsFirstScreeningTime',
              label: '初筛时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsFirstScreeningMethod',
              label: '初筛操作方式',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSCZFS', // 字典：初筛操作方式
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsFirstScreeningResult',
              label: '初筛结果',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX', // 字典：初筛结果
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsDiagnosisDate',
              label: '送实验室日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'submitter',
              label: '送交人',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入'
              }
            },
            {
              key: 'aidsLabFeedbackTime',
              label: '实验室反馈结果时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsLabFeedbackResult',
              label: '实验室检测结果',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX', // 字典：实验室反馈结果
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'aidsInformInmateTime',
              label: '告知在押人员时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'aidsIsInformFamily',
              label: '是否告知家属',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_TYSFDM',
              props: {
                placeholder: '请选择',
                multiple: false
              }
            }, {
              key: 'aidsInformFamilyTime',
              label: '告知家属时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'attUrlDj',
              label: '上传附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24, // 文件上传组件建议占满一行
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true
              },
              events: {
                'beforeUpload': this.beforeUpload,
                'fileSuccess': this.fileSuccessFile,
                'fileRemove': this.fileRemoveFile,
                'fileComplete': this.fileCompleteFileCertUrl,
                'onExceededSize': this.handleExceededSize,
                'onExceededFile': this.handleExceededFile,
                'fileError': this.handleFileError
              }
            }
          ]
        }
      ]
    },

    // 检查情况表单配置 - 基于定期检查登记接口
    inspectionFormConfig() {
      return [
        {
          title: '检查情况',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showHeader: false,
          fields: [
            {
              key: 'checkDate',
              label: '检查日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择检查日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'cd4tlbz',
              label: 'CD4T淋巴值',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入CD4T淋巴值',
                type: 'number'
              }
            },
            {
              key: 'viralLoadValue',
              label: '病毒载量值',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入病毒载量值',
                type: 'number'
              }
            },
            {
              key: 'managementSituation',
              label: '管理情况',
              type: FIELD_TYPES.TEXTAREA,
              required: false,
              span: 24,
              props: {
                placeholder: '请输入管理情况',
                rows: 4
              }
            },
            {
              key: 'attUrl',
              label: '上传附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              required: false,
              span: 24,
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true
              },
              events: {
                'fileSuccess': this.fileSuccessInspectionFile,
                'fileRemove': this.fileRemoveInspectionFile,
                'fileComplete': this.fileCompleteInspectionFile,
                'onExceededSize': this.handleExceededSize,
                'onExceededFile': this.handleExceededFile,
                'fileError': this.handleFileError
              }
            }
          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),

    // 初始化数据
    initializeData() {
      if (this.pk) {
        this.loadRecordData(this.pk)
      }
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: this.$path.infection_get,
        params: {id: recordId}
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.businessFormData = {...this.businessFormData, ...res.data}
          this.personInfo.jgrybm = res.data.jgrybm || ''

          // 如果有人员姓名，也更新到人员信息中
          if (res.data.jgryxm) {
            this.businessFormData.jgryxm = res.data.jgryxm
          }
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },


    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event
      switch (action.name) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSaveInspection()
          break
      }
    },

    // 返回
    handleBack() {
      this.$emit('toback')
    },


    // 保存检查情况
    handleSaveInspection() {
      this.validateAndSaveInspection()
    },

    // 验证并保存检查情况
    validateAndSaveInspection() {
      console.log('开始表单验证...')

      // 验证必填字段
      if (!this.personInfo.jgrybm) {
        this.$Message.error('请先选择监管人员')
        return
      }

      if (!this.inspectionFormData.checkDate) {
        this.$Message.error('请选择检查日期')
        return
      }

      // 验证表单
      this.$refs.inspectionForm?.validate().then(valid => {
        console.log('表单验证结果:', valid)

        if (valid) {
          console.log('验证通过，开始保存数据')
          this.saveInspectionData()
        } else {
          console.log('验证失败，阻止提交')
          this.$Message.error('请检查表单填写是否完整')
        }
      }).catch(error => {
        console.error('表单验证异常：', error)
        this.$Message.error('表单验证失败')
      })
    },


    // 文件上传前回调
    beforeUpload(file) {
      console.log('准备上传文件:', file.name)
    },

    // 文件上传成功回调
    fileSuccessFile(file) {
      console.log('文件上传成功:', file)
    },

    // 文件删除回调
    fileRemoveFile(file) {
      console.log('文件删除:', file)
    },

    // 所有文件上传完成回调
    fileCompleteFileCertUrl(files) {
      console.log('所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')

    },

    // 文件大小超出限制回调
    handleExceededSize() {
      this.$Message.error('文件大小超出限制！')
    },

    // 文件数量超出限制回调
    handleExceededFile() {
      this.$Message.error('文件数量超出限制！')
    },

    // 文件上传失败回调
    handleFileError(error) {
      console.log('文件上传失败:', error)
      this.$Message.error('文件上传失败！')
    },

    // ==================== 检查情况表单相关方法 ====================

    // 检查情况表单字段变化
    // handleInspectionFieldChange(key, value, formData) {
    //   console.log('检查情况表单字段变化:', key, value, typeof value)
    //   // 确保数据同步到父组件的 inspectionFormData
    //   this.inspectionFormData = {...this.inspectionFormData, ...formData}
    // },

    // 检查情况表单验证
    // handleInspectionValidate(valid, errors) {
    //   console.log('检查情况表单验证结果:', valid, errors)
    // },


    // 检查情况文件上传成功回调
    fileSuccessInspectionFile(file) {

    },

    // 检查情况文件删除回调
    fileRemoveInspectionFile(file) {

    },

    // 检查情况所有文件上传完成回调
    fileCompleteInspectionFile(files) {
      console.log('检查情况所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')
      this.inspectionFormData.attUrl = JSON.stringify(files);
    },

    // 保存检查情况数据
    saveInspectionData() {
      this.loading = true

      // 准备提交数据
      const submitData = this.prepareInspectionSubmitData()

      // 调用定期检查登记创建接口
      this.authPostRequest({
        url: this.$path.regular_inspection_create,
        params: submitData
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          this.$Message.success('检查情况保存成功')
          // 可以选择是否返回或继续编辑
          this.handleBack();
        } else {
          this.$Message.error(res.msg || res.message || '检查情况保存失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('检查情况保存失败：', error)
      })
    },

    // 准备检查情况提交数据
    prepareInspectionSubmitData() {
      const data = {...this.inspectionFormData}

      // 设置关联的传染病登记ID和人员编码
      data.infectionId = this.businessFormData.id || '' // 传染病登记ID
      data.jgrybm = this.personInfo.jgrybm || this.businessFormData.jgrybm

      // 过滤掉空值和undefined
      Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
          delete data[key]
        }
      })

      return data
    }
  }
}
</script>

<style lang="less" scoped>
.aids-management-registration {
  height: 100%;

  // 修复文件上传组件样式
  /deep/ .upload-box {
    width: 100%;

    .file-item {
      width: 100%;
      max-width: 500px;
      height: auto;
      min-height: 55px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(206, 224, 240, 1);
      border-radius: 4px;
      display: flex;
      padding: 10px;
      margin-bottom: 10px;
      box-sizing: border-box;
    }

    .file-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      min-width: 0; // 防止文本溢出
    }

    .file-text {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;

      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 10px;
      }

      .file-size {
        flex-shrink: 0;
        color: #999;
        font-size: 12px;
      }
    }

    .file-img {
      width: 32px;
      height: 32px;
      margin-right: 10px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .space-btw {
      display: flex;
      align-items: center;
      line-height: normal;

      span {
        margin: 0 3px;
      }
    }

    // 上传按钮样式
    .ivu-btn {
      margin-bottom: 15px;

      .ivu-icon {
        margin-right: 5px;
        position: relative;
        top: 2px;
      }
    }

    // 进度条样式
    .ivu-progress {
      margin-top: 5px;
    }
  }

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }

  // 文件上传字段特殊处理
  /deep/ .ivu-form-item {
    &:has(.upload-box) {
      .ivu-form-item-content {
        min-height: auto;
      }
    }
  }
}
</style>
