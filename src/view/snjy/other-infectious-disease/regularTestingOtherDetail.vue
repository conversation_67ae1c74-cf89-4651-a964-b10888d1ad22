<template>
  <div class="other-infectious-disease-record">
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :responsive="true"
      @bottom-action="handleBottomAction"
    >
      <!-- 左侧人员信息面板 -->
      <template #left>
        <personnel-selector
          v-model="personInfo.jgrybm"
          title="选择在押人员"
          :enableScan=false
          :showCaseInfo=showCaseInfo
          :personnelType=ryzt
          mode="detail"
          placeholder="请选择需要操作的在押人员"
        />
        <Record :infectionId="pk"></Record>
      </template>

      <!-- 业务登记表单 -->
      <template #business-registration>
        <!-- <DynamicForm
          ref="businessForm"
          v-model="businessFormData"
          :config="businessFormConfig"
          :mode="formModeView"
          :loading="loading"
        /> -->
        <div class="fm-content-info">
            <Form ref="formData" :model="formData"  inline style="margin-top: 20px;margin-bottom: 20px;">
            <div class="fm-content-box">
                <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />业务登记</p>
                <Row>
                    <Col span="5" class="col-title"><span>传染病类型</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5"><span>健康体检时间</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>健康体检方式</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5" class="col-title"><span>健康体检结果</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>检查人</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
            </div>
            <div class="fm-content-box">
                <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />业务登记</p>
                <Row>
                    <Col span="5" class="col-title"><span>告知(县)区级疾控机构日期</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5"><span>送交人</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>(县)区级疾控机构名称</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5" class="col-title"><span>是否收到转入疾控机构反馈</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>收到转入地疾控机构反馈日期</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5" class="col-title"><span>是否需要再次联系转入地疾控机构</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>再次联系转入地疾控机构日期</span></Col>
                    <Col span="7"><span>2222</span></Col>
                    <Col span="5" class="col-title"><span>附件</span></Col>
                    <Col span="7"><span>2222</span></Col>
                </Row>
            </div>
        
        
            </Form>
        </div>


      </template>

      <!-- 定期管控登记表单 -->
      <template #regular-management>
        <!-- <DynamicForm
          ref="regularForm"
          v-model="regularFormData"
          :config="regularFormConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleRegularFieldChange"
          @validate="handleRegularValidate"
        /> -->

        <div class="fm-content-info">
            <Form ref="formData" :model="formData"  inline style="margin-top: 20px;margin-bottom: 20px;">
            <div class="fm-content-box">
                <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />定期管控登记</p>
                <Row>
                    <Col span="5" class="col-title"><span>分类管控登记日期</span></Col>
                    <Col span="19"><span>2222</span></Col>
               
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>分类管控情况</span></Col>
                    <Col span="19"><span>2222</span></Col>
                </Row>
                <Row>
                    <Col span="5" class="col-title"><span>附件</span></Col>
                    <Col span="19"><span>2222</span></Col>
                </Row>
            </div>
            </Form>
        </div>
      </template>

    </DetailCardLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'
import Record from "./Record.vue";

export default {
  components: {
    Record
  },
  name: 'OtherInfectiousDiseaseRecord',
  props: {
    pk: {
      type: String,
      default: () => ({})
    }
  },
  data() {
    return {
    formData:{},
      ryzt:'ZS',
      loading: false,
      formMode: FORM_MODES.CREATE,
      formModeView: FORM_MODES.VIEW,
      showCaseInfo: false,
      detail: 'detail',

      // 人员信息
      personInfo: {
        jgrybm: ''
      },

      // 左侧面板配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true,
        data: {}
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'business-registration',
          title: '业务登记',
          icon: 'ios-calendar',
          iconColor: '#5b8ff9',
          slot: 'business-registration',
          showHeader: true
        },
        {
          name: 'regular-management',
          title: '定期管控登记',
          icon: 'ios-calendar',
          iconColor: '#5b8ff9',
          slot: 'regular-management',
          showHeader: false
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ],

      // 定期管控登记表单数据
      regularFormData: {
        id: '', // 主键
        infectionId: '', // 传染病登记ID
        jgrybm: '', // 监管人员编码
        checkDate: '', // 分类管控登记日期
        managementSituation: '', // 分类管控情况
        controlPersonnel: '', // 管控人员
        attUrl: '', // 附件地址

        // 经办信息（系统自动填充）
        operatePolice: '', // 登记民警
        operateTime: '' // 登记时间
      },
      // 合并后的表单数据
      businessFormData: {
        // 基本信息
        jgrybm: '', // 监管人员编码
        jgryxm: '', // 监管人员姓名

        // 业务登记相关
        infectiousDiseaseType: '', // 传染病类型
        checkupMethod: '', // 健康体检方式
        checkupTime: '', // 健康体检时间
        checkupResult: '', // 健康体检结果
        checkupExaminer: '', // 检查人

        // 消息通报情况相关
        jkjgrqmc: '', // (县)区级疾控机构名称
        gzjkjgrq: '', // 告知(县)区级疾控机构日期
        submitter: '', // 送交人
        sfjszrjkjgfkxx: '', // 是否收到转入疾控机构反馈
        jszrjkjgfkxxrq: '', // 收到转入疾控机构反馈日期
        sfxyzclxzrjkjg: '', // 是否需要再次联系转入地疾控机构
        zclxzrjkjgrq: '', // 再次联系转入地疾控机构日期
        attUrl: "", // 附件地址

        // 经办信息（系统自动填充）
        operatePolice: '', // 登记民警
        operatePoliceSfzh: '', // 登记民警身份证号
        operateTime: '' // 登记时间
      }
    }
  },

  computed: {

    businessFormConfig() {
      return [
        {
          disabled: true,
          title: '业务登记',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: true,
          collapsed: false,
          showHeader: false,
          fields: [
            {
              disabled: true,
              key: 'infectiousDiseaseType',
              label: '传染病类型',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CRBLX',
              props: {
                placeholder: '传染病类型',
                multiple: false,
                filterField: '2,3,4,5,99'
              }
            },
            {
              disabled: true,
              key: 'checkupTime',
              label: '健康体检时间',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              disabled: true,
              key: 'checkupMethod',
              label: '健康体检方式',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_JKTJFS',
              props: {
                placeholder: '请选择健康体检方式'
              }
            },
            {
              disabled: true,
              key: 'checkupResult',
              label: '健康体检结果',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX',
              props: {
                placeholder: '请选择检查结果'
              }
            },
            {
              disabled: true,
              key: 'checkupExaminer',
              label: '检查人',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入检查人'
              }
            }
          ]
        },

        {
          disabled: true,
          title: '消息通报情况登记',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: true,
          collapsed: false,
          showHeader: true,
          fields: [
            {
              disabled: true,
              key: 'gzjkjgrq',
              label: '告知(县)区级疾控机构日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              disabled: true,
              key: 'submitter',
              label: '送交人',
              type: FIELD_TYPES.INPUT,
              required: false,
              span: 12,
              props: {
                placeholder: '请输入'
              }
            },
            {
              disabled: true,
              key: 'jkjgrqmc',
              label: '(县)区级疾控机构名称',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入'
              }
            },
            {
              disabled: true,
              key: 'sfjszrjkjgfkxx',
              label: '是否收到转入疾控机构反馈',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_TYSFDM',
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              disabled: true,
              key: 'jszrjkjgfkxxrq',
              label: '收到转入地疾控机构反馈日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              dependsOn: {
                key: 'sfjszrjkjgfkxx',    // 依赖字段
                value: '1',               // 当值为'1'(是)时
                action: 'show'            // 显示该字段
              },
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              disabled: true,
              key: 'sfxyzclxzrjkjg',
              label: '是否需要再次联系转入地疾控机构',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_TYSFDM',
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              disabled: true,
              key: 'zclxzrjkjgrq',
              label: '再次联系转入地疾控机构日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              dependsOn: {
                key: 'sfxyzclxzrjkjg',    // 依赖字段
                value: '1',               // 当值为'1'(是)时
                action: 'show'            // 显示该字段
              },
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              disabled: true,
              key: 'attUrl',
              label: '上传附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              required: false,
              span: 24, // 文件上传组件建议占满一行
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true
              },
              events: {
                'fileComplete': this.fileCompleteFileCertUrl,
              }
            }
          ]
        }
      ]
    },
    // 定期管控登记表单配置
    regularFormConfig() {
      return [
        {
          title: '定期管控登记',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showHeader: false,
          fields: [
            {
              key: 'checkDate',
              label: '分类管控登记日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'managementSituation',
              label: '分类管控情况',
              type: FIELD_TYPES.TEXTAREA,
              required: true,
              span: 24,
              props: {
                placeholder: '请输入分类管控情况',
                rows: 4,
                maxlength: 500,
                showWordLimit: true
              }
            },
            {
              disabled: true,
              key: 'attUrl1',
              label: '上传附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              required: false,
              span: 24, // 文件上传组件建议占满一行
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true
              },
              events: {
                'fileComplete': this.fileCompleteFileCertUrl2,
              }
            }

          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  watch: {
    pk: {
      handler(newVal) {
        if (newVal) {
          this.regularFormData.infectionId = newVal
          this.loadInfectionDetails()
        }
      },
      immediate: true
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),

    // 初始化数据
    initializeData() {
      if (this.pk) {
        this.regularFormData.infectionId = this.pk
        this.loadInfectionDetails()
      }
    },

    // 加载传染病详情
    loadInfectionDetails() {
      if (!this.pk) return

      this.loading = true
      this.authGetRequest({
        url: this.$path.infection_get,
        params: { id: this.pk }
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          const data = res.data || res.result
          if (data) {
            // 设置人员信息
            this.personInfo.jgrybm = data.jgrybm
            this.regularFormData.jgrybm = data.jgrybm
            this.businessFormData = data;
          }
        }
      }).catch(error => {
        this.loading = false
        console.error('加载传染病详情失败：', error)
      })
    },

    // 处理定期管控表单字段变化
    // handleRegularFieldChange(field, value, formData) {
    //   console.log('定期管控表单字段变化：', field, value)
    // },

    // 处理定期管控表单验证
    // handleRegularValidate(valid, errors) {
    //   console.log('定期管控表单验证：', valid, errors)
    // },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event;
      if (action.name === 'back') {
        this.handleBack()
      } else if (action.name === 'submit') {
        this.handleSubmit()
      }
    },

    // 处理返回
    handleBack() {
      this.$emit('toback')
    },

    // 处理提交
    handleSubmit() {
      console.log('开始表单验证...')

      // 验证表单
      this.$refs.regularForm?.validate().then(valid => {
        console.log('表单验证结果:', valid)

        if (valid) {
          console.log('验证通过，开始保存数据')
          this.saveRegularData()
        } else {
          console.log('验证失败，阻止提交')
          this.$Message.error('请检查表单填写是否完整')
        }
      }).catch(error => {
        console.error('表单验证异常：', error)
        this.$Message.error('表单验证失败')
      })
    },

    // 准备定期管控提交数据
    prepareRegularSubmitData() {
      const submitData = { ...this.regularFormData }

      // 过滤掉空值
      Object.keys(submitData).forEach(key => {
        if (submitData[key] === '' || submitData[key] === null || submitData[key] === undefined) {
          delete submitData[key]
        }
      })

      return submitData
    },

    // 保存定期管控数据
    saveRegularData() {
      this.loading = true

      // 准备提交数据
      const submitData = this.prepareRegularSubmitData()

      // 调用定期检查登记创建接口
      this.authPostRequest({
        url: this.$path.regular_inspection_create,
        params: submitData
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          this.$Message.success('定期管控登记保存成功')
          // 可以选择是否返回或继续编辑
          this.handleBack();
        } else {
          this.$Message.error(res.msg || res.message || '定期管控登记保存失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('定期管控登记保存失败：', error)
      })
    },





    // 所有文件上传完成回调
    fileCompleteFileCertUrl(files) {
      console.log('所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')

    },
    // 所有文件上传完成回调
    fileCompleteFileCertUrl2(files) {
      console.log('所有文件上传完成:', files)
      this.$Message.success('所有文件上传完成')
      this.formData.attUrl = JSON.stringify(files);
    }


  }
}
</script>

<style lang="less" scoped>
.other-infectious-disease-record {
  height: 100%;

  /deep/ .detail-card-layout {
    height: 100%;
  }

  /deep/ .dynamic-form {
    padding: 16px;
  }

  /deep/ .ivu-card-body {
    padding: 16px;
  }
}
</style>
