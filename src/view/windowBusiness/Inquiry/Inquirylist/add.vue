<template>
  <div class='Inquiry-wrap'>
    <div class='Inquiry-wrap-left'>
      <!-- 使用新的人员选择组件 -->
      <personnel-selector v-model="formData.jgrybm" title="被监管人员" placeholder="点击选择在押人员或扫码识别" :show-case-info="true"
        :enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
      <record :jgrybm="formData.jgrybm" style="width: 100%;" />
    </div>
    <div class="Inquiry-wrap-right">
      <div class='bary-flex' style='margin-top:10px;'>
        <p class="detail-title ml-16-title">提讯人</p>
        <p><Button type="primary" @click='addInvestigators' style="cursor: pointer;"
            v-if="formData.casePersonnelList.length == 2">添加办案人员</Button>&nbsp;&nbsp;<Button type="primary"
            @click='addInvestigatorsCoop'>添加协同办案人员</Button></p>
      </div>
      <div class='bary-flex' style="margin-left: 16px;">
        <Form :ref="'formBox-' + index" class='form-box' :key="index + 'a'"
          v-for='(item, index) in formData.casePersonnelList' :model="item" :label-width="120" :label-colon="true"
          style="margin:0 16px 16px 0"
          :style="{ width: Number(formData.casePersonnelList.length + formData.coopCasePersonnelList.length) == 2 ? '48%' : '31%' }">
          <Row style='padding:10px 0;background:#f5f7fa'>
            <Col span="22" style="margin-bottom:0px;text-align:center;">办案人员（{{ Number(index + 1) }}）</Col>
            <Col span="2">
            <Icon size="20" v-if="formData.casePersonnelList.length == 3" @click="deleteCasePerson(item, index)"
              type="ios-close-circle" />&nbsp;&nbsp;&nbsp;</Col>
          </Row>
          <Row>
            <Col span="23" style="margin-bottom:0px">
            <FormItem label="照片采集" prop="zpUrl">
              <RadioGroup v-model="item.isdisabled" size="large" @on-change="getRadio">
                <Radio :disabled="item.id ? true : false" :label="0">本地上传</Radio>
                <Radio :disabled="item.id ? true : false" :label="1">拍照上传</Radio>
              </RadioGroup>
              <br />
              <div v-if="item.isdisabled" style="display: flex;">
                <div v-if="!item.zpUrl" @click="takePhoto(item, index)"
                  style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                  <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
                </div>
                <div v-if="item.zpUrl"
                  style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                  <img :src="item.zpUrl" style="width: 96px; height: 96px;" />
                </div>
                <!-- <div style="width: 100%;">
                       摄像头：<Tag color="primary" ghost style="position: relative;top: -0px;left: -4px;padding:0 5px;cursor: pointer;" v-if="item.isdisabled"   :loading="buttenloading"
                      @click="capture()" >采集照片</Tag><video ref='videoElement' style="margin-top: 14px;" id="video"  height="88" autoplay></video>
                      <canvas id="canvas" width="88" height="88" style="display:none;"></canvas>
                      
                    </div>
                    <div  style="position: relative;" ><span style="position: relative;top: 0px;">采集图：</span><img :src="item.zpUrl"   style="width: 88px;height: 88px;margin: 33px 14px 0 0;" /><Icon @click="remove" color="red" size="20" style="position: absolute;right: 11%;top:30px;cursor: pointer;" type="md-close-circle" /></div> -->
              </div>
              <Tooltip v-else max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。" placement="top">
                <s-ImageUploadLocal v-model="item.zpUrl" :maxSize="5120" ref="imgUpload" :multiple="false"
                  @getfile="(data) => getfile(data, index, 'casePersonnelList')" @removeFile="removeFile"
                  :defaultList="item.defaultList" :maxFiles="1" />

              </Tooltip>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入姓名', required: true }]"
              style="width: 100%;">
              <!-- <div class='jgrySelect-flex'>  -->
              <!-- <Input :disabled="item.id?true:false" v-model="item.xm" placeholder="请填写" maxlength="" style="width: 50%;"></Input>  -->
              <s-dialog :dialogMark="globalAppcode + ':ckywhqbarylb'" :disabled="false" :readonly="false"
                v-model="item.xm" return="resId:id|resName:xm"
                @on-select="(data) => onSelectXm(data, index, item, 'casePersonnelList')"
                @on-clear="(data) => onClearXm(data, index, item, 'casePersonnelList')" v-bind.sync="item">
              </s-dialog>
              <!-- </div> -->
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="办案单位类型" prop="orgtype"
              :rules="[{ trigger: 'blur,change', message: '请添加', required: true, }]" style="width: 100%;">
              <s-dicgrid @values="(data) => onSelectDwType(data, index, item, 'casePersonnelList')" v-model="item.orgtype"
                :disabled="item.id ? true : false" dicName="ZD_KSS_BADWLX" />
            </FormItem>
            <FormItem label="办案单位" prop="badwdm" v-if="item.disFale"
              :rules="[{ trigger: 'blur,change', message: '请添加', required: true, }]" style="width: 100%;">
              <s-dicgrid @values="(data) => onSelectDwmc(data, index, item, 'casePersonnelList')" v-if="item.disFale"
                v-model="item.badwdm" :disabled="item.id ? true : false" :dicName="item.disdicname" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="证件类型" prop="zjlx" style="width: 100%;">
              <s-dicgrid v-model="item.zjlx" :disabled="item.id ? true : false" dicName="ZD_GABBZ_ZJZL" />
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="证件号码" prop="zjhm" style="width: 100%;">
              <Input v-model="item.zjhm" placeholder="" :disabled="item.id ? true : false" maxlength=""
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="联系方式" prop="lxfs" style="width: 100%;">
              <Input v-model="item.lxfs" placeholder="" maxlength="" :disabled="item.id ? true : false"
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="性别" prop="xb" style="width: 100%;">
              <s-dicgrid v-model="item.xb" :disabled="item.id ? true : false" dicName="ZD_XB" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="警号" prop="jh" style="width: 100%;">
              <Input v-model="item.jh" placeholder="" :disabled="item.id ? true : false" maxlength=""
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="上传工作证件" prop="gzzjUrl" style="width: 100%;">
              <file-upload :disabled="item.id ? true : false" :defaultList="item.fileList" :serviceMark="serviceMark"
                :bucketName="bucketName" :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" v-if="showFile"
                @fileRemove="fileRemoveFile" @fileComplete="(data) => fileCompleteFile(data, index, 'casePersonnelList')" />
            </FormItem>
            </Col>
          </Row>
        </Form>
        <Form :ref="'coopformBox-' + index" class='form-box' :key="index + 'b'"
          v-for='(item, index) in formData.coopCasePersonnelList' :model="item" :label-width="120" :label-colon="true"
          style="margin:0 16px 16px 0;" :style="{ width: formData.casePersonnelList.length == 2 ? '31%' : '31%' }">
          <Row style='padding:10px 0;background:#f5f7fa'>
            <Col span="22" style="margin-bottom:0px;text-align:center;">协同办案人员（{{ Number(index + 1) }}）</Col>
            <Col span="2">
            <Icon size="20" @click="deleteCasePersonCoop(item, index)" type="ios-close-circle" />&nbsp;&nbsp;&nbsp;</Col>
          </Row>
          <Row>
            <Col span="23" style="margin-bottom:0px">
            <FormItem label="照片采集" prop="zpUrl">
              <RadioGroup v-model="item.isdisabled" size="large" @on-change="getRadio">
                <Radio :disabled="item.id ? true : false" :label="0">本地上传</Radio>
                <Radio :disabled="item.id ? true : false" :label="1">拍照上传</Radio>
              </RadioGroup>
              <br />
              <div v-if="item.isdisabled" style="display: flex;">
                <div v-if="!item.zpUrl" @click="takePhoto(item, index)"
                  style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                  <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
                </div>
                <div v-if="item.zpUrl"
                  style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                  <img :src="item.zpUrl" style="width: 96px; height: 96px;" />
                </div>
                <!-- <div style="width: 100%;">
                       摄像头：<Tag color="primary" ghost style="position: relative;top: -0px;left: -4px;padding:0 5px;cursor: pointer;" v-if="item.isdisabled"   :loading="buttenloading"
                      @click="capture()" >采集照片</Tag><video ref='videoElement' style="margin-top: 14px;" id="video"  height="88" autoplay></video>
                      <canvas id="canvas" width="88" height="88" style="display:none;"></canvas>
                      
                    </div>
                    <div  style="position: relative;" ><span style="position: relative;top: 0px;">采集图：</span><img :src="item.zpUrl"   style="width: 88px;height: 88px;margin: 33px 14px 0 0;" /><Icon @click="remove" color="red" size="20" style="position: absolute;right: 11%;top:30px;cursor: pointer;" type="md-close-circle" /></div> -->
              </div>
              <Tooltip v-else max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。" placement="top">
                <s-ImageUploadLocal v-model="item.zpUrl" :maxSize="5120" ref="imgUpload" :multiple="false"
                  @getfile="(data) => getfile(data, index, 'coopCasePersonnelList')" @removeFile="removeFile"
                  :defaultList="item.defaultList" :maxFiles="1" />

              </Tooltip>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入姓名', required: true }]"
              style="width: 100%;">
              <!-- <div class='jgrySelect-flex'> <Input :disabled="item.id?true:false" v-model="item.xm" placeholder="请填写" maxlength="" style="width: 50%;"></Input> -->
              <s-dialog :dialogMark="globalAppcode + ':ckywhqbarylb'" :disabled="false" :readonly="false"
                v-model="item.xm" return="resId:id|resName:xm"
                @on-select="(data) => onSelectXm(data, index, item, 'coopCasePersonnelList')"
                @on-clear="(data) => onClearXm(data, index, item, 'coopCasePersonnelList')" v-bind.sync="item">
              </s-dialog>
              <!-- </div> -->
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="办案单位类型" prop="orgtype"
              :rules="[{ trigger: 'blur,change', message: '请添加', required: true, }]" style="width: 100%;">
              <s-dicgrid @values="(data) => onSelectDwType(data, index, item, 'coopCasePersonnelList')"
                v-model="item.orgtype" :disabled="item.id ? true : false" dicName="ZD_KSS_BADWLX" />
            </FormItem>
            <FormItem label="办案单位" prop="badwdm" v-if="item.disFale"
              :rules="[{ trigger: 'blur,change', message: '请添加', required: true, }]" style="width: 100%;">
              <s-dicgrid v-if="item.disFale" @values="(data) => onSelectDwmc(data, index, item, 'coopCasePersonnelList')"
                v-model="item.badwdm" :disabled="item.id ? true : false" :dicName="item.disdicname" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="证件类型" prop="zjlx" style="width: 100%;">
              <s-dicgrid v-model="item.zjlx" :disabled="item.id ? true : false" dicName="ZD_GABBZ_ZJZL" />
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="证件号码" prop="zjhm" style="width: 100%;">
              <Input v-model="item.zjhm" placeholder="" :disabled="item.id ? true : false" maxlength=""
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="联系方式" prop="lxfs" style="width: 100%;">
              <Input v-model="item.lxfs" placeholder="" maxlength="" :disabled="item.id ? true : false"
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
            <Col span="23">
            <FormItem label="性别" prop="xb" style="width: 100%;">
              <s-dicgrid v-model="item.xb" :disabled="item.id ? true : false" dicName="ZD_XB" />
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="警号" prop="jh" style="width: 100%;">
              <Input v-model="item.jh" placeholder="" :disabled="item.id ? true : false" maxlength=""
                style="width: 100%;"></Input>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="23">
            <FormItem label="上传工作证件" prop="gzzjUrl" style="width: 100%;">
              <file-upload :disabled="item.id ? true : false" :defaultList="item.fileList" :serviceMark="serviceMark"
                :bucketName="bucketName" :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" v-if="showFile"
                @fileRemove="fileRemoveFile"
                @fileComplete="(data) => fileCompleteFile(data, index, 'coopCasePersonnelList')" />
            </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
      <div class="fm-content-wrap" style="padding: 0;">

        <div class="fm-content-form">
          <Form ref="taskForm" :model="formData" :label-width="140" :label-colon="true">
            <p class="fm-content-wrap-title"
              style='margin-bottom: 16px;padding:10px; border-bottom: 1px solid #cee0f0; background: #eff6ff;'>
              <Icon type="md-list-box" size="24" color="#2b5fda" />业务信息
            </p>
            <Row>
              <Col span="11">
              <FormItem label="提讯事由" prop="arraignmentReason"
                :rules="[{ trigger: 'blur,change', message: '请选择提讯事由', required: true }]" style="width: 100%;">
                <s-dicgrid v-model="formData.arraignmentReason" dicName="ZD_WB_TXSY" />
              </FormItem>
              </Col>
              <Col span="1">
              </Col>
              <Col span="11">
              <FormItem label="预约提讯时间" prop="arraignmentTime" style="width: 100%;"
                :rules="[{ trigger: 'blur,change', message: '请选择预约提讯时间', required: true, type: 'array' }]">
                <el-date-picker format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss'
                  v-model="formData.arraignmentTime" type="datetimerange" size='small' range-separator="至"
                  style='width:100%' start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="介绍信编号" prop="arraignmentLetterNumber" style="width: 100%;">
                <Input v-model="formData.arraignmentLetterNumber" placeholder="" maxlength=""
                  style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="1">
              </Col>
              <Col span="11">
              <FormItem label="提讯凭证类型" prop="evidenceType" style="width: 100%;"
                :rules="[{ trigger: 'blur,change', message: '请选择提讯凭证类型', required: true, }]">
                <s-dicgrid v-model="formData.evidenceType" :multiple="true" dicName="ZD_WB_TXPZLX"
                  @values="getEvidenceType" />
              </FormItem>
              </Col>
            </Row>
            <Row v-for="(ele, i) in formData.voucherList" :key="i + 'pzData'">
              <Col span="11">
              <FormItem :label="ele.name" prop="evidenceNumber" style="width: 100%;">
                <Input v-model="ele.evidenceNumber" placeholder="" maxlength="" style="width: 100%;" />
              </FormItem>
              </Col>
              <Col span="1">
              </Col>
              <Col span="11">
              <FormItem :label="'上传' + ele.name" prop="evidenceUrl" style="width: 100%;">
                <file-upload :defaultList="ele.evidenceUrlFile" :serviceMark="serviceMark" :bucketName="bucketName"
                  :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" v-if="showFile"
                  @fileRemove="fileRemoveFile" @fileComplete="(data) => fileCompleteFilePz(data, i)" />
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="11">
              <FormItem label="未成年代理人" prop="minorAgent" style="width: 100%;">
                <Input v-model="formData.minorAgent" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="1">
              </Col>
              <Col span="11">
              <FormItem label="未成年监护人" prop="minorGuardian" style="width: 100%;">
                <Input v-model="formData.minorGuardian" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="翻译人员" prop="translator" style="width: 100%;">
                <Input v-model="formData.translator" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="1">
              </Col>
              <Col span="11">
              <FormItem label="其他专业人员" prop="otherProfessionals" style="width: 100%;">
                <Input v-model="formData.otherProfessionals" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="16">
              <FormItem label="上传专业人员书面证明" prop="professionalCertUrl" style="width: 100%;">
                <file-upload :defaultList="professionalCertUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                  :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" v-if="showFile"
                  @fileRemove="fileRemoveFile" @fileComplete="fileCompleteFileCertUrl" />
              </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
      <br />

    </div>
    <div class='bsp-base-fotter'>
      <Button @click='goBack'>返 回</Button>
      <Button @click="printPageEvent" :loading="printLoading" type="primary">打印进门条/提讯通知单</Button>
      <Button @click="saveData" type="primary" :loading='loading'>保 存</Button>
    </div>

    <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>

  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import { orgSelector } from 'sd-org-selector'
import { sDialog } from 'sd-custom-dialog'
import cameraModal from './camera-modal.vue'
import record from './record.vue'
import personnelSelector from '@/components/personnel-selector/index.vue'
import { getToken } from "@/libs/util";
import print from "print-js";
import vueToPdf from "vue-to-pdf";
Vue.use(vueToPdf);
import Vue from "vue";
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";
export default {
  components: {
    sDataGrid, sImageUploadLocal, fileUpload, orgSelector, sDialog, cameraModal, record, personnelSelector
  },
  data() {
    return {
      globalAppcode: serverConfig.APP_CODE,
      disdicname: 'ZD_BADW_GAJG',
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,
      selectUseIds: '',
      showFile: true,
      showImg: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      importUrl: this.$path.upload_fj,
      custom_loading: false,
      defaultList: [],
      fileList: [],
      professionalCertUrl: [],
      evidenceUrl: [],
      buttenloading: false,
      stream: null,
      uploadForm: {},
      params: {},
      showData: false,
      modalTitle: '新增办案人员',
      pzData: [],
      openModal: false,
      formData: {
        isdisabled: 0,
        prison: {},
        voucherList: [
          // { "name": "提讯提接证",
          //   "jpcode": "txtjz",
          //   "code": "0",
          //   evidenceType:'0',
          //   "scode": "TiXunTiJieZheng"}
        ],
        casePersonnelList: [
          { isdisabled: 0, },
          { isdisabled: 0, }
        ],
        coopCasePersonnelList: [],
        arraignmentReason: '0',
        evidenceType: '0'
      },
      loading: false,
      saveType: 'add',
      loadingStates: [],
      curBaryData: {},
      printLoading: false
    }
  },
  watch: {
    orgI: {
      handler(newVal, oldVal) {
        //   console.log(newVal, oldVal, 'newVal, oldVal');
        if (newVal) {
          this.showData = true
        }

      },
      deep: true,
      immediate: true // 立即执行
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    onSelectDwmc(data, index, item, fileName) {
      //  console.log(data,index,item,'1212',this.formData[fileName][index])
      this.$set(item, 'badwdm', data[0].code)
      this.$set(item, 'badwmc', data[0].name)
    },
    onSelectDwType(data, index, item, fileName) {
      //  console.log(data,index,item,'1212',this.formData[fileName][index])
      //  this.$set(item,'badwdm',data[0].code)
      //  this.$set(item,'badwmc',data[0].name)
      this.$set(this.formData[fileName][index], 'disFale', false)
      setTimeout(() => {
        switch (data[0].code) {
          case '1':
            this.$set(this.formData[fileName][index], 'disdicname', 'ZD_BADW_GAJG')
            this.$set(item, 'disdicname', 'ZD_BADW_GAJG')
            break;
          case '2':
            this.$set(this.formData[fileName][index], 'disdicname', 'ZD_BADW_JCY')
            this.$set(item, 'disdicname', 'ZD_BADW_JCY')
            break;
          case '3':
            this.$set(this.formData[fileName][index], 'disdicname', 'ZD_BADW_FY')
            this.$set(item, 'disdicname', 'ZD_BADW_FY')
            break;
          case '4':
            this.$set(this.formData[fileName][index], 'disdicname', 'ZD_BADW_AQJG')
            this.$set(item, 'disdicname', 'ZD_BADW_AQJG')
            break;
          case '5':
            this.$set(this.formData[fileName][index], 'disdicname', '')
            break;
        }
        this.$set(item, 'disFale', true)
      }, 800)
      //  console.log(data,index,item,'1212')
    },
    deleteCasePerson(item, index) {
      this.formData.casePersonnelList.splice(index, 1)
    },
    deleteCasePersonCoop(item, index) {
      this.formData.coopCasePersonnelList.splice(index, 1)
    },
    takePhoto(row, index) {
      this.curBaryData = row
      this.curBaryData.index = index
      this.$refs.cameraModal.open({});
    },
    takePhotoItem(imgUrl) {
      this.$set(this.curBaryData, 'zpUrl', imgUrl)
      this.$set(this.formData.casePersonnelList[this.curBaryData.index], 'zpUrl', imgUrl)
    },
    getEvidenceType(data) {
      console.log(data, '1212')
      this.formData.voucherList = JSON.parse(JSON.stringify(data))
      this.formData.voucherList.forEach(item => {
        this.$set(item, 'evidenceType', item.code)
      })


    },
    goBack() {
      this.$emit('toback')
    },
    saveData() {
      let arr = [this.$refs.taskForm.validate()]
      if (this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0) {
        this.formData.casePersonnelList.forEach((item, index) => {
          console.log(this.$refs['formBox-' + index], 'formBox')
          arr.push(this.$refs['formBox-' + index][0].validate())
        })
        this.formData.coopCasePersonnelList.forEach((item, index) => {
          console.log(this.$refs['coopformBox-' + index], 'formBox')
          arr.push(this.$refs['coopformBox-' + index][0].validate())
        })
      }
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员！')
        return
      }
      Promise.all(arr).then(data => {
        console.log(data, '121   2')
        this.loading = true
        this.saveDataForm()
      })
    },
    saveDataForm() {
      if (this.formData.arraignmentTime && this.formData.arraignmentTime.length > 0) {
        this.$set(this.formData, 'startApplyArraignmentTime', this.formData.arraignmentTime[0])
        this.$set(this.formData, 'endApplyArraignmentTime', this.formData.arraignmentTime[1])
      }
      console.log(this.formData, 'params')
      this.$store.dispatch('authPostRequest', { url: this.$path.acp_arraignment_create, params: this.formData }).then(resp => {
        if (resp.success) {
          this.loading = false
          this.goBack()
          this.$Message.success('提讯登记成功')
        } else {
          this.loading = false
          this.$Message.error(resp.msg || '提讯登记失败')
        }
      }).catch(err => {
        this.loading = false
      })
    },

    printPageEvent() {
      if (!this.formData.jgrybm) {
        console.log('直接打印');
        this.printLoading = true
        this.directPrint()
      } else {
        console.log('带数据');
        this.printLoading = true
        this.savePrintPage()
      }
    },
    // 打印进门条/提讯通知单--打印空的进门条
    directPrint() {
      let _this = this;
      let formId = '1948289541387456512'
      _this.pldySpin = true;
      let iframeUrl =`${ this.$path.pdf_getPdf }?plugIns=MultipleRowTableRenderPolicy&formId=${formId}&access_token=${getToken()}` + "#toolbar=0";
      printJS({
        printable: iframeUrl,
        type: "pdf",
        header: "打印",
        onLoadingEnd: async () => {
          _this.pldySpin = false;
          this.printLoading = false
          // this.$nextTick(() => {
          //   this.goBack()
          // })
        },
      });
    },
    // 打印进门条/提讯通知单--打印带数据的进门条
    savePrintPage() {
      let arr = [this.$refs.taskForm.validate()]
      if (this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0) {
        this.formData.casePersonnelList.forEach((item, index) => {
          console.log(this.$refs['formBox-' + index], 'formBox')
          arr.push(this.$refs['formBox-' + index][0].validate())
        })
        this.formData.coopCasePersonnelList.forEach((item, index) => {
          console.log(this.$refs['coopformBox-' + index], 'formBox')
          arr.push(this.$refs['coopformBox-' + index][0].validate())
        })
      }
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员！')
        return
      }
      Promise.all(arr).then(data => {
        console.log(data, '121   2')
        //this.loading = true
        this.saveDataFormPrint()
      })
    },
    saveDataFormPrint() {
      
      if (this.formData.arraignmentTime && this.formData.arraignmentTime.length > 0) {
        this.$set(this.formData, 'startApplyArraignmentTime', this.formData.arraignmentTime[0])
        this.$set(this.formData, 'endApplyArraignmentTime', this.formData.arraignmentTime[1])
      }
      console.log(this.formData,'提交数据');
      if(this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0){
        const needFields = ['xm', 'badwmc', 'zjlx', 'zjhm', 'badwdm', 'lxfs', 'xb', 'zpurl'];
        const mergedObj = needFields.reduce((acc, field) => {
          acc[field] = [];
          return acc;
        }, {});
        this.formData.casePersonnelList.forEach(item => {
          needFields.forEach(field => {
            const value = item[field];
            if (value !== null && value !== undefined && value !== '') {
              mergedObj[field].push(value);
            }
          });
        });
        
        needFields.forEach(field => {
          mergedObj[field] = mergedObj[field].join(',');
        });
        
        this.formData.casePersonPrintData= mergedObj;
        this.$set(this.formData.casePersonPrintData,'add_user',this.$store.state.common.userName)
        this.$set(this.formData.casePersonPrintData,'add_time',formatDateparseTime(new Date()))
        // console.log(this.formData,'-----------------------');
        
      }
      // return;
      
      this.printPdf()
    },
    printPdf() {
      const url = this.$path.pdf_getPdf;
      const formData = new FormData();
      formData.append('access_token', getToken());
      formData.append('businessData', JSON.stringify(this.formData));
      formData.append('formId', '1948289541387456512');
      formData.append('plugIns', 'MultipleRowTableRenderPolicy');

      fetch(url, {
        method: 'POST',
        body: formData
      })
      .then(response => {
          if (!response.ok) {
              throw new Error('Network response was not ok');
          }
          return response.blob();
      })
      .then(blob => {
          const blobUrl = URL.createObjectURL(blob);
          
          printJS({
              printable: blobUrl,
              type: 'pdf',
              header: '打印',
              onLoadingEnd: () => {
                URL.revokeObjectURL(blobUrl); 
                this.pldySpin = false;
                this.printLoading = false
              },
              onError: (error) => {
                console.error('打印错误:', error);
                URL.revokeObjectURL(blobUrl);
                this.pldySpin = false;
                this.printLoading = false
              }
          });
      })
      .catch(error => {
        console.error('Error:', error);
      });
    },
    // 处理人员选择变化
    handlePersonnelChange(personnelData, jgrybm) {
      if (personnelData && jgrybm) {
        this.formData.prison = personnelData
        this.formData.jgrybm = jgrybm
        // 自动获取办案人员信息
        this.loadCasePersonnelInfo(jgrybm)
      } else {
        this.formData.prison = {}
        this.formData.jgrybm = ''
        // 清空办案人员信息
        this.clearCasePersonnelInfo()
      }
    },

    // 根据监管人员编码获取办案人员信息
    async loadCasePersonnelInfo(jgrybm) {
      if (!jgrybm) return

      try {
        this.$Message.loading('正在获取办案人员信息...', 0)

        const response = await this.$store.dispatch('authGetRequest', {
          url: this.$path.acp_arraignment_getCasePersonnelByjgrybm,
          params: { jgrybm }
        })

        this.$Message.destroy()

        // 兼容不同的响应格式
        const isSuccess = response.success || (response.code === 0)
        const responseData = response.data

        if (isSuccess && responseData) {
          this.fillCasePersonnelForms(responseData)
          this.$Message.success('办案人员信息已自动填充')
        }
      } catch (error) {
        this.$Message.destroy()
        console.error('获取办案人员信息失败:', error)
        this.$Message.warning('获取办案人员信息失败，请手动填写')
      }
    },

    // 填充办案人员表单信息
    fillCasePersonnelForms(casePersonnelData) {
      // 检查返回的数据是否为数组
      const personnelArray = Array.isArray(casePersonnelData) ? casePersonnelData : [casePersonnelData]

      // 字段映射关系
      const fieldMapping = {
        xm: 'bar',           // 姓名 -> 办案人
        orgtype: 'badwlx',   // 办案单位类型
        badwdm: 'badw',      // 办案单位代码 -> 办案单位
        badwmc: 'badw',      // 办案单位名称 -> 办案单位
        zjlx: 'barzjlx',     // 证件类型 -> 办案人证件类型
        zjhm: 'barzjhm',     // 证件号码 -> 办案人证件号码
        lxfs: 'barlxff',     // 联系方式 -> 办案人联系方式
        xb: 'barxb',         // 性别 -> 办案人性别
        jh: 'bahj'           // 警号
      }

      // 确保有足够的办案人员表单项
      if (this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0) {
        // 遍历API返回的办案人员数据
        personnelArray.forEach((personnelData, index) => {
          // 确保有对应的表单项
          if (index < this.formData.casePersonnelList.length) {
            const currentPersonnel = this.formData.casePersonnelList[index]

            // 填充基本信息
            Object.keys(fieldMapping).forEach(formField => {
              const apiField = fieldMapping[formField]
              const apiValue = personnelData[apiField]

              if (apiValue !== undefined && apiValue !== null && apiValue !== '' && apiValue !== 'null') {
                this.$set(currentPersonnel, formField, String(apiValue))
              }
            })

            // 特殊处理：办案单位类型相关逻辑
            if (personnelData.badwlx) {
              this.$set(currentPersonnel, 'orgtype', String(personnelData.badwlx))
              this.$set(currentPersonnel, 'disFale', false)

              // 根据办案单位类型设置对应的字典名称
              setTimeout(() => {
                let dicName = ''
                switch (String(personnelData.badwlx)) {
                  case '1':
                    dicName = 'ZD_BADW_GAJG'
                    break;
                  case '2':
                    dicName = 'ZD_BADW_JCY'
                    break;
                  case '3':
                    dicName = 'ZD_BADW_FY'
                    break;
                  case '4':
                    dicName = 'ZD_BADW_AQJG'
                    break;
                  case '5':
                    dicName = ''
                    break;
                }
                this.$set(currentPersonnel, 'disdicname', dicName)
                this.$set(currentPersonnel, 'disFale', true)
              }, 100 + index * 50) // 为每个人员设置不同的延迟时间
            }

            // 特殊处理：照片信息
            if (personnelData.zpUrl && personnelData.zpUrl !== 'null') {
              this.$set(currentPersonnel, 'zpUrl', personnelData.zpUrl)
              this.$set(currentPersonnel, 'defaultList', [{ url: personnelData.zpUrl, name: '' }])
            }
          }
        })
      }
    },

    // 清空办案人员信息
    clearCasePersonnelInfo() {
      if (this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0) {
        // 重置所有办案人员信息
        this.formData.casePersonnelList.forEach((personnel) => {
          const resetFields = ['xm', 'orgtype', 'badwdm', 'badwmc', 'zjlx', 'zjhm', 'lxfs', 'xb', 'jh', 'zpUrl']

          resetFields.forEach(field => {
            this.$set(personnel, field, '')
          })

          this.$set(personnel, 'disFale', false)
          this.$set(personnel, 'disdicname', '')
          this.$set(personnel, 'defaultList', [])
        })
      }
    },
    addInvestigators() {
      console.log(this.formData, 'this.formData.')
      let obj = {
        isdisabled: 0
      }
      this.formData.casePersonnelList.push(obj)
    },
    addInvestigatorsCoop() {
      console.log(this.formData, 'this.formData.')
      let obj = {
        isdisabled: 0
      }
      this.formData.coopCasePersonnelList.push(obj)
    },

    // 上传组件回调方法
    handleSuccessImg(res, index) {
      console.log(res, '1212')
      this.defaultList.push(res.data);
      this.defaultList.forEach(ele => {
        ele.src = ele.url
        ele.imgSrc = ele.url
      })
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.defaultList.splice(e.index, 1);
          this.imgArr.forEach((ele, i) => {
            if (ele.url == e.url) {
              this.defaultList.splice(i, 1);
            }
          })
        },
      });
    },
    onSelectXm(data, index, item, fileName) {
      console.log(data, index, '1212')
      if (data[0] && data[0].badwdm) {
        this.$set(data[0], 'disFale', true)
      }
      this.$set(this.formData[fileName], [index], data[0])
      this.$set(this.formData[fileName][index], 'isdisabled', 0)
      this.formData[fileName][index].defaultList = [{ url: data[0].zpUrl, name: '' }]
    },
    onClearXm(data, index, item, fileName) {
      this.$set(this.formData[fileName], [index], {})
      this.$set(this.formData[fileName][index], 'isdisabled', 0)

    },
    onSelect(data, index, item) {
      let badwmc = []
      data.forEach(ele => {
        badwmc.push(ele.orgName)
      })
      this.$set(item, 'badwmc', badwmc.join(','))
      console.info(data, this.formData, index, item, 'onSelect')

    },
    onClear(data) {
      console.info(data, this.formData, 'onClear')
      //    this.$set(this.formData,'badwmc','')
    },
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFilePz(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData.voucherList[index], 'evidenceUrl', JSON.stringify(data))
        this.$set(this.formData.voucherList[index], 'evidenceUrlFile', data)
      }
    },
    fileCompleteFile(data, index, fileName) {
      if (data && data.length > 0) {
        this.$set(this.formData[fileName][index], 'gzzjUrl', JSON.stringify(data))
        this.$set(this.formData[fileName][index], 'fileList', data)
      }
    },
    fileCompleteFileCertUrl(data) {
      this.professionalCertUrl = data
      this.$set(this.formData, 'professionalCertUrl', JSON.stringify(data))
    },
    removeFile(file) {
      if (file.length == 0) {
        this.$set(this.formData, 'zpUrl', '')
        this.defaultList = []
      }
    },
    remove() {
      this.$set(this.formData, 'zpUrl', '')
      this.defaultList = []
    },
    async uploadImg(imgSrc) {
      let blob = this.dataURLtoBlob(imgSrc);
      let file = this.blobToFile(blob, "拍照");
      let formData = new FormData();
      formData.append("file", file);
      let res = await this.$store.dispatch('authPostRequest', { url: this.importUrl, params: formData })
      let { status, data } = res;
      if (status === 200) {
        this.formData.zpUrl = data.url;
      } else {
        this.errorModal({ content: "上传失败!" })
      }
    },
    dataURLtoBlob(dataurl) {
      let arr = dataurl.split(",");
      let type = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8str = new Uint8Array(n);
      while (n--) {
        u8str[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8str], { type });
    },
    blobToFile(blob, fileName) {
      blob.lastModifiedDate = new Date();
      blob.name = fileName;
      return blob;
    },
    getfile(file, index, fileName) {
      // console.log(file,'getfile')
      this.$set(this.formData[fileName][index], 'zpUrl', file.url)
      this.formData[fileName][index].defaultList = [file]
    },
    getRadio(value) {
      // console.log(value,'getRadio')
      console.log(this.formData.isdisabled, this.defaultList, 'this.formData.isdisabled')
      if (this.defaultList && this.defaultList.length == 0) {
        this.$set(this.formData, 'zpUrl', '')
      }
      if (value) {
        // 获取摄像头视频流
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
            this.stream = stream
            // this.$refs.videoElement= this.stream;
            const video = document.getElementById('video');
            video.srcObject = this.stream;
            video.play();
          });
        }
      } else {
        this.defaultList = []
        let faceImageUrl = this.formData.zpUrl
        if (faceImageUrl) {
          let urlObj = { url: faceImageUrl, name: '' }
          this.defaultList.push(urlObj)
        }
        this.stopCamera()
      }
    },
    stopCamera() {
      // 关闭摄像头
      if (this.stream) {
        const tracks = this.stream.getTracks();
        tracks.forEach(track => track.stop());
        this.$refs.videoElement.srcObject = null;
      }
    },
    capture() {
      this.buttenloading = true;
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const context = canvas.getContext('2d');
      context.drawImage(video, 0, 0, 100, 100);
      // 如果有自定义文件名称，优先使用自定义的文件名称
      let filenames = ''
      if (this.uploadForm.filename != undefined && this.uploadForm.filename != '') {
        filenames = this.uploadForm.filename + '.png'
      } else {
        filenames = this.uploadForm.code + '_' + this.picnum + '.png'
      }
      const base64 = canvas.toDataURL('image/png');//canvas.toDataURL();
      console.log(filenames, 'filenames', base64);
      this.uploadImg(base64);
      this.$set(this.formData, 'zpUrl', base64)
      this.defaultList = []
      let faceImageUrl = this.formData.zpUrl
      if (faceImageUrl) {
        let urlObj = { url: faceImageUrl, name: '' }
        this.defaultList.push(urlObj)
      }
      this.buttenloading = false;
    },
    addEvent(row) {
      this.showFile = true
      this.showImg = true
      this.saveType = 'add'
      this.formData = { isdisabled: 0, zpUrl: '' },
        this.fileList = []
      this.openModal = true
      this.modalTitle = '新增办案人员'
      let faceImageUrl = this.formData.zpUrl
      if (faceImageUrl) {
        let urlObj = { url: faceImageUrl, name: '' }
        this.defaultList.push(urlObj)
      }
    },
    editEvent(index, row, tag) {
      if (tag == 'edit') {
        this.modalTitle = '编辑办案人员'
        this.saveType = 'edit'
      } else {
        this.modalTitle = '查看办案人员'
        this.saveType = 'info'
      }

      this.openModal = false
      console.log(row);
      this.$store.dispatch("authGetRequest", {
        url: this.$path.acp_casePersonnel_get,
        params: {
          id: row.id
        }
      }).then(res => {
        console.log(res, '编辑');
        if (res.success && res.data) {
          this.$set(this.loadingStates, index, false);
          this.formData = res.data
          this.defaultList = []
          this.fileList = []
          if (this.formData.gzzjUrl) {
            this.$set(this, 'fileList', JSON.parse(this.formData.gzzjUrl))
          } else {
            this.fileList = []
          }
          console.log(this.fileList, 'fileList', this.formData.zpUrl)
          this.showFile = true
          // this.formData.isdisabled=0
          if (this.formData.zpUrl) {
            let urlObj = { url: this.formData.zpUrl, name: '' }
            //  this.formData.isdisabled=0
            this.defaultList.push(urlObj)
          }
          this.showImg = true

          this.openModal = true
        } else {
          this.$set(this.loadingStates, index, false);
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
          this.showFile = true
          this.showImg = true


        }
      })
    },
    deleleChange(row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.acp_casePersonnel_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            console.log(res);
            if (res.success) {
              this.on_refresh_table()
            }
          })
        }
      })
    },
    onCancel() {
      this.showFile = false
      this.showImg = false
      this.openModal = false
      this.stopCamera()
    },
    submitClick() {
      this.$refs['releaseForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.saveForm()
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    saveForm() {
      console.log(this.formData, 'this.formData');
      if (this.fileList && this.fileList.length > 0) {
        this.$set(this.formData, 'gzzjUrl', JSON.stringify(this.fileList))
      } else {
        this.$set(this.formData, 'gzzjUrl', '')
      }
      // return
      let url = ''
      if (this.formData.id) {
        url = this.$path.acp_casePersonnel_update
      } else {
        url = this.$path.acp_casePersonnel_create
      }
      this.$store
        .dispatch("authPostRequest", {
          url: url,
          params: this.formData
        }).then(res => {
          console.log(res, 'res');
          if (res.success) {
            this.loading = false
            this.openModal = false
            this.on_refresh_table()
          } else {
            this.$Message.error(res.msg || '保存失败！')
            this.loading = false
          }
        })
    },
    changeStatus(row, val) {
      console.log(row, val);
      this.$store.dispatch('postRequest', {
        url: this.$path.bsp_pam_reportItem_updateStatus,
        params: {
          id: row.id,
          status: row.status,
        }
      }).then(res => {
        if (res.success) {
          this.$Message.success('修改成功!!')

          //   this.on_refresh_table()
        }
      })
    },
    handleOrgIChange(newVal, oldVal) {
      //   console.log(newVal, oldVal,'newVal, oldVal');
      if (!newVal) {
        this.params.orgCode = this.$store.state.common.orgCode
        this.showData = false
      } else {
        this.showData = false
        this.params.orgCode = newVal
      }
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1);
    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.ivu-form-item {
  margin-bottom: 18px !important;
}
</style>
<style>
.bsp_org_sel_box .ivu-modal {
  width: 930px !important;
}
</style>
<style lang='less'>
@import url("~@/view/windowBusiness/Inquiry/Inquirylist/index.less");
</style>
<style lang='less' scoped>
.jgrySelect-info {
  border: 1px solid #dcdee2;
  padding: 16px 0 16px 16px;
  margin: 16px 16px 0 0;
  border-radius: 6px;

  .xm {
    font-size: 18px;
    font-weight: 700;
  }
}

.jgrySelect {
  border: 1px solid #dcdee2;
  height: 180px;
  border-radius: 6px;
  background: #f7fbfd;
  margin-right: 16px;
  text-align: center;
  display: flex;
  cursor: pointer;
  flex-wrap: wrap;
  align-items: self-start;

  .jgrySelect-addIcon {
    width: 110px;
    height: 60px;
    background: #50a6f9;
    border-radius: 6px;
    margin: auto;
  }

  .jgrySelect-text {
    width: 100%;
    margin-top: -20px;
    color: #2d8cf0;
  }
}

.jgrySelect-flex {
  display: flex;
}

.bary-flex {
  display: flex;
  // align-items:center;
  justify-content: space-between;
  z-index: 9999;
  flex-wrap: wrap;
}

.bsp-imgminio-container {
  width: 100% !important;
}

.bary-flex .bsp-imgminio-container .upload-list-container .upload-list-content .content-filecontent .filecontent-top .top-success .text-hidden {
  max-width: 75px !important;
}

.bary-flex .bsp-imgminio-container .upload-list-container .upload-list-content .filecontent-top /deep/ p>span:last-of-type {
  display: none !important;
}

.Inquiry-flex {
  width: 100%;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
}
</style>
