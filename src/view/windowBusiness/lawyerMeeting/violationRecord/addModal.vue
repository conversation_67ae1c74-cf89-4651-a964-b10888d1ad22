<template>
  	  <Modal
		v-model="openModal"
		:mask-closable="false"
		:closable="true"
		class-name="select-sy-modal"
		width="40%"
		:title="modalTitle"
	  >
	  <Form ref="releaseForm" v-if="openModal" :model="formData" :label-width="100" :label-colon="true" style="margin-right: 16px;">
		  <Row>
			  <Col span="24">
				  <FormItem label="律师姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]" style="width: 100%;">
				              <s-dialog  dialogMark='acp:ckywhqlvshirylb' :disabled="false" :readonly="false"
                       v-model="formData.xm" 
                       return="resId:id|resName:xm"
                       @on-select="onSelectXm"
                       @on-clear="onClearXm"
                       v-bind.sync="formData"
                       >
            </s-dialog>
          </FormItem>
			  </Col>
              <Col span="24">
				  <FormItem label="证件号" prop="zjhm"  style="width: 100%;" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]">
					  <Input v-model="formData.zjhm" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
        <Col span="24">
				  <FormItem label="律所单位" prop="lsdw" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]"   style="width: 100%;">
            		<Input v-model="formData.lsdw" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
        <Col span="24">
				<FormItem label="违规情况" prop="violationType" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]"   style="width: 100%;">
                    <s-dicgrid v-model="formData.violationType"   dicName="ZD_WB_WGQK" />                
                </FormItem>
			  </Col>
        <Col span="24">
				  <FormItem label="违规时间" prop="violationTime" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]"   style="width: 100%;">
            		 <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.violationTime" style="width: 100%;"  key="violationTime"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
				  </FormItem>
			  </Col>

			  <Col span="24">
				  <FormItem label="详细说明" prop="description" :rules="[{ trigger: 'blur,change', message: '请输入', required: true }]"  style="width: 100%;">
					  <Input type="textarea" v-model="formData.description" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
           </Row>
	  </Form>
		<div slot="footer">
		  <Button @click="onCancel">取消</Button>
		  <Button type="primary" @click="submitClick" :loading="loading">提交</Button>
		</div>
	  </Modal>
</template>

<script>
import { sImageUploadLocal } from '@/components/upload/image'
import { sDialog } from 'sd-custom-dialog'
  import {formatDateparseTime } from "@/libs/util";

export default {
    components:{sImageUploadLocal,sDialog},
    props:{
        openMadalAffairs:Boolean,
        modalTitle:String,
        curData:Object
    },
   data(){
      return{
        openModal:false,  //this.openMadalAffairs?this.openMadalAffairs:
        formData:{
            isDisabled:'1'
        },
        defaultList:[],
        loading:false,
        showUpload:false
      }
   },
   watch:{
     'openMadalAffairs':{
        handler(n,o){
           this.openModal=n
        },deep:true,immediate:true
     },
     'curData':{
        handler(n,o){
           this.formData=n
           if(!this.formData.violationTime){
               this.$set(this.formData,'violationTime',formatDateparseTime(new Date()))
           }
        },deep:true,immediate:true
     },
   },
   methods:{
        deleteImageUrl(){
                this.$set(this.formData, 'imageUrl', '')
                this.defaultList=[]
        },
        onCancel(){
            this.openModal=false
            this.showUpload=false
            this.$emit('cancal',false)
        },
	  submitClick(){
		  this.$refs['releaseForm'].validate((valid) => {
			  if(valid) {
				  this.loading = true
				  this.saveForm()
			  } else {
				  this.loading = false
				  this.$Message.error('请填写完整!!')
			  }
		  })
	  },
	  saveForm() {
		  console.log(this.formData,'this.formData');
		  let url = ''
		  if(this.formData.id) {
			  url = this.$path.acp_lawyerViolation_update
		  } else{
			  url = this.$path.acp_lawyerViolation_create
		  }
		  this.$store
		  .dispatch("authPostRequest", {
			  url: url, 
			  params:this.formData
		  }).then(res => {
			  console.log(res,'res');
			  if(res.success) {
				  this.loading = false
				  this.openModal = false
                  this.showUpload=false
                  this.$Message.success(res.msg || '保存成功！')
                  this.$emit('cancal',true)
			  }else{
				  this.$Message.error(res.msg || '保存失败！')
				  this.loading = false
			  }
		  })
	    },
        getDis(data){
            console.log(data,'1212')
        },
        getfile(file) {
            console.log(file,'getfile')
            this.$set(this.formData, 'imageUrl', file.url)
            this.defaultList=[file]
            this.$refs['releaseForm'].validateField('imageUrl')
        },
      takePhoto(row,index) {
        // this.curBaryData=row
        // this.curBaryData.index=index
        this.$refs.cameraModal.open({});
      },
      takePhotoItem(imgUrl){
          this.$set( this.formData,'imageUrl',imgUrl)
        //   this.$set( this.formData.casePersonnelList[this.curBaryData.index],'imageUrl',imgUrl)
      },
      onSelectXm(data){
        console.log(data,'data,index,item')
        this.$set(this.formData,'zjhm',data[0].zjhm)
        this.$set(this.formData,'lsdw',data[0].lsdw)
        this.$set(this.formData,'xm',data[0].xm)
        this.$set(this.formData,'lawyerId',data[0].id)
      },
      onClearXm(data,index,item){
        console.log(data,index,item,'data,index,item')
      }
   }
}
</script>

<style lang="less" scoped>
.ImageUrlDe{
   position: absolute;
   right: 0px;
   top: 0;
   color: red;
   cursor: pointer;
}
</style>