<template>
    <div class="monitoring-view">
        <div class="monitoring-view-top flex-wrap">
            <span class="monitoring-title">视频监控</span>
            <p class="monitoring-type">
                <span @click="getTab(index)" v-for="(item, index) in screen" :key="index"
                    :class="[curTab == index ? 'selectText' : '']">{{
                        item.title }}<i class="curDot" v-if="curTab == index"></i></span>
            </p>
        </div>
        <div>
            <div class="monitoring-room-select">
                <div class="select-show" @click="showSelect">
                    <span>{{ selectItem.name }}</span>
                    <Icon :type="!selectModel ? 'md-arrow-dropdown' : 'md-arrow-dropup'" />
                </div>
                <div class="select-list" v-if="selectModel">
                    <div class="item" v-for="(item, index) in selectList" :key="item.code" @click="selectClick(item)">
                        {{ item.name }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            screen: [
                { title: '四屏', num: 4 }, { title: '六屏', num: 6 }, { title: '八屏', num: 8 },
            ],
            curTab: 0,
            selectModel: false,
            selectList: [
                { name: '北京市监管总队', code: '10000' },
                { name: '北京市第二看守所', code: '10001' },
                { name: '北京市第三看守所', code: '10002' },

            ],
            selectItem: { name: '北京市监管总队', code: '10000' },

        }
    },
    methods: {
        getTab(index) {
            console.log(index, 'getTab121221')
            this.$set(this, 'curTab', index)
        },
        showSelect() {
            this.selectModel = !this.selectModel
        },
        selectClick(item) {
            this.selectModel = false
            console.log(item);
            this.selectItem = item
        },
    }
}

</script>
<style scoped lang="less">
.monitoring-view {
    width: 100%;
    height: 100%;
    background-image: url('~@/assets/images/postScreen/leftBorder.png');
    background-size: 100% 100%;
    margin: 0 16px;

    .monitoring-title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: bold;
        font-size: 16px;
        color: #FFFFFF;
        width: 298px;
        height: 36px;
        display: inline-block;
        background-image: url('~@/assets/images/postScreen/monitoring-title.png');
        background-size: 100% 100%;
        text-indent: 40px;
    }
}

.monitoring-type {
    background: #131936;
    border-radius: 0px 4px 4px 0px;
    border: 1px solid #144B6D;
    margin: 6px 16px 0 0;

    span {
        font-family: MicrosoftYaHei, MicrosoftYaHei;
        font-weight: normal;
        font-size: 14px;
        color: #CDEBFE;
        display: inline-block;
        width: 60px;
        height: 28px;
        text-align: center;
        cursor: pointer;
        position: relative;
    }

    .selectText {
        background: #131936;
        border-radius: 4px 0px 0px 4px;
        border: 2px solid #0ABBDD;
        font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
        font-weight: normal;
        font-size: 16px;
        color: #CDFBFE;
        height: 32px;
    }
}

.flex-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

}

.curDot {
    width: 6px;
    height: 6px;
    display: inline-block;
    background: #00EAFF;
    position: absolute;
    right: 3px;
    top: 2px;
    border-radius: 50%;
}

.monitoring-room-select {
    position: relative;

    .select-show {
        width: 435px;
        height: 32px;
        background: #04122C;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid rgba(66, 131, 220, 0.8);
        display: flex;
        padding: 0 15px;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        user-select: none;

        span {
            font-size: 14px;
            color: #CDFBFE;
        }

        i {
            font-size: 28px;
            color: #0CC6E2;
        }
    }

    .select-list {
        position: absolute;
        top: 40px;
        width: 435px;
        background: #061c44;
        border-radius: 4px;
        flex-direction: column;
        display: flex;
        padding: 5px 0;
        user-select: none;
        z-index: 10;

        .item {
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
            font-size: 14px;
            color: #CDFBFE;
            // border-bottom: 1px solid rgba(66, 131, 220, 0.8);
            cursor: pointer;

            &:hover {
                background: rgba(12, 198, 226, 0.2);
            }

            &:last-child {
                border-bottom: none;
            }
        }
    }
}
</style>