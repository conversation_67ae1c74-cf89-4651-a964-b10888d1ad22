<template>
    <div class="screen" id="screen">
        <img src="~@/assets/images/leaderScreen/bgbk.png" alt="" class="bg-img">
        <div class="title">提押岗大屏</div>
        <div class="top">
            <div class="select">
                <div class="select-show" @click="showSelect">
                    <span>{{ selectItem.name }}</span>
                    <Icon :type="!selectModel ? 'md-arrow-dropdown' : 'md-arrow-dropup'" />
                </div>
                <div class="select-list" v-if="selectModel">
                    <div class="item" v-for="(item, index) in selectList" :key="item.code" @click="selectClick(item)">
                        {{ item.name }}</div>
                </div>
            </div>
            <div class="time">{{ dataTime }}</div>
        </div>
        <windowTask />
    </div>
</template>

<script>
import autofit from 'autofit.js'
import windowTask from './windowTask.vue'
export default {
    name: 'PostScreen',
    components: {
        autofit, windowTask
    },

    data() {

        return {
            dataTime: '',
            selectModel: false,
            selectList: [
                { name: '北京市监管总队', code: '10000' },
                { name: '北京市第二看守所', code: '10001' },
                { name: '北京市第三看守所', code: '10002' },

            ],
            selectItem: { name: '北京市监管总队', code: '10000' },
        }

    },
    mounted() {
        this.dataTime = this.getCurrentDateFormatted()
        autofit.init({
            designHeight: 1080,
            designWidth: 1920,
            renderDom: '#screen',
            resize: true,
        })
    },

    methods: {
        goToRouter(name) {
            if (this.$route.name !== name) {
                this.$router.push({ name: name })
            }

        },
        showSelect() {
            this.selectModel = !this.selectModel
        },
        selectClick(item) {
            this.selectModel = false
            console.log(item);
            this.selectItem = item
        },
        getCurrentDateFormatted() {
            const date = new Date();
            const year = date.getFullYear();           // 年：2025
            const month = date.getMonth() + 1;         // 月：6（注意月份从 0 开始）
            const day = date.getDate();                // 日：24
            const weekday = date.getDay();             // 星期几：0（星期日）~ 6（星期六）
            // 将星期几转换为中文
            const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            const weekStr = weekDays[weekday];
            // 拼接格式：2025年6月24日 星期二
            return `${year}年${month}月${day}日 ${weekStr}`;
        }
    },


    created() { },

    computed: {},

}

</script>

<style scoped lang="less">
.tabs {
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    background: url('~@/assets/images/leaderScreen/dzbg.png');
    background-size: 98% 100%;
    height: 176px;
    width: 1100px;

    .btn-box {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: end;
        //  position: relative;
        gap: 128px;

        .btn {
            width: 116px;
            height: 80px;
            text-align: center;
            background: url('~@/assets/images/leaderScreen/btnbg.png');
            background-size: 100% 100%;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: bold;
            font-size: 20px;
            color: #D6F6FF;
            line-height: 50px;
            user-select: none;
            cursor: pointer;
        }

        .btns {
            margin-bottom: 8px;
        }

        .btn-active {
            background: url('~@/assets/images/leaderScreen/btnbg1.png');
            background-size: 100% 100%;
            color: #FFFCF3;
            text-shadow: 0px 3px 10px #F4C345;
        }

    }

}

.screen {
    width: 100%;
    height: 100%;
    background: url('~@/assets/images/leaderScreen/bg.jpg');
    background-size: 100% 100%;

    .bg-img {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;

    }

    .title {
        font-size: 36px;
        height: 41px;
        line-height: 41px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-weight: bold;
        background-image: linear-gradient(to bottom, #FFFFFF 0%, #C0F0FF 68%, #63ADF8 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        margin-top: 30px;
        letter-spacing: 12px;
    }

    .top {
        display: flex;
        justify-content: space-between;
        width: 94%;
        margin: auto;
        padding-top: 50px;
        align-items: center;
        position: relative;
        z-index: 99;

        .time {
            font-size: 16px;
            color: #7186A3;
        }

        .select {
            position: relative;

            .select-show {
                width: 435px;
                height: 32px;
                background: #04122C;
                border-radius: 4px 4px 4px 4px;
                border: 1px solid rgba(66, 131, 220, 0.8);
                display: flex;
                padding: 0 15px;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
                user-select: none;

                span {
                    font-size: 14px;
                    color: #CDFBFE;
                }

                i {
                    font-size: 28px;
                    color: #0CC6E2;
                }
            }

            .select-list {
                position: absolute;
                top: 40px;
                width: 435px;
                background: #061c44;
                border-radius: 4px;
                flex-direction: column;
                display: flex;
                padding: 5px 0;
                user-select: none;
                z-index: 10;

                .item {
                    height: 32px;
                    line-height: 32px;
                    padding: 0 15px;
                    font-size: 14px;
                    color: #CDFBFE;
                    // border-bottom: 1px solid rgba(66, 131, 220, 0.8);
                    cursor: pointer;

                    &:hover {
                        background: rgba(12, 198, 226, 0.2);
                    }

                    &:last-child {
                        border-bottom: none;
                    }
                }
            }
        }

    }
}
</style>
