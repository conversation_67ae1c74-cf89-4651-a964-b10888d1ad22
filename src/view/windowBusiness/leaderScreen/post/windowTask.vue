<template>
    <div class="post-window-wrap">
        <div class="post-window-wrap-tab">
            <div class="post-window-wrap-tab-left flex-wrap">
                <p v-for="(item, index) in tabList" :key="index + 'tabList'" @click="getTab(item)"
                    :class="[curTab == item.id ? 'selectImg' : '']">
                    <span class="title">{{ item.title }}</span><span class="num">{{ item.num }}</span>
                </p>
            </div>
            <div class="post-window-wrap-tab-right flex-wrap">
                <div v-for="(item, index) in tabTypeList" :key="index + 'tabTypeList'"
                    class="post-window-wrap-tab-right-main">
                    <img :src="item.src" />
                    <div>
                        <p class="num">{{ item.num }}</p>
                        <p class="title">{{ item.title }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="post-window-wrap-data-main">
            <div class="post-window-wrap-data-main-left">
                <div class="post-window-wrap-view-data">
                    <Row class="data-main-left-title">
                        <Col span="1">序号</Col>
                        <Col span="3">在押人员</Col>
                        <Col span="3">监室号</Col>
                        <Col span="1">性别</Col>
                        <Col span="4">业务类型</Col>
                        <Col span="2">会见室</Col>
                        <Col span="4">预约会见时间</Col>
                        <Col span="3">单位</Col>
                        <Col span="3">状态</Col>
                    </Row>
                    <div class="data-main-data-content" v-if="siteData && siteData.length > 0">
                        <vue-seamless-scroll :data="siteData" :class-option="classOption" class="seamless-warp">
                            <Row class="meeting-view-data-content-data" v-for="(item, index) in siteData"
                                :key="index + 'site'">
                                <Col span="3">{{ Number(index + 1) }}</Col>
                                <Col span="3">{{ item.familymembername }}</Col>
                                <Col span="6">{{ item.roomname }}</Col>
                                <Col span="6">{{ item.applymeetingtime }}</Col>
                                <Col span="6" class='statusName'><span class='statusName w180 h50'
                                    :class="'stasus' + item.status">{{ item.statusname }}</span></Col>
                            </Row>
                        </vue-seamless-scroll>
                    </div>
                    <div class="data-view-data-content-noData" v-else>
                        <img src="@/assets/images/noMeeting.png" style="width: 400px;margin-top: 10%;" />
                        <p class="noView-data">暂无数据</p>

                    </div>
                </div>
            </div>
            <div class="post-window-wrap-data-main-right">
                <monitoringWindow  />
            </div>
        </div>
    </div>
</template>
<script>
import autofit from 'autofit.js'
import vueSeamlessScroll from 'vue-seamless-scroll'
import monitoringWindow from "@/view/windowBusiness/leaderScreen/monitoringWindow/index.vue"
export default {
    name: '',
    components: { vueSeamlessScroll,monitoringWindow },

    data() {

        return {
            curTab: 'dtc',
            dataTime: '',
            selectModel: false,
            selectList: [
                { name: '北京市监管总队', code: '10000' },
                { name: '北京市第二看守所', code: '10001' },
                { name: '北京市第三看守所', code: '10002' },

            ],
            selectItem: { name: '北京市监管总队', code: '10000' },
            tabList: [
                { title: '待提出', num: 0, id: 'dtc' }, { title: '待提回', num: 0, id: 'dth' },
            ],
            tabTypeList: [
                { title: '提讯', num: 0, src: require('@/assets/images/postScreen/1.png') }, { title: '提解', num: 0, src: require('@/assets/images/postScreen/1.png') }, { title: '律师会见', num: 0, src: require('@/assets/images/postScreen/1.png') }, { title: '家属会见', num: 0, src: require('@/assets/images/postScreen/1.png') }, { title: '使领馆事会见', num: 0, src: require('@/assets/images/postScreen/1.png') },
            ],
            siteData: [],
            tabData: [],
            classOption: {
                limitMoveNum: 6,
                direction: 1,
                step: 0.5
                // singleHeight: 40,
                // waitTime: 1000
            },
        }

    },
    mounted() {
        this.dataTime = this.getCurrentDateFormatted()
        autofit.init({
            designHeight: 1080,
            designWidth: 1920,
            renderDom: '#screen',
            resize: true,
        })
    },

    methods: {
        getTab(item) {
            console.log(item, 'this.curTab=item.idthis.curTab=item.idthis.curTab=item.id')
            this.$set(this, 'curTab', item.id)
        },
        goToRouter(name) {
            if (this.$route.name !== name) {
                this.$router.push({ name: name })
            }

        },
        showSelect() {
            this.selectModel = !this.selectModel
        },
        selectClick(item) {
            this.selectModel = false
            console.log(item);
            this.selectItem = item
        },
        getCurrentDateFormatted() {
            const date = new Date();
            const year = date.getFullYear();           // 年：2025
            const month = date.getMonth() + 1;         // 月：6（注意月份从 0 开始）
            const day = date.getDate();                // 日：24
            const weekday = date.getDay();             // 星期几：0（星期日）~ 6（星期六）
            // 将星期几转换为中文
            const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            const weekStr = weekDays[weekday];
            // 拼接格式：2025年6月24日 星期二
            return `${year}年${month}月${day}日 ${weekStr}`;
        }
    },


    created() { },

    computed: {},

}
</script>
<style lang="less" scoped>
.post-window-wrap {
    width: 100%;
    margin: 60px 0;
    position: relative;
    z-index: 99;

    .post-window-wrap-tab {
        width: 100%;
        display: flex;
        align-items: center;
        margin: 16px 64px;

        .post-window-wrap-tab-left {
            width: 30%;
            margin-right: 30px;

            .selectImg {
                background-image: url('~@/assets/images/postScreen/select.png') !important;

                .num {
                    color: #FFAF3C !important;
                }
            }

            p {
                width: 50%;
                height: 72px;
                display: flex;
                align-items: center;
                padding: 0 32px;
                background-image: url('~@/assets/images/postScreen/border.png');
                background-size: 100% 100%;
                justify-content: space-between;
                margin-right: 40px;
                cursor: pointer;

                .title {
                    font-family: MicrosoftYaHei, MicrosoftYaHei;
                    font-weight: normal;
                    font-size: 18px;
                    color: #FFFFFF;
                }

                .num {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: bold;
                    font-size: 28px;
                    color: #26B8FF;
                }
            }
        }

        .post-window-wrap-tab-right {
            width: 64%;

            .post-window-wrap-tab-right-main {
                width: 18%;
                height: 72px;
                display: flex;
                align-items: center;
                padding: 0 16px;
                background-image: url('~@/assets/images/postScreen/card.png');
                background-size: 100% 100%;
                // justify-content: space-between;
                margin-right: 32px;

                img {
                    width: 56px;
                    height: 56px;
                    margin-right: 16px;
                }

                .title {
                    font-family: MicrosoftYaHei, MicrosoftYaHei;
                    font-weight: normal;
                    font-size: 14px;
                    color: #D5EBFA;
                }

                .num {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: bold;
                    font-size: 22px;
                    color: #00EAFF;
                    line-height: 30px;
                }
            }


        }
    }

    .post-window-wrap-data-main {
        width: 100%;
        display: flex;
        align-items: center;
        margin: 16px 64px;
        height: calc(~'100vh - 140px');

        .post-window-wrap-data-main-left {
            width: 45%;
            height: 100%;
            background-image: url('~@/assets/images/postScreen/leftBorder.png');
            background-size: 100% 100%;
            padding: 16px 12px;
            .data-main-left-title.ivu-row {
                background: rgba(1, 6, 16, 0.3);
                height: 64px;
                line-height: 64px;
                font-family: Microsoft YaHei, Microsoft YaHei;
                font-weight: bold;
                font-size: 16px;
                color: #D6F6FF;
                text-align: center;
            }
        }

        .post-window-wrap-data-main-right {
            width: 48%;
            height: 100%;

        }
    }
}

.flex-wrap {
    display: flex;
    align-items: center;
}

.data-view-data-content-noData {
    text-align: center;

}

.noView-data {
    line-height: 40px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #D6F6FF;
    text-align: center;
}
</style>