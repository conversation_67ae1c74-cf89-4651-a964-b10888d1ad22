<template>
	<div class='Inquiry-wrap'>
      <div class='Inquiry-wrap-left'>
         <!-- <div class="Inquiry-flex"  style="width: 100%;"><p class="detail-title">被监管人员</p><Button v-if="formData.jgrybm" @click='openPrison' type="primary" style="margin: -16px 6px 0 0;">重新选择</Button></div>
         <div class='jgrySelect' @click='openPrison' v-if="!formData.jgrybm">
             <p class='jgrySelect-addIcon'><Icon type="ios-people" color='#fff' size='36' style='position:relative;left:-10px;top:10px;' /><Icon style='position:relative;right:-10px;bottom:-10px;' size='36' type="md-add-circle" color='#fff' /></p>
             <p class='jgrySelect-text'>点击选择在押人员</p>
         </div>
         <div class='jgrySelect-info' v-if="formData.jgrybm"  style="width: 100%;">
             <div class='jgrySelect-flex'>
                <img :src="formData.prison.frontPhoto? http+formData.prison.frontPhoto:defaultImg" style="width: 88px;height:110px;margin-right: 10px;" />
                <div>
                    <p><span class='xm'>{{formData.prison.xm?formData.prison.xm:'-'}}</span>&nbsp;&nbsp;<span  class='xm'>{{formData.prison.roomName}}</span></p>
                    <p><span>证件号码：</span><span>{{formData.prison.zjhm}}</span></p>
                    <p><span>出生日期：</span><span>{{formData.prison.csrq}}</span></p>
                    <p><span>籍  贯：</span><span>{{formData.prison.jgName}}</span></p>
                    <p><span>民  族：</span><span>{{formData.prison.mzName}}</span></p>
                </div>
             </div>
             <div class='jgrySelect-flex' style='margin-top:16px;'>
                <div style='width:10px;padding:16px 26px 0 16px;background:#e6e9f2;border-radius:6px;margin-right:16px'>案件名称</div>
                <div>
                    <p><span>涉嫌罪名：</span><span>{{formData.prison.xszm}}</span></p>
                    <p><span>案件编号：</span><span>{{formData.prison.ajbh}}</span></p>
                    <p><span>诉讼环节：</span><span>{{formData.prison.sshjName}}</span></p>
                    <p><span>入所时间：</span><span>{{formData.prison.rssj}}</span></p>
                    <p><span>关押期限：</span><span>{{formData.prison.gyqx}}</span></p>
                    <p><span>办案单位：</span><span>{{formData.prison.basj}}</span></p>
                </div>
             </div>
         </div> -->
         <personnel-selector v-model="formData.jgrybm" title="被监管人员" placeholder="点击选择在押人员或扫码识别" :show-case-info="true"
           :enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
         <record :jgrybm="formData.jgrybm" style="width: 100%;" />
      </div>
	  <div class="Inquiry-wrap-right">
      <div class='bary-flex' style='margin-top:10px;' > <p class="detail-title ml-16-title">提解人</p><p><Button type="primary" @click='addInvestigators' style="cursor: pointer;"  v-if="formData.casePersonnelList.length==2">添加办案人员</Button></p></div>
      <div class='bary-flex'>
	  <Form :ref="'formBox-'+index" class='form-box' v-for='(item,index) in formData.casePersonnelList' :key="index" :model="item" :label-width="120" :label-colon="true" style="margin-right: 16px;"  :style="{width:formData.casePersonnelList.length==2?'48%':'32%'}">
        <Row style='padding:10px 0;background:#f5f7fa'>
             <Col span="22" style="margin-bottom:0px;text-align:center;">办案人员（{{Number(index+1)}}）</Col>
             <Col span="2" ><Icon size="20" v-if="formData.casePersonnelList.length==3" @click="deleteCasePerson(item,index)" type="ios-close-circle" />&nbsp;&nbsp;&nbsp;</Col>
        </Row>
        <Row>
              <Col span="23" style="margin-bottom:0px">
                <FormItem label="照片采集" prop="zpUrl" >
                  <RadioGroup v-model="item.isdisabled" size="large" @on-change="getRadio">
                    <Radio :disabled="item.id?true:false" :label="0">本地上传</Radio>
                    <Radio :disabled="item.id?true:false" :label="1">拍照上传</Radio>
                  </RadioGroup>
                  <br />
                  <div v-if="item.isdisabled" style="display: flex;">
                       <div v-if="!item.zpUrl" @click="takePhoto(item,index)" style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                             <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
                       </div>
                       <div v-if="item.zpUrl"  style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                             <img :src="item.zpUrl" style="width: 96px; height: 96px;" />
                       </div>
                    <!-- <div style="width: 100%;">
                       摄像头：<Tag color="primary" ghost style="position: relative;top: -0px;left: -4px;padding:0 5px;cursor: pointer;" v-if="item.isdisabled"   :loading="buttenloading"
                      @click="capture()" >采集照片</Tag><video ref='videoElement' style="margin-top: 14px;" id="video"  height="88" autoplay></video>
                      <canvas id="canvas" width="88" height="88" style="display:none;"></canvas>
                      
                    </div>
                    <div  style="position: relative;" ><span style="position: relative;top: 0px;">采集图：</span><img :src="item.zpUrl"   style="width: 88px;height: 88px;margin: 33px 14px 0 0;" /><Icon @click="remove" color="red" size="20" style="position: absolute;right: 11%;top:30px;cursor: pointer;" type="md-close-circle" /></div> -->
                  </div>
                  <Tooltip v-else  max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。" placement="top">
                    <s-ImageUploadLocal
                      v-model="item.zpUrl"
                      :maxSize="5120" ref="imgUpload"
                      :multiple="false"
                      @getfile="(data)=>getfile(data,index)"
                      @removeFile="removeFile"
                      :defaultList="item.defaultList"
                      :maxFiles="1"
                    />

                  </Tooltip>
                </FormItem>
              </Col>
        </Row>
		    <Row>
			  <Col span="23">
				  <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入姓名', required: true }]" style="width: 100%;">
					<!-- <div class='jgrySelect-flex'> <Input :disabled="item.id?true:false" v-model="item.xm" placeholder="请填写" maxlength="" style="width: 50%;"></Input>  -->
            <s-dialog  :dialogMark="globalAppcode+':ckywhqbarylb'"  :disabled="false" :readonly="false"
                       v-model="item.xm" 
                       return="resId:id|resName:xm"
                       @on-select="(data) => onSelectXm(data,index,item)"
                       @on-clear="(data) => onClearXm(data,index,item)"
                       v-bind.sync="item"
                       >
            </s-dialog>
          <!-- </div> -->
				  </FormItem>
			  </Col>
			  <Col span="23">
				<FormItem label="办案单位" prop="badwdm" :rules="[{ trigger: 'blur,change', message: '请添加', required: true,}]" style="width: 100%;">
                 <org-selector ref="org" :disabled="item.id?true:false"
                        v-model="item.badwdm"
                        :text="item.badwmc"
                        :orgType="item.orgType"
                        :multiple="false"
                        return="orgId:orgId|orgName:orgName"
                        @on-select="(data) => onSelect(data,index,item)"
                        @on-clear="onClear"
                        v-bind.sync="item" >
                  </org-selector>               
                </FormItem>
			  </Col>
		    </Row>
			  <Row>
			  <Col span="23">
				  <FormItem label="证件类型" prop="zjlx"  style="width: 100%;">
                    <s-dicgrid v-model="item.zjlx" :disabled="item.id?true:false"  dicName="ZD_ZJLX" />                
				  </FormItem>
			  </Col>
              <Col span="23">
				  <FormItem label="证件号码" prop="zjhm"  style="width: 100%;">
					  <Input v-model="item.zjhm" placeholder="" :disabled="item.id?true:false" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
			  <Col span="23">
				<FormItem label="联系方式" prop="lxfs"  style="width: 100%;">
					  <Input v-model="item.lxfs" placeholder="" maxlength="" :disabled="item.id?true:false" style="width: 100%;"></Input>
                </FormItem>
			  </Col>
			  <Col span="23">
				<FormItem label="性别" prop="xb"  style="width: 100%;">
                    <s-dicgrid v-model="item.xb" :disabled="item.id?true:false"  dicName="ZD_XB" />                
                </FormItem>
			  </Col>
		  </Row>
          <Row>
			  <Col span="23">
				  <FormItem label="警号" prop="jh"  style="width: 100%;">
					  <Input v-model="item.jh" placeholder="" :disabled="item.id?true:false" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
           </Row>
            <Row>
			  <Col span="23">
				<FormItem label="上传工作证件" prop="gzzjUrl"  style="width: 100%;">
                   <file-upload :disabled="item.id?true:false"
                    :defaultList="item.fileList"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    @fileSuccess="fileSuccessFile"
                    v-if="showFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="(data)=>fileCompleteFile(data,index)" />
                </FormItem>
			  </Col>
		  </Row>
	  </Form>
      
      </div>
         <div class="fm-content-form">

          <Form ref="taskForm"  :model="formData" :label-width="140" :label-colon="true" style="margin: 16px 16px 16px 0;border-right: 1px solid rgb(206, 224, 240);;" >
            <p class="fm-content-wrap-title" style='margin-bottom: 16px;padding:10px; border-bottom: 1px solid #cee0f0; background: #eff6ff;'><Icon type="md-list-box" size="24" color="#2b5fda" />业务信息</p>
            <Row>
            <Col span="11">
              <FormItem label="提解时间" prop="applyEscortDate"  style="width: 100%;" :rules="[{ trigger: 'blur,change', message: '请选择提解时间', required: true,  }]">
                <el-date-picker  format='yyyy-MM-dd'  value-format='yyyy-MM-dd'  key="escortDatetj"
                                v-model="formData.applyEscortDate" style="width: 100%;"
                                type="datetime" size='small'
                                placeholder="选择日期时间">
                                </el-date-picker>
              </FormItem>
              
            </Col>
            <Col span="1"> </Col>
            <Col span="11">
              <FormItem label="提解事由" prop="escortReason" :rules="[{ trigger: 'blur,change', message: '请选择提解事由', required: true }]"  style="width: 100%;">
                        <s-dicgrid v-model="formData.escortReason"   dicName="ZD_WB_TJSY" />                
              </FormItem>
            </Col>
            <Col span="11">
                <FormItem label="提解详细事由" prop="detailReason"  style="width: 100%;">
                  <Input v-model="formData.detailReason" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col>
            <Col span="1"> </Col>
            <Col span="11">
                <FormItem label="提解目的地" prop="destination"  style="width: 100%;">
                  <Input v-model="formData.destination" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
            </Col>
            </Row>
            <Row>
              <Col span="11">
                <FormItem label="提解文书号" prop="wsh"  style="width: 100%;">
                  <Input v-model="formData.wsh" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
              </Col><Col span="1"> </Col>
              <Col span="11">
                <FormItem label="备注" prop="remark"  style="width: 100%;">
                  <Input v-model="formData.remark" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
              </Col>
              <Col span="11">
                  <FormItem label="上传提解凭证" prop="evidenceUrl"  style="width: 100%;">
                      <file-upload
                        :defaultList="evidenceUrl"
                        :serviceMark="serviceMark"
                        :bucketName="bucketName"
                        :beforeUpload="beforeUpload"
                        @fileSuccess="fileSuccessFile"
                        v-if="showFile"
                        @fileRemove="fileRemoveFile"
                        @fileComplete="fileCompleteFileCertUrl" />
                    </FormItem>
            </Col>
            
          </Row>
          </Form>
         </div>
      <br />
      <!-- 被监管人员选择组件 -->
      <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360" title="人员列表">
            <div class="select-use">
               <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS"  :isMultiple='false' :selectUseIds="formData.jgrybm"  />
            </div>
            <div slot="footer">
                 <Button  type="primary" @click="useSelect" class="save">确 定</Button>
                 <Button @click="openModal=false" class="save">关 闭</Button>
            </div>
        </Modal>
	  </div>
    <div class='bsp-base-fotter'>
        <Button @click='goBack'>返 回</Button>
        <Button @click="printPageEvent" :loading='printLoading' type="primary">打印进门条</Button>
        <Button @click="saveData" type="primary" :loading='loading'>保 存</Button>
    </div>

    <camera-modal ref="cameraModal" @takePhoto="takePhotoItem" ></camera-modal>

</div>
  </template>
  
  <script>
  import { sDataGrid } from 'sd-data-grid'
  import { mapActions } from 'vuex'
  import { sImageUploadLocal } from '@/components/upload/image'
  import { fileUpload } from 'sd-minio-upfile'
  import { orgSelector} from 'sd-org-selector'
  import { prisonSelect } from "sd-prison-select"
  import { sDialog } from 'sd-custom-dialog'
  import cameraModal from './camera-modal.vue'
  import record from './record.vue'
  import print from "print-js";
  import vueToPdf from "vue-to-pdf";
  import personnelSelector from '@/components/personnel-selector/index.vue'
  Vue.use(vueToPdf);
  import Vue from "vue";
  import { getToken } from "@/libs/util";
  import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";

  export default {
	components: {
	  sDataGrid,sImageUploadLocal,fileUpload,orgSelector,prisonSelect,sDialog,cameraModal,record,personnelSelector
	},
	data() {
	  return {
          globalAppcode:serverConfig.APP_CODE,
          defaultImg: require('@/assets/images/main.png'),
          http: serverConfig.severHttp,
          selectUseIds:'',
          openModal:false,
          showFile:true,
          showImg:false,
          serviceMark: serverConfig.OSS_SERVICE_MARK,
          bucketName: serverConfig.bucketName,
          importUrl: this.$path.upload_fj,
          custom_loading: false,
          defaultList: [],
          fileList:[],
          evidenceUrl:[],
          buttenloading:false,
          stream:null,
          uploadForm:{},
          params:{},
          showData: false,
          modalTitle: '新增办案人员',
          pzData:[],
          formData: {
                isdisabled:0,
                prison:{},
                casePersonnelList:[
                    {isdisabled:0,},
                    {isdisabled:0,}
                ],
                arraignmentReason:'0',
                // evidenceType:'0'
              },
          loading: false,
          saveType: 'add',
          loadingStates: [],
          curBaryData:{},
          formId: '',
          printLoading: false
	  }
	},
	watch: {
	  orgI: {
		handler(newVal, oldVal) {
		//   console.log(newVal, oldVal, 'newVal, oldVal');
		  if(newVal) {
			this.showData = true
		  }
		  
		},
		deep: true,
		immediate: true // 立即执行
	  }
	},
	methods: {
	  ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      deleteCasePerson(item,index){
         this.formData.casePersonnelList.splice(index,1)
      },
      takePhoto(row,index) {
        this.curBaryData=row
        this.curBaryData.index=index
        this.$refs.cameraModal.open({});
      },
      takePhotoItem(imgUrl){
          this.$set( this.curBaryData,'zpUrl',imgUrl)
          this.$set( this.formData.casePersonnelList[this.curBaryData.index],'zpUrl',imgUrl)
      },
      goBack(){
         this.$emit('toback')
      },
      saveData(){
          let arr=[this.$refs.taskForm.validate()]
          if(this.formData.casePersonnelList && this.formData.casePersonnelList.length>0){
              this.formData.casePersonnelList.forEach((item,index)=>{
                // console.log(this.$refs['formBox-'+index],'formBox')
                arr.push(this.$refs['formBox-'+index][0].validate())
              })
          }
        if(!this.formData.jgrybm){
          this.$Message.error('请选择被监管人员！')
          return
        }
        Promise.all(arr).then(data => {
            this.loading=true
            this.saveDataForm()
        })
      },
      saveDataForm(){
          if(this.formData.arraignmentTime && this.formData.arraignmentTime.length>0){
            this.$set(this.formData,'startApplyArraignmentTime',this.formData.arraignmentTime[0])
            this.$set(this.formData,'endApplyArraignmentTime',this.formData.arraignmentTime[1])
          }
          this.$store.dispatch('authPostRequest', {url: this.$path.acp_escort_create, params: this.formData}).then(resp => {
          if (resp.success) {
            this.loading=false
            this.goBack()
            this.$Message.success('提解登记成功')
          } else {
            this.loading=false
            this.$Message.error(resp.msg||'提解登记失败')
          }
        }).catch(err=>{
          this.loading=false
        })
      },

      printPageEvent() {
        if (!this.formData.jgrybm) {
          this.printLoading = true
          this.directPrint()
        } else {
          this.printLoading = true
          this.savePrintPage()
        }
      },
      // 保存并打印进门条--打印空的进门条
      directPrint() {
        let _this = this;
        let formId = '1948268902094082048'
        _this.pldySpin = true;
        let iframeUrl =`${ this.$path.pdf_getPdf }?plugIns=MultipleRowTableRenderPolicy&formId=${formId}&access_token=${getToken()}` + "#toolbar=0";
        printJS({
          printable: iframeUrl,
          type: "pdf",
          header: "打印",
          onLoadingEnd: async () => {
            _this.pldySpin = false;
            this.printLoading = false
            // this.$nextTick(() => {
            //   this.goBack()
            // })
          },
        });
      },
      // 保存并打印进门条--打印带数据的进门条
      savePrintPage() {
        let arr=[this.$refs.taskForm.validate()]
          if(this.formData.casePersonnelList && this.formData.casePersonnelList.length>0){
              this.formData.casePersonnelList.forEach((item,index)=>{
                // console.log(this.$refs['formBox-'+index],'formBox')
                arr.push(this.$refs['formBox-'+index][0].validate())
              })
          }
        if(!this.formData.jgrybm){
          this.$Message.error('请选择被监管人员！')
          return
        }
        Promise.all(arr).then(data => {
            // this.pldySpin=true
            this.saveDataFormPrint()
        })
      },
      saveDataFormPrint() {
        if(this.formData.arraignmentTime && this.formData.arraignmentTime.length>0){
          this.$set(this.formData,'startApplyArraignmentTime',this.formData.arraignmentTime[0])
          this.$set(this.formData,'endApplyArraignmentTime',this.formData.arraignmentTime[1])
        }

        if(this.formData.casePersonnelList && this.formData.casePersonnelList.length > 0){
        const needFields = ['xm', 'badwmc', 'zjlx', 'zjhm', 'badwdm', 'lxfs', 'xb', 'zpurl'];
        const mergedObj = needFields.reduce((acc, field) => {
          acc[field] = [];
          return acc;
        }, {});
        this.formData.casePersonnelList.forEach(item => {
          needFields.forEach(field => {
            const value = item[field];
            if (value !== null && value !== undefined && value !== '') {
              mergedObj[field].push(value);
            }
          });
        });
        
        needFields.forEach(field => {
          mergedObj[field] = mergedObj[field].join(',');
        });
        
        this.formData.casePersonPrintData= mergedObj;
        this.$set(this.formData.casePersonPrintData,'add_user',this.$store.state.common.userName)
        this.$set(this.formData.casePersonPrintData,'add_time',formatDateparseTime(new Date()))
        this.$set(this.formData.casePersonPrintData,'remark',this.formData.remark || '')
        
      }
        this.printPdf()
      },
      printPdf() {
        const url = this.$path.pdf_getPdf;
        const formData = new FormData();
        formData.append('access_token', getToken());
        formData.append('businessData', JSON.stringify(this.formData));
        formData.append('formId', '1948268902094082048');
        formData.append('plugIns', 'MultipleRowTableRenderPolicy');

        fetch(url, {
          method: 'POST',
          body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            const blobUrl = URL.createObjectURL(blob);
            
            printJS({
                printable: blobUrl,
                type: 'pdf',
                header: '打印',
                onLoadingEnd: () => {
                  URL.revokeObjectURL(blobUrl); 
                  this.pldySpin = false;
                  this.printLoading = false
                },
                onError: (error) => {
                  console.error('打印错误:', error);
                  URL.revokeObjectURL(blobUrl);
                  this.pldySpin = false;
                  this.printLoading = false
                }
            });
        })
        .catch(error => {
          console.error('Error:', error);
        });
      },
      openPrison(){
        this.openModal=true
      },
      addInvestigators(){
        console.log(this.formData,'this.formData.')
         let obj={
            isdisabled:0
         }
         this.formData.casePersonnelList.push(obj)
      },
      useSelect(){
        if(this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length  >0){
          this.formData.prison=this.$refs.prisonSelect.checkedUse[0]
          this.formData.jgrybm=this.$refs.prisonSelect.checkedUse[0].jgrybm
          this.openModal=false
        }else{
          this.$Notice.warning({
              title:'提示',
              desc:'请选择人员!'
          })
        }
       },
      // 上传组件回调方法
      handleSuccessImg(res, index) {
        // console.log(res,'1212')
        this.defaultList.push(res.data);
        this.defaultList.forEach(ele=>{
          ele.src=ele.url
          ele.imgSrc=ele.url
        })
      },
      removeItem(e, index) {
        this.$Modal.confirm({
          title: "提示",
          render: (h, params) => {
            return h("div", [
              h(
                "p",
                { style: { marginLeft: "10px", wordBreak: "break-all" } },
                "是否确认删除【" + e.item.filename + "】?"
              ),
            ]);
          },
          loading: true,
          onOk: async () => {
            this.$Modal.remove();
            this.defaultList.splice(e.index, 1);
            this.imgArr.forEach((ele,i)=>{
              if(ele.url==e.url){
                this.defaultList.splice(i, 1);
              }
            })
          },
        });
      },
      onSelectXm(data,index,){
        if(this.formData.casePersonnelList && this.formData.casePersonnelList.length>0){
          const isExist =this.formData.casePersonnelList.some(item =>
              item.id && item.id === data[0].id
              );
                if (!isExist) {
                  this.$set(this.formData.casePersonnelList,[index],data[0])
                  this.$set(this.formData.casePersonnelList[index],'isdisabled',0)
                  this.formData.casePersonnelList[index].defaultList=[{ url: data[0].zpUrl, name: '' }]
                }else{
                  this.$Message.error('该办案人员已选，请重新选择！')
                  return
                }
        }else{
                  this.$set(this.formData.casePersonnelList,[index],data[0])
                  this.$set(this.formData.casePersonnelList[index],'isdisabled',0)
                  this.formData.casePersonnelList[index].defaultList=[{ url: data[0].zpUrl, name: '' }]

        }
      },
      onClearXm(data,index,){
        this.$set(this.formData.casePersonnelList,[index],{})
        this.$set(this.formData.casePersonnelList[index],'isdisabled',0)

      },
      onSelect(data,index,item){
           let badwmc=[]
            data.forEach(ele=>{
              badwmc.push(ele.orgName)
              })
            this.$set(item,'badwmc',badwmc.join(','))
            console.info(data,this.formData,index,item,'onSelect')

      },
      onClear(data){
          console.info(data,this.formData,'onClear')
      //    this.$set(this.formData,'badwmc','')
      },
      beforeUpload(){},
      fileSuccessFile(){},
      fileRemoveFile(){},
      fileCompleteFile(data,index){
          if(data && data.length>0){
            this.$set(this.formData.casePersonnelList[index],'gzzjUrl',JSON.stringify(data))
            this.$set(this.formData.casePersonnelList[index],'fileList',data)
          }
      },
      fileCompleteFileCertUrl(data){
          this.evidenceUrl=data
          this.$set(this.formData,'evidenceUrl',JSON.stringify(data))
      },
      removeFile(file) {
          if(file.length==0){
              this.$set(this.formData, 'zpUrl', '')
              this.defaultList=[]
              }
      },
      remove(){
          this.$set(this.formData, 'zpUrl', '')
          this.defaultList=[]
      },
      async uploadImg(imgSrc) {
      let blob = this.dataURLtoBlob(imgSrc);
      let file = this.blobToFile(blob, "拍照");
      let formData = new FormData();
      formData.append("file", file);
      let res = await this.$store.dispatch('authPostRequest', { url: this.importUrl, params: formData })
      let { status, data } = res;
      if (status === 200) {
          this.formData.zpUrl = data.url;
      } else {
          this.errorModal({ content: "上传失败!" })
      }
      },
      dataURLtoBlob(dataurl) {
      let arr = dataurl.split(",");
      let type = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8str = new Uint8Array(n);
      while (n--) {
          u8str[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8str], { type });
      },
      blobToFile(blob, fileName) {
      blob.lastModifiedDate = new Date();
      blob.name = fileName;
      return blob;
      },
      getfile(file,index) {
      console.log(file,'getfile')
      this.$set(this.formData.casePersonnelList[index], 'zpUrl', file.url)
      this.formData.casePersonnelList[index].defaultList=[file]
      },
      getRadio(value){
        console.log(value,'getRadio')
        console.log(this.formData.isdisabled,this.defaultList,'this.formData.isdisabled')
        if(this.defaultList && this.defaultList.length==0){
          this.$set(this.formData,'zpUrl','')
        }
        if(value){
          // 获取摄像头视频流
          if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
              this.stream=stream
              // this.$refs.videoElement= this.stream;
              const video = document.getElementById('video');
              video.srcObject = this.stream;
              video.play();
            });
          }
        }else{
          this.defaultList=[]
          let faceImageUrl = this.formData.zpUrl
          if (faceImageUrl) {
            let urlObj = { url: faceImageUrl, name: '' }
            this.defaultList.push(urlObj)
          }
          this.stopCamera()          
        }
      },
      stopCamera() {
        // 关闭摄像头
        if (this.stream) {
          const tracks = this.stream.getTracks();
          tracks.forEach(track => track.stop());
          this.$refs.videoElement.srcObject = null;
        }
      },
      capture(){
        this.buttenloading = true;
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const context = canvas.getContext('2d');
        context.drawImage(video, 0, 0, 100, 100);
        // 如果有自定义文件名称，优先使用自定义的文件名称
        let filenames = ''
        if (this.uploadForm.filename != undefined && this.uploadForm.filename != '') {
          filenames = this.uploadForm.filename + '.png'
        } else {
          filenames = this.uploadForm.code + '_' + this.picnum + '.png'
        }
        const base64 = canvas.toDataURL('image/png');//canvas.toDataURL();
        console.log(filenames,'filenames',base64);
        this.uploadImg(base64);
        this.$set(this.formData,'zpUrl',base64)
        this.defaultList=[]
            let faceImageUrl = this.formData.zpUrl
            if (faceImageUrl) {
              let urlObj = { url: faceImageUrl, name: '' }
              this.defaultList.push(urlObj)
            }
        this.buttenloading = false;
      },
      addEvent(row) {
            this.showFile=true
            this.showImg=true
            this.saveType = 'add'
            this.formData= {isdisabled:0,zpUrl:''},
            this.fileList=[]
            this.openModal = true
            this.modalTitle='新增办案人员'
            let faceImageUrl = this.formData.zpUrl
            if (faceImageUrl) {
                  let urlObj = { url: faceImageUrl, name: '' }
                  this.defaultList.push(urlObj)
                  }
      },
      editEvent(index,row,tag) {
        if(tag=='edit'){
        this.modalTitle='编辑办案人员'
        this.saveType = 'edit'
        }else{
        this.modalTitle='查看办案人员'
        this.saveType = 'info'
        }

        this.openModal = false
        console.log(row);
        this.$store.dispatch("authGetRequest", {
          url: this.$path.acp_casePersonnel_get,
          params:{
            id: row.id
          }
        }).then(res => {
          console.log(res,'编辑');
          if(res.success && res.data) {
                    this.$set(this.loadingStates, index, false);
                    this.formData=res.data
                    this.defaultList=[]
                    this.fileList=[]
                    if(this.formData.gzzjUrl){
                      this.$set(this,'fileList',JSON.parse(this.formData.gzzjUrl))
                    }else{
                      this.fileList=[]
                    }
                    console.log(this.fileList,'fileList',this.formData.zpUrl)
                    this.showFile=true
                    // this.formData.isdisabled=0
                    if(this.formData.zpUrl){
                      let urlObj = { url: this.formData.zpUrl, name: '' }
                      //  this.formData.isdisabled=0
                      this.defaultList.push(urlObj)
                    }
                    this.showImg=true

            this.openModal = true
          } else {
            this.$set(this.loadingStates, index, false);
            this.$Modal.error({
              title: '温馨提示',
              content: res.msg || '操作失败'
            })
            this.showFile=true
            this.showImg=true


          }
        })
      },
      deleleChange(row) {
        this.$Modal.confirm({
          title: '温馨提示',
          content: '请确认是否删除？',
          onOk: () => {
            this.$store.dispatch('authGetRequest',{
              url: this.$path.acp_casePersonnel_delete,
              params: {
                ids: row.id
              }
            }).then(res => {
              console.log(res);
              if(res.success) {
                this.on_refresh_table()
              }
            })
          }
        })
      },
      onCancel() {
        this.showFile=false
        this.showImg=false
        this.openModal = false
            this.stopCamera()  
      },
      submitClick(){
        this.$refs['releaseForm'].validate((valid) => {
          if(valid) {
            this.loading = true
            this.saveForm()
          } else {
            this.loading = false
            this.$Message.error('请填写完整!!')
          }
        })
      },
      saveForm() {
        console.log(this.formData,'this.formData');
            if(this.fileList && this.fileList.length>0){
              this.$set(this.formData,'gzzjUrl',JSON.stringify(this.fileList))
              }else{
              this.$set(this.formData,'gzzjUrl','')
              }
          // return
        let url = ''
        if(this.formData.id) {
          url = this.$path.acp_casePersonnel_update
        } else{
          url = this.$path.acp_casePersonnel_create
        }
        this.$store
        .dispatch("authPostRequest", {
          url: url, 
          params:this.formData
        }).then(res => {
          console.log(res,'res');
          if(res.success) {
            this.loading = false
            this.openModal = false
            this.on_refresh_table()
          }else{
            this.$Message.error(res.msg || '保存失败！')
            this.loading = false
          }
        })
      },
      changeStatus(row,val){
        console.log(row,val);
        this.$store.dispatch('postRequest',{
          url: this.$path.bsp_pam_reportItem_updateStatus,
          params: {
            id: row.id,
                    status: row.status,
          }
        }).then(res => {
          if(res.success) {
          this.$Message.success('修改成功!!')

          //   this.on_refresh_table()
          }
        })
      },
      handleOrgIChange(newVal, oldVal) {
      //   console.log(newVal, oldVal,'newVal, oldVal');
        if(!newVal) {
          this.params.orgCode = this.$store.state.common.orgCode
          this.showData = false
        } else {
        this.showData = false
        this.params.orgCode = newVal
        }
      },
      // 处理人员选择变化
      handlePersonnelChange(personnelData, jgrybm) {
        if (personnelData && jgrybm) {
          this.formData.prison = personnelData
          this.formData.jgrybm = jgrybm
          // 自动获取办案人员信息
          // this.loadCasePersonnelInfo(jgrybm)
        } else {
          this.formData.prison = {}
          this.formData.jgrybm = ''
          // 清空办案人员信息
          // this.clearCasePersonnelInfo()
        }
      },
      on_refresh_table() {
        this.$refs.grid.query_grid_data(1);
      },
	},
	mounted() {
      if(!this.formData.applyEscortDate){
            this.$set(this.formData,'applyEscortDate',formatDateparseTime(new Date()))
          }
	}
  }
  </script>
  
  <style  scoped lang="less">
  .ivu-form-item{
    margin-bottom: 10px !important;
  }
 

</style>
<style>
 .bsp_org_sel_box  .ivu-modal{
    width: 930px !important;
  }
</style>
<style lang='less'>
@import url("~@/view/windowBusiness/Inquiry/Inquirylist/index.less");
</style>
<style lang='less' scoped>
.jgrySelect-info{
    border:1px solid #dcdee2;
    padding:16px 0 16px 16px;
    margin:16px 16px 0 0;
    border-radius:6px;
    .xm{
        font-size:18px;
        font-weight:700;
    }
}
.jgrySelect{
    border:1px solid #dcdee2;
    height:180px;
    border-radius:6px;
    background:#f7fbfd;
    margin-right:16px;
    text-align:center;
    display:flex;
    cursor: pointer;
    flex-wrap:wrap;
    align-items: self-start;
    .jgrySelect-addIcon{
         width:110px;
         height:60px;
         background:#50a6f9 ;
         border-radius:6px;
         margin: auto;
    }
    .jgrySelect-text{
        width:100%;
        margin-top:-20px;
        color:#2d8cf0 ;
    }
}
.jgrySelect-flex{
    display:flex;
}
.bary-flex{
    display:flex;
    // align-items:center;
    justify-content: space-between;
    z-index: 9999;
}
.bsp-imgminio-container{
  width:100% !important;
}
     .Inquiry-flex{
        width: 100%;
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: space-between;
     }
</style>
