<template>
    <div class="gsyp-wrap">
        <div>
            <!-- 使用新的人员选择组件 -->
            <personnel-selector v-model="formValidate.jgrybm" mode="detail" title="被监管人员" placeholder="点击选择在押人员或扫码识别"
                :show-case-info="true" :enable-scan="true" :show-scan-tip="true" />
            <record :jgrybm="formValidate.jgrybm" :traceList="formValidate.traceList" />
        </div>
        <div class="gswp-info">
            <Form ref="formValidate" :model="formValidate" :label-width="160" class="detail">
                <p class="section-title title-bg">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />药品信息
                </p>

                <Row v-for="(ele, index) in formValidate.medicineDeliverys" :key="index + 'medicineDeliverys'">
                    <Col span="8">
                    <FormItem label="药品名称" prop="medicineName">
                        <span>{{ ele.medicineName }}</span>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="剂型" prop="dosageFormName">
                        <span>{{ ele.dosageForm ?ele.dosageForm:ele.dosageFormName }}</span>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="规格" prop="specs">
                        <span>{{ ele.specs }}</span>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="生产单位" prop="productUnit">
                        <span>{{ ele.productUnit }}</span>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="批准文号" prop="approvalNum">
                        <span>{{ ele.approvalNum }}</span>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="数量" prop="totalNum">
                        <span>{{ ele.totalNum }}{{ ele.jldwName }}</span>
                    </FormItem>
                    </Col>
                </Row>

                <p class="section-title title-bg">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />业务信息
                </p>
                <Row>
                    <Col span="12">
                    <FormItem label="期望送药日期" prop="expectedStartDate">
                        <span>{{ formValidate.expectedStartDate }}</span>-<span>{{ formValidate.expectedEndDate
                            }}</span>
                    </FormItem>
                    </Col>
                    <Col span="12">
                    <FormItem label="顾送药原因" prop="deliveryReasonName">
                        <span>{{ formValidate.deliveryReasonName }}</span>
                    </FormItem>
                    </Col>
                    <Col span="24">
                    <FormItem label="备注" prop="deliveryRemark">
                        <span>{{ formValidate.deliveryRemark }}</span>
                    </FormItem>
                    </Col>
                </Row>
                <template v-for="(ele, i) in formValidate.traceList">
                    <Row :key="i + 'traceList'">
                        <Col span="12" v-for="(elechild, inx) in ele.nodeInfo" :key="inx + 'nodeInfo' + i">
                        <FormItem :label="elechild.key" prop="doctorName">
                            <span>{{ elechild.val }}</span>
                        </FormItem>
                        </Col>
                    </Row>
                </template>
                <p class="section-title title-bg"
                    v-if="curData.status == '01' && curData.type == 'approve' && formValidate.currentNode">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />{{ formValidate.currentNode.nodeName }}
                </p>
                <Row class="edit-form" v-if="curData.status == '01' && curData.type == 'approve'">
                    <Col span="8">
                    <FormItem label="审批结果" prop="approvalResult">
                        <RadioGroup v-model="formData.approvalResult">
                            <Radio label="1">同意</Radio>
                            <Radio label="0">不同意</Radio>
                        </RadioGroup>
                    </FormItem>
                    </Col>
                    <Col span="16">
                    <FormItem label="审批意见" prop="approvalComments">
                        <Input v-model="formData.approvalComments"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="审批人" prop="approvalUser">
                        <span>{{ formData.approvalUser }}</span>
                    </FormItem>
                    </Col>
                    <Col span="16">
                    <FormItem label="审批时间" prop="approvalTime">
                        <span>{{ formData.approvalTime }}</span>
                    </FormItem>
                    </Col>
                </Row>
                <!-- || curData.status == '005'顾送信息 -->
                 
                <p class="section-title title-bg" v-if="curData.status == '005' ">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />顾送信息
                </p>
                  <Row  v-if="curData.status == '005' ">
                    <Col span="12">
                    <FormItem label="是否与申请信息一致" prop="isConsistent">
                        <span>{{ formValidate.isConsistent?'一致':'不一致' }}</span>
                    </FormItem>
                    </Col>
                    <Col span="12">
                    <FormItem label="不一致原因" prop="reasonForInconsistency">
                        <span >{{ formValidate.reasonForInconsistency }}</span>
                    </FormItem>
                    </Col>
                      <Table  :columns="columnsInfo" :data="formValidate.medicineDeliverys"
                        tooltip-theme="light" :tooltip-max-width="300" border>
                                            <template slot-scope="{ row ,index}" slot="totalNum" v-if="row.medicineName">
                            <div style="width: 100%;display: flex;justify-content: space-evenly;align-items: center;">
                                <InputNumber :min="0" v-model="row.totalNum" disabled
                                    @on-change="(data) => changeItem(row, index, 'totalNum', data)" placeholder="请输入"
                                    type="number" style="width: 40%;"></InputNumber>
                                <Select v-model="row.dw" placement="top-end" @on-change="changeItem(row, index,)"
                                    placeholder="-（计量单位）" style="width: 50%;z-index: 999999;">
                                    <Option v-for="item in row.dwList" :value="item.value" :key="item.value">{{
                                        item.title }}</Option>
                                </Select>
                                <Tooltip placement="top" :content="row.content">
                                    <Icon type="ios-help-circle" size="24" color="#2d8cf0" />
                                </Tooltip>
                            </div>
                        </template>
                    </Table>
                    <Col span="24">
                    <FormItem label="药品图片" prop="importUrl">
                        <uploadList :fileList="formValidate.imgUrl?JSON.parse(formValidate.imgUrl):[]" :photo="true" :importUrl="importUrl"
                            @handleSuccess="(data) => handleSuccess(data, 'imgUrl')" :readonly="true" ></uploadList>
                    </FormItem>
                    </Col>
                    <Col span="12">
                    <FormItem label="送药日期" prop="deliveryDate">
                        <span>{{ formValidate.deliveryDate }}</span>
                    </FormItem>
                    </Col>
                     <Col span="12">
                    <FormItem label="备注" prop="deliveryRemark">
                        <span>{{ formValidate.deliveryRemark }}</span>
                    </FormItem>
                    </Col>
                </Row>
                <p class="f title-bg" v-if="curData.status == '01' && curData.type == 'zz'">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />终止登记信息
                </p>
                <Row class="edit-form" v-if="curData.status == '01' && curData.type == 'zz'">
                    <Col span="24">
                    <FormItem label="原因" prop="exceptionReason">
                        <s-dicgrid placeholder="请选择" style="width: 100%; max-width: 450px; height: 40px"
                            v-model="formDataZz.exceptionReason" ref="dicGrid" dicName="ZD_YPGS_YCYY" />
                    </FormItem>
                    </Col>
                    <Col span="24">
                    <FormItem label="备注" prop="deliveryRemark">
                        <Input type="textarea" v-model="formDataZz.deliveryRemark"></Input>
                    </FormItem>
                    </Col>

                </Row>
                <p class="f title-bg" v-if="curData.type == 'qs'  ">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />顾送药品查验
                </p>
                <Row class="edit-form" v-if="curData.type == 'qs'">
                    <Col span="8">
                    <FormItem label="是否与申请信息一致" prop="isConsistent">
                        <RadioGroup v-model="formValidate.isConsistent">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    </Col>
                    <Col span="16">
                    <FormItem label="不一致原因" v-if="formValidate.isConsistent=='0'"  prop="reasonForInconsistency">
                        <Input v-model="formValidate.reasonForInconsistency"></Input>
                    </FormItem>
                    </Col>
                    <Table v-if="curData.type == 'qs'" :columns="columns" :data="medicineDeliverys"
                        tooltip-theme="light" :tooltip-max-width="300" border>
                        <template slot-scope="{ row,index }" slot="medicineName">
                            <Input v-model="row.medicineName" search enter-button="选择" placeholder="请选择"
                                @on-change="changeItem(row, index, 'medicineName')"
                                @on-search="openYpSelect(row, index)" readonly @on-click="openYpSelect(row, index)" />
                        </template>
                        <template slot-scope="{ row ,index}" slot="approvalNum" v-if="row.medicineName">
                            <Input v-model="row.approvalNum" @on-change="changeItem(row, index, 'approvalNum',)"
                                placeholder="请输入" style="width: 40%;"></Input>
                        </template>

                        <template slot-scope="{ row ,index}" slot="totalNum" v-if="row.medicineName">
                            <div style="width: 100%;display: flex;justify-content: space-evenly;align-items: center;">
                                <InputNumber :min="0" v-model="row.totalNum"
                                    @on-change="(data) => changeItem(row, index, 'totalNum', data)" placeholder="请输入"
                                    type="number" style="width: 40%;"></InputNumber>
                                <Select v-model="row.dw" placement="top-end" @on-change="changeItem(row, index,)"
                                    placeholder="-（计量单位）" style="width: 50%;z-index: 999999;">
                                    <Option v-for="item in row.dwList" :value="item.value" :key="item.value">{{
                                        item.title }}</Option>
                                </Select>
                                <Tooltip placement="top" :content="row.content">
                                    <Icon type="ios-help-circle" size="24" color="#2d8cf0" />
                                </Tooltip>
                            </div>
                        </template>

                        <template slot-scope="{ row,index }" slot="action">
                            <Button type="primary" @click="addTime(row, index)">增行</Button>&nbsp;<Button type="error"
                                @click="deleteTime(row, index)">删除</Button>
                        </template>
                    </Table>
                    <Col span="24">
                    <FormItem label="药品图片" prop="imgUrl">
                        <uploadList :fileList="fileJson" :photo="true" :importUrl="importUrl"
                            @handleSuccess="(data) => handleSuccess(data, 'imgUrl')"></uploadList>
                    </FormItem>
                    </Col>

                </Row>
                <p class="f title-bg" v-if="curData.type == 'qs' ">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />顾送信息
                </p>
                <Row class="edit-form" v-if="curData.type == 'qs'">
                    <Col span="24">
                    <FormItem label="送药日期" prop="deliveryDate">
                        <el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="formValidate.deliveryDate" size="small" placeholder="请选择" />
                    </FormItem>
                    </Col>
                    <Col span="24">
                    <FormItem label="备注" prop="deliveryRemark">
                        <Input type="textarea" v-model="formValidate.deliveryRemark">{{ }}</Input>
                    </FormItem>
                    </Col>

                </Row>
            </Form>
        </div>
        <div class="bsp-base-fotter">
            <Button @click="on_return_table(false)">返 回</Button>
            <Button type="primary" :loading="loading" @click="handleSubmit"
                v-if="(curData.type == 'approve' || curData.type == 'zz' || curData.type == 'qs') && curData.status != '005'">保
                存</Button>
        </div>

        <!-- 药品选择 -->
        <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="" width="1100" title="选择药品">
            <div class="select-use">
                <ypData  v-if="openModal && curData.type != 'qs'" :url="$path.md_medicineCode_page" ref="ypData" />
                <ypSelectgO v-if="openModal && curData.type == 'qs'" :data="formValidate.medicineDeliverys?JSON.parse(JSON.stringify(formValidate.medicineDeliverys)):[]" ref="ypData"  />
            </div>
            <div slot="footer">
                <Button type="primary" @click="ypSelect" class="save">确 定</Button>
                <Button @click="openModal = false" class="save">关 闭</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import { mapActions } from 'vuex'
import uploadList from "@/components/upload/upload-list.vue";
import record from "./record.vue";
import { formatDateparseTime, getUserCache } from "@/libs/util";
import ypData from "@/view/yfgl/ypcg/ypSelect.vue";
import ypSelectgO from './ypSelect.vue';
export default {
    components: { uploadList, record, ypData,ypSelectgO },
    props: {
        curData: Object
    },
    data() {
        return {
            openModal: false,
            formValidate: this.curData ? this.curData : {},
            importUrl: this.$path.upload_fj,
            formData: {
                approvalResult: '1',
                approvalComments: '同意',
                approvalUser: getUserCache.getUserName(),
                approvalTime: formatDateparseTime(new Date())
            },
            formDataZz: {
                deliveryRemark: '',
                exceptionReason: ''
            },
            loading: false,
            columnsInfo: [
                { type: "index", title: "序号", width: 70, align: "center" },
                {
                    title: "*批准文号",
                    key: "approvalNum",
                    align: "center",
                    tooltip: true,
                    width: 240,
                },
                {
                    title: "药品名称*",
                    key: "medicineName",
                    align: "center",
                    // width: 260,
                    tooltip: true,
                },


                {
                    title: "*数量",
                    key: "totalNum",
                    width: 200,
                    align: "center",
                    tooltip: true,
                },

            ],
            columns: [
                { type: "index", title: "序号", width: 70, align: "center" },
                {
                    title: "*批准文号",
                    slot: "approvalNum",
                    align: "center",
                    tooltip: true,
                    width: 240,
                },
                {
                    title: "药品名称*",
                    slot: "medicineName",
                    align: "center",
                    // width: 260,
                    tooltip: true,
                },
                // {
                //   title: "*剂型",
                //   key: "dosageFormName",
                //   align: "center",
                //   width: 150,
                //   tooltip: true,
                // },

                {
                    title: "*数量",
                    slot: "totalNum",
                    width: 200,
                    align: "center",
                    tooltip: true,
                },
                // {
                //     title: "*生产批次",
                //     key: "specs",
                //     align: "center",
                //     width: 160,
                //     tooltip: true,
                // },
                // {
                //     title: "*有效期",
                //     key: "productUnitEn",
                //     align: "center",
                //     width: 180,
                //     tooltip: true,
                // },
                {
                    title: "操作",
                    slot: "action",
                    width: 180,
                    align: "center",
                },
            ],
            medicineDeliverys: [
                {
                    name: "",
                    goodsType: "",
                    quantity: "",
                    unit: "",
                    features: "",
                    storageLocationName: "",
                    photoCount: "",
                    remark: "",
                },
            ],
            curIndex: 0,
            rowData: {},
            fileJson: []
        }
    },
    watch: {
        curData: {
            handler(n, o) {
                console.log(n, 'curDatacurDatacurData')
                if (n.id) {
                    this.getData()
                }
            }, deep: true, immediate: true
        }
    },
    methods: {
        ...mapActions(['authPostRequest', 'authGetRequest', 'getRequest']),
        on_return_table() {
            this.$emit('on_show_table', false)
        },
        changeItem(row, index, file, data) {
            console.log(row, index, file, 'row, index, file', data)
            if (file) {
                //  this.$set(row,file,row.file)
                this.$set(this.medicineDeliverys[index], file, data)
            }
            console.log(this.medicineDeliverys, 'medicineDeliverys')
        },
        openYpSelect(row, index) {
            this.rowData = row;
            this.curIndex = index;
            this.openModal = true;
        },
        deleteTime(item, index) {
            this.medicineDeliverys.splice(index, 1)
        },
        addTime(item, index) {
            this.medicineDeliverys.push({})
        },
        handleSubmit() {
            // 审批
            if (this.curData && this.curData.type == 'approve') {
                this.approvalSumbit()
            } else if (this.curData && this.curData.type == 'zz') {
                this.zzSumbit()
            } else if (this.curData && this.curData.type == 'qs') {
                this.qsSumbit()
            }
        },
        ypSelect(data) {
            console.log(this.$refs.ypData.selectionData[0], ' this.$refs.ypData.selectionData[0]')
            this.$set(this.medicineDeliverys, this.curIndex, this.$refs.ypData.selectionData[0])
            this.$set(this.medicineDeliverys[this.curIndex], 'id', '')
            let dataArr = [
                {
                    title: this.$refs.ypData.selectionData[0].dosageForm + "（计量单位）",  // this.$refs.ypData.selectionData[0].measurementUnitName + "（计量单位）",
                    value: 1
                },
                // {
                //   title: this.filterName(this.$refs.ypData.selectionData[0].minMeasurementUnit) + "（最小计量单位）",  //this.$refs.ypData.selectionData[0].minMeasurementUnitName + "（最小计量单位）",//
                //   value: 2
                // }
            ]
            this.$set(this.medicineDeliverys[this.curIndex], 'dwList', dataArr)
            this.$set(this.medicineDeliverys[this.curIndex], 'dw', 1)
            this.$forceUpdate()
            this.openModal = false;


        },
        approvalSumbit() {
            this.$set(this.formData, 'id', this.curData.id)
            this.authPostRequest({
                url: this.$path.md_medicineDeliveryApply_approve,
                params: this.formData
            }).then(res => {
                // this.loading = false
                if (res.success || res.code === 200) {
                    this.$Message.success('提交成功！')
                    this.on_return_table()
                } else {
                    this.$Message.error(res.msg || '提交失败！')
                }
            }).catch(error => {

            })
        },
        zzSumbit() {
            this.$set(this.formDataZz, 'id', this.curData.id)
            this.authPostRequest({
                url: this.$path.md_medicineDeliveryApply_abnormalRegInfo,
                params: this.formDataZz
            }).then(res => {
                // this.loading = false
                if (res.success || res.code === 200) {
                    this.$Message.success('提交成功！')
                    this.on_return_table()
                } else {
                    this.$Message.error('提交失败！')
                }
            }).catch(error => {

            })
        },
        qsSumbit() {
            this.$set(this.formValidate, 'id', this.curData.id)
            this.$set(this.formValidate, 'medicineDeliverys', this.medicineDeliverys)
            this.$set(this.formValidate, 'imgUrl', this.fileJson && this.fileJson.length > 0 ? JSON.stringify(this.fileJson) : '')
            this.authPostRequest({
                url: this.$path.md_medicineDeliveryApply_regInfo,
                params: this.formValidate
            }).then(res => {
                // this.loading = false
                if (res.success || res.code === 200) {
                    this.$Message.success('提交成功！')
                    this.on_return_table()
                } else {
                    this.$Message.error('提交失败！')
                }
            }).catch(error => {

            })
        },
        // 上传组件回调方法
        handleSuccess(res, file) {
            console.log(res, file, 'res, index')
            this.fileJson.push(res.data);
        },
        getData() {
            this.getRequest({
                url: this.$path.md_medicineDeliveryApply_get,
                params: { id: this.curData.id }
            }).then(res => {
                // this.loading = false
                if (res.success || res.code === 200) {
                    this.formValidate = res.data
                    console.log(this.formValidate.medicineDeliverys,'this.formValidate.medicineDeliverys')
                } else {

                }
            }).catch(error => {

            })
        },

    }
}
</script>
<style scoped lang="less">
.gsyp-wrap {
    width: 100%;
    display: flex;
}

.gswp-info {
    margin-left: 16px;
    width: calc(~'100% - 420px');
    margin-bottom: 50px;
}

.section-title {
    // border-left: 4px solid #2d8cf0;
    padding-left: 8px;
    font-size: 16px;
    font-weight: 700;
    height: 20px;
    line-height: 20px;
    position: relative;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title:nth-of-type(n+1) {
    margin-top: 10px;
}

.detail /deep/ .ivu-form-item-label {
    background: #eff5fc;
    border-right: 2px solid #fff;
}

.detail .edit-form /deep/ .ivu-form-item-label {
    background: #fff;
    border-right: 2px solid #fff;
}

.detail /deep/ .ivu-form-item-content {
    background: #f4f7ff;
    min-height: 36px;
}

.detail /deep/ .ivu-form-item {
    margin-bottom: 2px;
}

.detail .edit-form /deep/ .ivu-form-item-content {
    background: #fff !important;
    min-height: 36px;
}

.edit-form {
    border: 1px solid #cee0f0;
    border-top: none;
    padding: 16px 0;
}

.title-bg {
    border: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px !important;
    height: 40px !important;
    padding-left: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 16px;
    color: #00244a;
    margin-bottom: 0px !important;
    margin-top: 0px !important;
    display: flex;
    align-items: center;
}
</style>