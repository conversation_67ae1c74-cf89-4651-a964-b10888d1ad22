<template>
  <div class="main-view">

    <portalComponents ref="portalComponents" :mrPage="mrPage" :appId="appId"
      :handleManage="this.$store.state.app.manageTag" :handleShow="handleShow" :portalCatId="portalCatId"
      :componentLists="componentLists" :token="token" :paramsData="paramsData" @cancelSelect="cancelSelect"
      @confirmSelectPortlets="confirmSelectPortlets" @submitDel="submitDel" @handleMr="handleMr"
      @cancalHandle="cancalHandle" @saveHandle="saveHandle" />


  </div>
</template>

<script>
import Cookies from "js-cookie";
import { getUserCache } from "@/libs/util";
import { portalComponents } from "sd-portal-components";
import { mapMutations } from "vuex";
export default {
  components: {
    portalComponents,
  },
  data() {
    return {
      openModal: false,
      paramsData: {
        name: "ceshi",
        num: 12
      }, //参数
      componentLists: ["sjzl", "fbqk", "yxmj", "tjfs", "jbfb", "application", 'messageNotification', 'zdgz',], //通过组件方式引入组件需配置组件名传入   微前端方式开发的组件则只需要在bsp中配置即可
      appId: serverConfig.APP_ID, //应用id
      portalCatId: "shmdProtal", //布局id
      mrPage: "1", //用户自定义门户
      handleManage: this.$store.state.app.manageTag, //是否可以拖拽门户
      handleShow: false, //是否显示操作按钮
      token: Cookies.get("icp_access_token") //'83da8007-de36-43c1-99e1-d760d755a2b5'
    };
  },
  mounted() {
  },
  methods: {
    ...mapMutations(["setManageTag"]),
    // 添加组件的取消按钮
    cancelSelect() { },
    confirmSelectPortlets(data) {
      //console.log(data, "datad");
      //   this.$refs.portalComponents.handle()
    },
    // 删除组件
    submitDel(item, index) {
      // //console.log(item, index)
    },
    // 恢复默认
    handleMr() { this.setManageTag(true) },
    // 取消操作
    cancalHandle() { this.setManageTag(true) },
    // 保存
    saveHandle(params, layoutData) {
      this.setManageTag(true)
    },
    // useSelect(data){
    //    if(this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length>0){
    //        //console.log(this.$refs.prisonSelect.checkedUse,'this.$refs.prisonSelect.checkedUse')
    //        this.openModal=false
    //    }else{
    //         this.$Notice.warning({
    //               title:'提示',
    //               desc:'请选择人员!'
    //         })
    //    }
    // }
  }
};
</script>

<style scoped lang="less">
.main-view {
  background: #E9EEF5;
  width: 100%;
  height: 100%;
  overflow-y: overlay !important;
}

/deep/ .idContain {
  overflow: hidden !important;
  border: 2px solid #fff !important;
  border-radius: 6px;

  &:hover {
    border: 2px solid blue !important;
  }
}

.hmPageManage /deep/ .contentMain {
  height: 100% !important;
}

/deep/ .contentMain {
  width: 100%;
  height: 100% !important;
  overflow-x: hidden;
  overflow-y: auto;
}

.hmPageManage /deep/ .btns {
  padding-bottom: 10px !important;
  background: #fff !important;
}
</style>
