user  root;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;
    server {
        listen       8899;
        server_name  localhost;
	    client_max_body_size 1024M;
        #charset koi8-r;
        set_real_ip_from 127.0.0.1;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;
        #access_log  logs/host.access.log  main;

        location / {
            root  /usr/share/nginx/html/acp-web;
            index  index.html;
        }
        location /fm {
            alias  /usr/share/nginx/html/acp-web;
            index  fm.html;
        }

        location /acp-com {
            proxy_pass   http://*************:9100/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        location /ihc-com {
            proxy_pass   http://*************:9200/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

         location /tem-com {
            proxy_pass   http://*************:9300/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }

        location /pam-com {
            proxy_pass   http://*************:9600/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        location /zhjg-adapter {
            proxy_pass   http://*************:9111/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        location /bsp-uac {
            proxy_pass   http://*************:1910/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }

        location /bsp-ext {
            proxy_pass   http://*************:1912/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_hide_header X-Frame-Options;
        }

        location /bsp-com {
            proxy_pass   http://*************:1910/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /bsp-ops {
            proxy_pass   http://*************:1910/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /bsp-msg {
            proxy_pass   http://*************:1910/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
         location /bsp-bpm {
            proxy_pass   http://*************:1911/;
            proxy_next_upstream http_500 http_502 error timeout;
            proxy_set_header HOST $host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Kss-Upstream $upstream_addr;
            add_header serverURL $upstream_addr;
            add_header serverURLCode $upstream_status;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
         location /ptm-com {
                    proxy_pass   http://*************:9400/;
                    proxy_next_upstream http_500 http_502 error timeout;
                    proxy_set_header HOST $host;
                    proxy_set_header X-Real-IP $http_x_real_ip;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header Kss-Upstream $upstream_addr;
                    add_header serverURL $upstream_addr;
                    add_header serverURLCode $upstream_status;
                    proxy_http_version 1.1;
                    proxy_set_header Connection "";
                }
                 location /dam-com {
                    proxy_pass   http://*************:9500/;
                    proxy_next_upstream http_500 http_502 error timeout;
                    proxy_set_header HOST $host;
                    proxy_set_header X-Real-IP $http_x_real_ip;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header Kss-Upstream $upstream_addr;
                    add_header serverURL $upstream_addr;
                    add_header serverURLCode $upstream_status;
                    proxy_http_version 1.1;
                    proxy_set_header Connection "";
                }
                location /dms-convert-com{
                    proxy_pass   http://*************:9510/;
                    proxy_next_upstream http_500 http_502 error timeout;
                    proxy_set_header HOST $host;
                    proxy_set_header X-Real-IP $http_x_real_ip;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header Kss-Upstream $upstream_addr;
                    add_header serverURL $upstream_addr;
                    add_header serverURLCode $upstream_status;
                    proxy_http_version 1.1;
                    proxy_set_header Connection "";
                }
        location /dynamic-proxy/(.*)$ {
            proxy_pass $1;
            proxy_set_header Host $host;
            proxy_http_version 1.1;
            # 此处同样需要添加上述CORS头
            proxy_hide_header X-Frame-Options;
            # 可选：添加CORS头（解决跨域）
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        }
        location ~* ^/proxy-image/([a-zA-Z]+:/)(.*)$ {
            proxy_pass $1/$2;
            proxy_set_header Host $host;
            proxy_http_version 1.1;
            proxy_hide_header X-Frame-Options;
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        }
         location ^~ /api/zhjg-prison-room-terminal/ {
                    rewrite ^/api/zhjg-prison-room-terminal/(.*) /$1 break;
                    proxy_pass   http://*************:9111;
                    proxy_next_upstream http_500 http_502 error timeout;
                    proxy_set_header HOST $host;
                    proxy_set_header X-Real-IP $http_x_real_ip;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header Kss-Upstream $upstream_addr;
                    add_header serverURL $upstream_addr;
                    add_header serverURLCode $upstream_status;
                    proxy_http_version 1.1;
                    proxy_set_header Connection "";
                    proxy_hide_header X-Frame-Options;
                }
            location ^~ /api/zhjg-basic-business/ {
                    rewrite ^/api/zhjg-basic-business/(.*) /$1 break;
                    proxy_pass   http://*************:9111;
                    proxy_next_upstream http_500 http_502 error timeout;
                    proxy_set_header HOST $host;
                    proxy_set_header X-Real-IP $http_x_real_ip;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header Kss-Upstream $upstream_addr;
                    add_header serverURL $upstream_addr;
                    add_header serverURLCode $upstream_status;
                    proxy_http_version 1.1;
                    proxy_set_header Connection "";
                    proxy_hide_header X-Frame-Options;
                }
     }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}

